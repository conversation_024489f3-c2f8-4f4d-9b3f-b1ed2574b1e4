import mongoose from "mongoose";
import { EventEmitter } from "node:events";

// Create a connection events emitter that can be used across the project
export const connectionEvents = new EventEmitter();

// Connection status constants
export const CONNECTION_STATES = {
	DISCONNECTED: "disconnected",
	CONNECTED: "connected",
	CONNECTING: "connecting",
	ERROR: "error",
};

// Singleton to track connection state
class MongoConnectionManager {
	// Explicitly declare properties
	private _isConnected: boolean;
	private _connectionState: string;
	private _uri: string | null;

	constructor() {
		this._isConnected = false;
		this._connectionState = CONNECTION_STATES.DISCONNECTED;
		this._uri = null;
	}

	get isConnected(): boolean {
		return this._isConnected;
	}

	get connectionState(): string {
		return this._connectionState;
	}

	get uri(): string | null {
		return this._uri;
	}

	setConnected(state: boolean): void {
		const previousState = this._isConnected;
		this._isConnected = state;

		if (previousState !== state) {
			connectionEvents.emit("connectionStateChanged", {
				connected: state,
				state: state
					? CONNECTION_STATES.CONNECTED
					: CONNECTION_STATES.DISCONNECTED,
			});
		}
	}

	setConnectionState(state: string): void {
		const previousState = this._connectionState;
		this._connectionState = state;

		if (previousState !== state) {
			connectionEvents.emit("connectionStateChanged", {
				connected: this._isConnected,
				state: state,
			});
		}
	}

	setUri(uri: string): void {
		this._uri = uri;
	}
}

// Create a singleton instance
export const mongoManager = new MongoConnectionManager();

// Register the Mongoose connection events before any connection attempt
// This ensures events are captured regardless of when connection happens
const setupMongooseEvents = () => {
	// Remove any existing listeners to prevent duplicates
	mongoose.connection.removeAllListeners();

	mongoose.connection.on("connected", () => {
		console.log(
			"🌟 Event fired: Connection to MongoDB established successfully",
		);
		mongoManager.setConnected(true);
		mongoManager.setConnectionState(CONNECTION_STATES.CONNECTED);
	});

	mongoose.connection.on("error", (err) => {
		console.error("❌ Event fired: MongoDB connection error:", err);
		mongoManager.setConnected(false);
		mongoManager.setConnectionState(CONNECTION_STATES.ERROR);
		connectionEvents.emit("connectionError", err);
	});

	mongoose.connection.on("disconnected", () => {
		console.log("🔌 Event fired: MongoDB disconnected");
		mongoManager.setConnected(false);
		mongoManager.setConnectionState(CONNECTION_STATES.DISCONNECTED);
	});

	// Add additional debug events
	mongoose.connection.on("reconnected", () => {
		console.log("🔄 Event fired: MongoDB reconnected");
		mongoManager.setConnected(true);
		mongoManager.setConnectionState(CONNECTION_STATES.CONNECTED);
	});
};

// Set up the events immediately
setupMongooseEvents();

/**
 * Initialize MongoDB connection
 * @param uri Optional MongoDB connection URI
 * @returns Promise that resolves when connected
 */
export const initializeConnection = async (uri?: string): Promise<void> => {
	// If already connected, don't connect again
	if (mongoManager.isConnected && mongoose.connection.readyState === 1) {
		console.log("🔄 Using existing MongoDB connection");
		return;
	}

	try {
		// Ensure events are set up (redundant, but ensures events are registered)
		setupMongooseEvents();

		// Use provided URI or fallback to environment variable or default
		const mongoDBURI =
			uri || process.env.MONGO_URI || "mongodb://localhost:27017";
		mongoManager.setUri(mongoDBURI);
		mongoManager.setConnectionState(CONNECTION_STATES.CONNECTING);
		console.log("🔌 Attempting to connect to MongoDB...");

		// Set up connection options for better diagnostics
		const options = {
			serverSelectionTimeoutMS: 5000, // Timeout for server selection
			connectTimeoutMS: 10000, // Timeout for initial connection
			socketTimeoutMS: 45000, // How long sockets stay idle before timing out
			heartbeatFrequencyMS: 10000, // How often to check server status
			maxPoolSize: 10, // Maximum number of connections in the pool
		};

		// Connect to MongoDB
		await mongoose.connect(mongoDBURI, options);

		// Check connection state immediately after connect resolves
		const readyState = mongoose.connection.readyState;

		// Manual update if the connection is already established but event didn't fire
		if (readyState === 1 && !mongoManager.isConnected) {
			console.log(
				"🔔 Connection established, but event didn't fire. Manually updating state.",
			);
			mongoManager.setConnected(true);
			mongoManager.setConnectionState(CONNECTION_STATES.CONNECTED);
		}

		// Handle process termination
		process.on("SIGINT", async () => {
			console.log("Received SIGINT signal, closing MongoDB connection...");
			await mongoose.connection.close();
			console.log("MongoDB connection closed due to application termination");
			mongoManager.setConnected(false);
			mongoManager.setConnectionState(CONNECTION_STATES.DISCONNECTED);
			process.exit(0);
		});

		console.log("✅ MongoDB connection initialization complete");
	} catch (error) {
		console.error("❌ Failed to connect to MongoDB:", error);
		mongoManager.setConnected(false);
		mongoManager.setConnectionState(CONNECTION_STATES.ERROR);
		connectionEvents.emit("connectionError", error);
		throw error;
	}
};

/**
 * Get MongoDB native connection
 * @returns MongoDB native connection instance
 * @throws Error if not connected
 */
export const getDB = (): mongoose.mongo.Db => {
	const readyState = mongoose.connection.readyState;

	if (readyState !== 1) {
		console.error(
			`Attempted to access DB when connection readyState is ${readyState}`,
		);
		throw new Error(
			`MongoDB is not connected (state: ${readyState}). Call initializeConnection() first.`,
		);
	}

	if (!mongoose.connection.db) {
		throw new Error(
			"MongoDB connection exists but database reference is missing.",
		);
	}

	return mongoose.connection.db;
};

/**
 * Check if MongoDB is connected
 * @returns Boolean indicating connection status
 */
export const isConnected = (): boolean => {
	const readyState = mongoose.connection.readyState === 1;
	// If mongoose shows connected but our manager doesn't, update the manager
	if (readyState && !mongoManager.isConnected) {
		mongoManager.setConnected(true);
		mongoManager.setConnectionState(CONNECTION_STATES.CONNECTED);
	}
	return readyState;
};

/**
 * Close MongoDB connection
 * @returns Promise that resolves when disconnected
 */
export const closeConnection = async (): Promise<void> => {
	if (mongoose.connection.readyState !== 0) {
		await mongoose.connection.close();
		mongoManager.setConnected(false);
		mongoManager.setConnectionState(CONNECTION_STATES.DISCONNECTED);
		console.log("MongoDB connection closed");
	}
};

// Export mongoose instance as default export
export default mongoose;

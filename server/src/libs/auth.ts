import { betterAuth } from "better-auth";
import { admin as adminPlugin, customSession } from "better-auth/plugins"
import type { Db } from "mongodb";
import { ObjectId } from "mongodb";
import { mongodbAdapter } from "better-auth/adapters/mongodb";
import { createAccessControl } from "better-auth/plugins/access";

export const statement = {
	apiKey: ["create", "read", "update", "delete", "admin"],
} as const;

const ac = createAccessControl(statement);

const whiteListed = ac.newRole({
	apiKey: ["create", "read", "update", "delete"],
})

const user = ac.newRole({
	apiKey: [],
});

const admin = ac.newRole({
	apiKey: ["create", "read", "update", "delete", "admin"],
});

// Create a function that returns the auth configuration to properly infer types
const createAuthConfig = (db: Db) => ({
	database: mongodbAdapter(db),
	secret: process.env.BETTER_AUTH_SECRET || "super secure secret",
	emailAndPassword: {
		enabled: true, // If you want to use email and password auth
	},

	trustedOrigins: ["https://home-server.kvmanas.com"],
	plugins: [adminPlugin({
		ac,
		adminRoles: ["admin"],
		defaultRole: "user",
		roles: {
			whiteListed,
			user,
			admin,
		},
	}),
	customSession(async ({ user, session }) => {
		const role = await findUserRole(db, session.userId);
		return {
			role,
			user,
			session
		};
	}),
	]
});

// Type for the auth instance with admin plugin
type AuthInstanceWithAdmin = ReturnType<typeof betterAuth<ReturnType<typeof createAuthConfig>>>;

class AuthManager {
	private static instance: AuthManager | null = null;
	private authInstance: AuthInstanceWithAdmin | null = null;
	private initialized = false;

	private constructor() {
		// Private constructor to prevent direct construction calls with 'new'
	}

	/**
	 * Get the singleton instance of AuthManager
	 */
	public static getInstance(): AuthManager {
		if (!AuthManager.instance) {
			AuthManager.instance = new AuthManager();
		}
		return AuthManager.instance;
	}

	/**
	 * Initialize the auth instance with database connection
	 * @param db MongoDB database instance
	 * @returns The initialized auth instance
	 */
	public initialize(db: Db): AuthInstanceWithAdmin {
		if (this.initialized && this.authInstance) {
			console.log("🔄 Using existing auth instance");
			return this.authInstance;
		}

		console.log("🔐 Initializing auth instance...");
		this.authInstance = betterAuth(createAuthConfig(db));

		this.initialized = true;
		console.log("✅ Auth instance initialized successfully");

		// Initialize admin user
		this.initializeAdmin(db);

		return this.authInstance;
	}

	/**
	 * Get the auth instance, throws error if not initialized
	 */
	public getAuth(): AuthInstanceWithAdmin {
		if (!this.initialized || !this.authInstance) {
			throw new Error(
				"Auth is not initialized. Call initialize() with database instance first.",
			);
		}
		return this.authInstance;
	}

	/**
	 * Add Admin User
	 */
	public async initializeAdmin(db: Db) {
		if (!this.initialized || !this.authInstance) {
			throw new Error(
				"Auth is not initialized. Call initialize() with database instance first.",
			);
		}

		// check admin exist of db
		const adminUser = await db.collection("user").findOne({
			email: process.env.ADMIN_EMAIL,
		});

		if (adminUser) {
			console.log("✅ Admin user already exists");

			if (adminUser.role !== "admin") {
				await db.collection("user").updateOne(
					{ _id: adminUser._id },
					{ $set: { role: "admin", emailVerified: true } },
				);
				console.log("✅ Admin user role updated to admin");
			}
			return;
		}

		const newAdmin = await this.authInstance.api.signUpEmail({
			body: {
				email: process.env.ADMIN_EMAIL as string,
				password: process.env.ADMIN_PASSWORD as string,
				name: process.env.ADMIN_NAME as string,
				role: "user",
			},
		});

		console.log("✅ Admin user created with id:", newAdmin.user.id);

		// update role to admin
		await db.collection("user").updateOne(
			{ _id: new ObjectId(newAdmin.user.id) },
			{ $set: { role: "admin", emailVerified: true } },
		);

		console.log("✅ Admin user role updated to admin");
	}

	/**
	 * Check if auth is initialized
	 */
	public isInitialized(): boolean {
		return this.initialized;
	}

	/**
	 * Reset the auth instance (useful for testing)
	 */
	public reset(): void {
		this.authInstance = null;
		this.initialized = false;
	}
}

// Export a convenient function to get the auth instance
export const getAuthManager = (): AuthManager => {
	return AuthManager.getInstance();
};

// Export a function to initialize auth
export const initializeAuth = (db: Db): AuthInstanceWithAdmin => {
	return getAuthManager().initialize(db);
};

// Export a function to get the auth instance
export const getAuth = (): AuthInstanceWithAdmin => {
	return getAuthManager().getAuth();
};

export const findUserRole = async (db: Db, userId: string): Promise<string> => {
	const user = await db.collection("user").findOne({ _id: new ObjectId(userId) });
	if (!user) {
		return "user"; // default role if user not found
	}
	return user.role; // return user's role or default to "user"
}


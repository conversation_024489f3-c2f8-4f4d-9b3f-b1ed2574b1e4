// import { Context } from "elysia";

// export class OllamaProxyService {

//     proxy = async (context: Context) => {
//         const apiKey = context.params.apikey;
//         const startTime = Date.now();
//         const endpoint = context.path.replace('/proxy', '/ollama');
//         const method = context.request.method;

//         let requestBody = null;
//         let responseBody = null;
//         let model = null;

//         try {
//             // Parse request body for non-GET requests
//             if (method !== 'GET') {
//                 requestBody = await context.body;

//                 // Try to extract model name from various request formats
//                 if (requestBody) {
//                     model = requestBody.model ||
//                         (requestBody.messages && 'chat format') ||
//                         (requestBody.prompt && 'prompt format');
//                 }
//             }

//             // Build request to Ollama
//             const ollamaUrl = `http://localhost:11434${endpoint}`;

//             const options = {
//                 method,
//                 headers: {
//                     'Content-Type': 'application/json',
//                 }
//             };

//             if (requestBody) {
//                 options.body = JSON.stringify(requestBody);
//             }

//             // Forward request to Ollama
//             const response = await fetch(ollamaUrl, options);

//             if (!response.ok) {
//                 const errorText = await response.text();
//                 throw new Error(`Ollama API error: ${response.status} - ${errorText}`);
//             }

//             // Handle streaming responses specially
//             const isStream = requestBody?.stream === true ||
//                 endpoint.includes('/stream') ||
//                 response.headers.get('content-type')?.includes('text/event-stream');

//             if (isStream) {
//                 // For streaming responses, collect the full response while streaming
//                 const reader = response.body.getReader();
//                 const encoder = new TextEncoder();
//                 const decoder = new TextDecoder();
//                 let fullResponseText = '';

//                 const stream = new ReadableStream({
//                     async start(controller) {
//                         try {
//                             while (true) {
//                                 const { done, value } = await reader.read();
//                                 if (done) break;

//                                 const chunk = decoder.decode(value);
//                                 fullResponseText += chunk;

//                                 controller.enqueue(value);
//                             }

//                             controller.close();

//                             // Log the complete stream response to MongoDB
//                             const duration = Date.now() - startTime;
//                             await logsCollection.insertOne({
//                                 timestamp: new Date(),
//                                 endpoint,
//                                 method,
//                                 model,
//                                 request: requestBody,
//                                 response: fullResponseText,
//                                 duration,
//                                 streaming: true,
//                                 ip: context.request.headers.get('x-forwarded-for') ||
//                                     context.request.headers.get('cf-connecting-ip') ||
//                                     'unknown',
//                                 userAgent: context.request.headers.get('user-agent') || 'unknown'
//                             });

//                         } catch (error) {
//                             controller.error(error);
//                         }
//                     }
//                 });

//                 return new Response(stream, {
//                     headers: {
//                         'Content-Type': response.headers.get('Content-Type') || 'text/event-stream',
//                         'Cache-Control': 'no-cache',
//                         'Connection': 'keep-alive'
//                     }
//                 });
//             } else {
//                 // For non-streaming responses, get the response data
//                 const contentType = response.headers.get('Content-Type');

//                 if (contentType && contentType.includes('application/json')) {
//                     responseBody = await response.json();
//                 } else {
//                     responseBody = await response.text();
//                 }

//                 // Log to MongoDB
//                 const duration = Date.now() - startTime;
//                 await logsCollection.insertOne({
//                     timestamp: new Date(),
//                     endpoint,
//                     method,
//                     model,
//                     request: requestBody,
//                     response: responseBody,
//                     duration,
//                     streaming: false,
//                     ip: context.request.headers.get('x-forwarded-for') ||
//                         context.request.headers.get('cf-connecting-ip') ||
//                         'unknown',
//                     userAgent: context.request.headers.get('user-agent') || 'unknown'
//                 });

//                 return responseBody;
//             }
//         } catch (error) {
//             console.error('Proxy error:', error);

//             // Log errors too
//             await logsCollection.insertOne({
//                 timestamp: new Date(),
//                 endpoint,
//                 method,
//                 model,
//                 request: requestBody,
//                 error: error.message,
//                 duration: Date.now() - startTime,
//                 ip: context.request.headers.get('x-forwarded-for') ||
//                     context.request.headers.get('cf-connecting-ip') ||
//                     'unknown',
//                 userAgent: context.request.headers.get('user-agent') || 'unknown'
//             });

//             context.set.status = 500;
//             return {
//                 success: false,
//                 error: error.message || 'Failed to proxy request to Ollama'
//             };
//         }
//     }

// }
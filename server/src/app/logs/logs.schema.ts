import mongoose from "mongoose";
import type { Document } from "mongoose";
import { Schema } from "mongoose";

export interface IApiLog extends Document {
	method: string;
	path: string;
	query: Record<string, unknown>;
	headers: Record<string, unknown>;
	body: unknown;
	responseStatus: number;
	responseTime: number;
	ip: string;
	timestamp: Date;
}

const ApiLogSchema = new Schema<IApiLog>({
	method: { type: String, required: true },
	path: { type: String, required: true },
	query: { type: Schema.Types.Mixed, default: {} },
	headers: { type: Schema.Types.Mixed, default: {} },
	body: { type: Schema.Types.Mixed, default: {} },
	responseStatus: { type: Number, required: true },
	responseTime: { type: Number, required: true },
	ip: { type: String, default: "" },
	timestamp: { type: Date, default: Date.now },
});

// Create indexes for better query performance
ApiLogSchema.index({ path: 1, timestamp: -1 });
ApiLogSchema.index({ method: 1 });
ApiLogSchema.index({ responseStatus: 1 });

export const ApiLog = mongoose.model<IApiLog>("ApiLog", ApiLogSchema);

import { initializeAuth } from "@libs/auth";
import { Elysia } from "elysia";
import type { Db } from "mongodb";

export const authMiddleware = (db: Db) => {
	const auth = initializeAuth(db);
	return new Elysia<string>({ name: "better-auth" })
		.mount(auth.handler)
		.macro({
			auth: {
				async resolve({ error, request: { headers } }) {
					const session = await auth.api.getSession({
						headers,
					});

					if (!session) return error(401);

					return {
						user: session.user,
						session: session.session,
						role: session.role,
					};
				},
			},
		});
};

#!/usr/bin/env node

import { execSync } from 'child_process'
import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

const DIST_DIR = 'dist'
const ASSETS_DIR = join(DIST_DIR, 'assets')

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function analyzeBundle() {
  console.log('🔍 Analyzing bundle...\n')

  if (!existsSync(DIST_DIR)) {
    console.error('❌ Build directory not found. Run "bun run build" first.')
    process.exit(1)
  }

  try {
    // Get file sizes
    const result = execSync(`find ${ASSETS_DIR} -name "*.js" -o -name "*.css" | xargs ls -la`, { 
      encoding: 'utf8' 
    })
    
    const files = result.split('\n').filter(line => line.trim())
    const jsFiles = []
    const cssFiles = []
    let totalSize = 0

    files.forEach(line => {
      const parts = line.split(/\s+/)
      if (parts.length >= 9) {
        const size = parseInt(parts[4])
        const filename = parts[8]
        const basename = filename.split('/').pop()
        
        totalSize += size
        
        if (filename.endsWith('.js')) {
          jsFiles.push({ name: basename, size, sizeFormatted: formatBytes(size) })
        } else if (filename.endsWith('.css')) {
          cssFiles.push({ name: basename, size, sizeFormatted: formatBytes(size) })
        }
      }
    })

    // Sort by size (largest first)
    jsFiles.sort((a, b) => b.size - a.size)
    cssFiles.sort((a, b) => b.size - a.size)

    console.log('📊 Bundle Analysis Results')
    console.log('=' .repeat(50))
    console.log(`Total bundle size: ${formatBytes(totalSize)}\n`)

    if (jsFiles.length > 0) {
      console.log('📦 JavaScript Files:')
      jsFiles.forEach(file => {
        const percentage = ((file.size / totalSize) * 100).toFixed(1)
        console.log(`  ${file.name.padEnd(40)} ${file.sizeFormatted.padStart(10)} (${percentage}%)`)
      })
      console.log()
    }

    if (cssFiles.length > 0) {
      console.log('🎨 CSS Files:')
      cssFiles.forEach(file => {
        const percentage = ((file.size / totalSize) * 100).toFixed(1)
        console.log(`  ${file.name.padEnd(40)} ${file.sizeFormatted.padStart(10)} (${percentage}%)`)
      })
      console.log()
    }

    // Warnings for large chunks
    const largeChunks = jsFiles.filter(file => file.size > 500 * 1024) // > 500KB
    if (largeChunks.length > 0) {
      console.log('⚠️  Large chunks detected (>500KB):')
      largeChunks.forEach(chunk => {
        console.log(`  - ${chunk.name}: ${chunk.sizeFormatted}`)
      })
      console.log('  Consider further code splitting for these chunks.\n')
    }

    // Check for optimal chunk sizes
    const optimalChunks = jsFiles.filter(file => file.size >= 50 * 1024 && file.size <= 250 * 1024)
    console.log(`✅ Optimal chunks (50KB-250KB): ${optimalChunks.length}/${jsFiles.length}`)
    
    if (totalSize > 2 * 1024 * 1024) { // > 2MB
      console.log('⚠️  Total bundle size is quite large. Consider:')
      console.log('  - Further code splitting')
      console.log('  - Tree shaking unused code')
      console.log('  - Lazy loading more components')
    } else {
      console.log('✅ Bundle size looks good!')
    }

  } catch (error) {
    console.error('❌ Error analyzing bundle:', error.message)
    process.exit(1)
  }
}

analyzeBundle()
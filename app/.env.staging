# Staging Environment Configuration
NODE_ENV=production
VITE_APP_TITLE=Home Server Admin Panel (Staging)
VITE_BUILD_VERSION=1.0.0-staging

# API Configuration
VITE_API_BASE_URL=https://staging-api.your-domain.com
VITE_WS_URL=wss://staging-api.your-domain.com

# Authentication
VITE_AUTH_PROVIDER=better-auth
VITE_SESSION_TIMEOUT=3600000

# Feature Flags
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG_MODE=true

# Performance Monitoring
VITE_PERFORMANCE_SAMPLE_RATE=0.5
VITE_ERROR_REPORTING_ENDPOINT=https://staging-errors.your-domain.com/api/errors

# Staging Settings
VITE_MOCK_API=false
VITE_ENABLE_DEVTOOLS=true
FROM oven/bun:1 as development

WORKDIR /apps

# Copy package files
COPY package.json bun.lock* ./
COPY app/package.json ./app/
COPY server/package.json ./server/

# Install dependencies
RUN bun install 

# Copy source code
COPY app ./app

WORKDIR /apps/app

EXPOSE 5173

CMD ["bun", "run", "dev"]

FROM oven/bun:1 as build

WORKDIR /apps

# Copy package files
COPY package.json bun.lock* ./
COPY app/package.json ./app/
COPY server/package.json ./server/

# Install dependencies
RUN bun install --frozen-lockfile

# Copy source code
COPY app ./app

# Build the app
WORKDIR /apps/app
RUN bun run build:production

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=build /apps/app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx/frontend.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
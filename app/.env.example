# Environment Configuration Example
# Copy this file to .env.local and fill in your values

# Application Environment
NODE_ENV=development
VITE_APP_TITLE=Home Server Admin Panel
VITE_BUILD_VERSION=1.0.0

# API Configuration
VITE_API_BASE_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001

# Authentication
VITE_AUTH_PROVIDER=better-auth
VITE_SESSION_TIMEOUT=3600000

# Feature Flags
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG_MODE=false

# Performance Monitoring
VITE_PERFORMANCE_SAMPLE_RATE=1.0
VITE_ERROR_REPORTING_ENDPOINT=

# Development Settings
VITE_MOCK_API=false
VITE_ENABLE_DEVTOOLS=true
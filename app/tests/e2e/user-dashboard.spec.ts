import { test, expect } from '@playwright/test'

test.describe('User Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Login as regular user
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    // Wait for dashboard to load
    await expect(page).toHaveURL(/\/dashboard/)
  })

  test('should display user dashboard with key metrics', async ({ page }) => {
    // Should show welcome message
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('Welcome')
    
    // Should show API key metrics
    await expect(page.locator('[data-testid="api-keys-count"]')).toBeVisible()
    await expect(page.locator('[data-testid="total-requests"]')).toBeVisible()
    await expect(page.locator('[data-testid="success-rate"]')).toBeVisible()
    
    // Should show recent activity
    await expect(page.locator('[data-testid="recent-activity"]')).toBeVisible()
  })

  test('should navigate to API keys page', async ({ page }) => {
    // Click API Keys navigation
    await page.click('text=API Keys')
    
    // Should navigate to API keys page
    await expect(page).toHaveURL(/\/dashboard\/api-keys/)
    
    // Should show API keys page content
    await expect(page.locator('text=Your API Keys')).toBeVisible()
    await expect(page.locator('[data-testid="create-api-key-button"]')).toBeVisible()
  })

  test('should navigate to usage analytics page', async ({ page }) => {
    // Click Usage Analytics navigation
    await page.click('text=Usage Analytics')
    
    // Should navigate to analytics page
    await expect(page).toHaveURL(/\/dashboard\/analytics/)
    
    // Should show analytics content
    await expect(page.locator('text=Usage Analytics')).toBeVisible()
    await expect(page.locator('[data-testid="usage-chart"]')).toBeVisible()
  })

  test('should navigate to profile page', async ({ page }) => {
    // Click profile navigation
    await page.click('[data-testid="profile-link"]')
    
    // Should navigate to profile page
    await expect(page).toHaveURL(/\/dashboard\/profile/)
    
    // Should show profile content
    await expect(page.locator('text=Profile Settings')).toBeVisible()
    await expect(page.locator('[data-testid="profile-form"]')).toBeVisible()
  })
})

test.describe('API Key Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login and navigate to API keys page
    await page.goto('/login')
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    await page.click('text=API Keys')
    await expect(page).toHaveURL(/\/dashboard\/api-keys/)
  })

  test('should display existing API keys', async ({ page }) => {
    // Mock API keys response
    await page.route('**/api/keys', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'key-1',
            name: 'Production Key',
            description: 'Key for production use',
            status: 'active',
            createdAt: '2024-01-01T00:00:00Z',
            lastUsedAt: '2024-01-02T00:00:00Z'
          },
          {
            id: 'key-2',
            name: 'Development Key',
            description: 'Key for development',
            status: 'active',
            createdAt: '2024-01-02T00:00:00Z'
          }
        ])
      })
    })
    
    // Reload to get mocked data
    await page.reload()
    
    // Should show API keys
    await expect(page.locator('text=Production Key')).toBeVisible()
    await expect(page.locator('text=Development Key')).toBeVisible()
    await expect(page.locator('text=Key for production use')).toBeVisible()
  })

  test('should create new API key', async ({ page }) => {
    // Mock create API key response
    await page.route('**/api/keys', route => {
      if (route.request().method() === 'POST') {
        route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'key-new',
            name: 'New Test Key',
            description: 'Test key description',
            key: 'sk-test-123456789',
            status: 'active',
            createdAt: new Date().toISOString()
          })
        })
      } else {
        route.continue()
      }
    })
    
    // Click create API key button
    await page.click('[data-testid="create-api-key-button"]')
    
    // Should open create dialog
    await expect(page.locator('text=Create New API Key')).toBeVisible()
    
    // Fill in form
    await page.fill('[data-testid="key-name-input"]', 'New Test Key')
    await page.fill('[data-testid="key-description-input"]', 'Test key description')
    
    // Select permissions
    await page.check('[data-testid="permission-read"]')
    await page.check('[data-testid="permission-write"]')
    
    // Submit form
    await page.click('[data-testid="create-key-submit"]')
    
    // Should show success message and new key
    await expect(page.locator('text=API Key Created Successfully')).toBeVisible()
    await expect(page.locator('text=sk-test-123456789')).toBeVisible()
    await expect(page.locator('text=Copy this key now')).toBeVisible()
  })

  test('should copy API key to clipboard', async ({ page }) => {
    // Mock clipboard API
    await page.addInitScript(() => {
      Object.assign(navigator, {
        clipboard: {
          writeText: (text: string) => Promise.resolve()
        }
      })
    })
    
    // Create API key first (using previous test setup)
    await page.route('**/api/keys', route => {
      if (route.request().method() === 'POST') {
        route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            id: 'key-new',
            name: 'New Test Key',
            key: 'sk-test-123456789',
            status: 'active'
          })
        })
      } else {
        route.continue()
      }
    })
    
    await page.click('[data-testid="create-api-key-button"]')
    await page.fill('[data-testid="key-name-input"]', 'New Test Key')
    await page.check('[data-testid="permission-read"]')
    await page.click('[data-testid="create-key-submit"]')
    
    // Click copy button
    await page.click('[data-testid="copy-key-button"]')
    
    // Should show copied confirmation
    await expect(page.locator('text=Copied to clipboard')).toBeVisible()
  })

  test('should regenerate API key', async ({ page }) => {
    // Mock existing keys and regenerate response
    await page.route('**/api/keys', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'key-1',
            name: 'Production Key',
            status: 'active',
            createdAt: '2024-01-01T00:00:00Z'
          }
        ])
      })
    })
    
    await page.route('**/api/keys/key-1/regenerate', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'key-1',
          name: 'Production Key',
          key: 'sk-new-regenerated-key',
          status: 'active'
        })
      })
    })
    
    await page.reload()
    
    // Click regenerate button
    await page.click('[data-testid="regenerate-key-button"]')
    
    // Confirm regeneration
    await expect(page.locator('text=Regenerate API Key')).toBeVisible()
    await page.click('[data-testid="confirm-regenerate"]')
    
    // Should show new key
    await expect(page.locator('text=API Key Regenerated')).toBeVisible()
    await expect(page.locator('text=sk-new-regenerated-key')).toBeVisible()
  })

  test('should revoke API key', async ({ page }) => {
    // Mock existing keys and revoke response
    await page.route('**/api/keys', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'key-1',
            name: 'Production Key',
            status: 'active',
            createdAt: '2024-01-01T00:00:00Z'
          }
        ])
      })
    })
    
    await page.route('**/api/keys/key-1/revoke', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          id: 'key-1',
          name: 'Production Key',
          status: 'revoked'
        })
      })
    })
    
    await page.reload()
    
    // Click revoke button
    await page.click('[data-testid="revoke-key-button"]')
    
    // Confirm revocation
    await expect(page.locator('text=Revoke API Key')).toBeVisible()
    await page.click('[data-testid="confirm-revoke"]')
    
    // Should show success message
    await expect(page.locator('text=API Key Revoked')).toBeVisible()
  })

  test('should delete API key', async ({ page }) => {
    // Mock existing keys and delete response
    await page.route('**/api/keys', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'key-1',
            name: 'Production Key',
            status: 'active',
            createdAt: '2024-01-01T00:00:00Z'
          }
        ])
      })
    })
    
    await page.route('**/api/keys/key-1', route => {
      if (route.request().method() === 'DELETE') {
        route.fulfill({ status: 204 })
      } else {
        route.continue()
      }
    })
    
    await page.reload()
    
    // Click delete button
    await page.click('[data-testid="delete-key-button"]')
    
    // Confirm deletion
    await expect(page.locator('text=Delete API Key')).toBeVisible()
    await expect(page.locator('text=This action cannot be undone')).toBeVisible()
    await page.click('[data-testid="confirm-delete"]')
    
    // Should show success message
    await expect(page.locator('text=API Key Deleted')).toBeVisible()
  })

  test('should view API key details', async ({ page }) => {
    // Mock existing keys and usage data
    await page.route('**/api/keys', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'key-1',
            name: 'Production Key',
            description: 'Key for production use',
            status: 'active',
            createdAt: '2024-01-01T00:00:00Z',
            lastUsedAt: '2024-01-02T00:00:00Z'
          }
        ])
      })
    })
    
    await page.route('**/api/keys/key-1/usage', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          totalRequests: 1000,
          successfulRequests: 950,
          failedRequests: 50,
          averageResponseTime: 250
        })
      })
    })
    
    await page.reload()
    
    // Click on API key card
    await page.click('[data-testid="api-key-card"]')
    
    // Should open details modal
    await expect(page.locator('text=API Key Details')).toBeVisible()
    await expect(page.locator('text=Production Key')).toBeVisible()
    await expect(page.locator('text=Key for production use')).toBeVisible()
    
    // Should show usage statistics
    await expect(page.locator('text=1,000')).toBeVisible() // Total requests
    await expect(page.locator('text=950')).toBeVisible() // Successful requests
    await expect(page.locator('text=250ms')).toBeVisible() // Average response time
  })

  test('should filter API keys', async ({ page }) => {
    // Mock keys with different statuses
    await page.route('**/api/keys', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'key-1',
            name: 'Active Key',
            status: 'active',
            createdAt: '2024-01-01T00:00:00Z'
          },
          {
            id: 'key-2',
            name: 'Revoked Key',
            status: 'revoked',
            createdAt: '2024-01-02T00:00:00Z'
          }
        ])
      })
    })
    
    await page.reload()
    
    // Should show all keys initially
    await expect(page.locator('text=Active Key')).toBeVisible()
    await expect(page.locator('text=Revoked Key')).toBeVisible()
    
    // Filter by active status
    await page.click('[data-testid="status-filter"]')
    await page.click('text=Active')
    
    // Should only show active keys
    await expect(page.locator('text=Active Key')).toBeVisible()
    await expect(page.locator('text=Revoked Key')).not.toBeVisible()
  })

  test('should search API keys', async ({ page }) => {
    // Mock keys for search
    await page.route('**/api/keys', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'key-1',
            name: 'Production Key',
            status: 'active'
          },
          {
            id: 'key-2',
            name: 'Development Key',
            status: 'active'
          }
        ])
      })
    })
    
    await page.reload()
    
    // Search for specific key
    await page.fill('[data-testid="search-input"]', 'Production')
    
    // Should only show matching keys
    await expect(page.locator('text=Production Key')).toBeVisible()
    await expect(page.locator('text=Development Key')).not.toBeVisible()
  })

  test('should handle empty API keys state', async ({ page }) => {
    // Mock empty response
    await page.route('**/api/keys', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([])
      })
    })
    
    await page.reload()
    
    // Should show empty state
    await expect(page.locator('text=No API Keys Found')).toBeVisible()
    await expect(page.locator('text=Create your first API key')).toBeVisible()
    await expect(page.locator('[data-testid="create-first-key-button"]')).toBeVisible()
  })

  test('should handle API key loading error', async ({ page }) => {
    // Mock error response
    await page.route('**/api/keys', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' })
      })
    })
    
    await page.reload()
    
    // Should show error state
    await expect(page.locator('text=Failed to load API keys')).toBeVisible()
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
  })

  test('should retry loading API keys', async ({ page }) => {
    // Mock initial error then success
    let callCount = 0
    await page.route('**/api/keys', route => {
      callCount++
      if (callCount === 1) {
        route.fulfill({
          status: 500,
          contentType: 'application/json',
          body: JSON.stringify({ error: 'Internal server error' })
        })
      } else {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify([
            {
              id: 'key-1',
              name: 'Production Key',
              status: 'active'
            }
          ])
        })
      }
    })
    
    await page.reload()
    
    // Should show error initially
    await expect(page.locator('text=Failed to load API keys')).toBeVisible()
    
    // Click retry
    await page.click('[data-testid="retry-button"]')
    
    // Should load successfully
    await expect(page.locator('text=Production Key')).toBeVisible()
  })
})
import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/')
  })

  test('should complete user login flow', async ({ page }) => {
    // Should redirect to login page if not authenticated
    await expect(page).toHaveURL(/\/login/)
    
    // Fill in login form
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    
    // Submit login form
    await page.click('[data-testid="login-button"]')
    
    // Should redirect to user dashboard after successful login
    await expect(page).toHaveURL(/\/dashboard/)
    
    // Should show welcome message
    await expect(page.locator('[data-testid="welcome-message"]')).toContainText('Welcome')
    
    // Should show user navigation
    await expect(page.locator('[data-testid="user-nav"]')).toBeVisible()
    await expect(page.locator('text=API Keys')).toBeVisible()
    await expect(page.locator('text=Usage Analytics')).toBeVisible()
  })

  test('should complete admin login flow', async ({ page }) => {
    // Fill in admin credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'admin123')
    
    // Submit login form
    await page.click('[data-testid="login-button"]')
    
    // Should redirect to admin dashboard
    await expect(page).toHaveURL(/\/admin/)
    
    // Should show admin dashboard
    await expect(page.locator('text=Admin Dashboard')).toBeVisible()
    
    // Should show admin navigation
    await expect(page.locator('text=User Management')).toBeVisible()
    await expect(page.locator('text=Analytics')).toBeVisible()
    await expect(page.locator('text=Settings')).toBeVisible()
  })

  test('should handle login validation errors', async ({ page }) => {
    // Try to submit empty form
    await page.click('[data-testid="login-button"]')
    
    // Should show validation errors
    await expect(page.locator('text=Email is required')).toBeVisible()
    await expect(page.locator('text=Password is required')).toBeVisible()
    
    // Fill invalid email
    await page.fill('[data-testid="email-input"]', 'invalid-email')
    await page.click('[data-testid="login-button"]')
    
    // Should show email validation error
    await expect(page.locator('text=Please enter a valid email')).toBeVisible()
    
    // Fill short password
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', '123')
    await page.click('[data-testid="login-button"]')
    
    // Should show password validation error
    await expect(page.locator('text=Password must be at least 6 characters')).toBeVisible()
  })

  test('should handle login failure', async ({ page }) => {
    // Fill in invalid credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'wrongpassword')
    
    // Submit login form
    await page.click('[data-testid="login-button"]')
    
    // Should show error message
    await expect(page.locator('[data-testid="auth-error"]')).toContainText('Invalid credentials')
    
    // Should remain on login page
    await expect(page).toHaveURL(/\/login/)
  })

  test('should show loading state during login', async ({ page }) => {
    // Fill in credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    
    // Submit form and immediately check loading state
    await page.click('[data-testid="login-button"]')
    
    // Should show loading state
    await expect(page.locator('[data-testid="login-button"]')).toContainText('Signing in...')
    await expect(page.locator('[data-testid="login-button"]')).toBeDisabled()
  })

  test('should handle logout flow', async ({ page }) => {
    // Login first
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    // Wait for dashboard to load
    await expect(page).toHaveURL(/\/dashboard/)
    
    // Click logout button
    await page.click('[data-testid="logout-button"]')
    
    // Should redirect to login page
    await expect(page).toHaveURL(/\/login/)
    
    // Should not be able to access protected routes
    await page.goto('/dashboard')
    await expect(page).toHaveURL(/\/login/)
  })

  test('should handle session expiry', async ({ page }) => {
    // Login with expired session (mock scenario)
    await page.route('**/api/auth/session', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Session expired' })
      })
    })
    
    // Try to access protected route
    await page.goto('/dashboard')
    
    // Should redirect to login with session expired message
    await expect(page).toHaveURL(/\/login/)
    await expect(page.locator('text=Session expired')).toBeVisible()
  })

  test('should toggle password visibility', async ({ page }) => {
    const passwordInput = page.locator('[data-testid="password-input"]')
    const toggleButton = page.locator('[data-testid="password-toggle"]')
    
    // Password should be hidden by default
    await expect(passwordInput).toHaveAttribute('type', 'password')
    
    // Click toggle to show password
    await toggleButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'text')
    
    // Click toggle to hide password again
    await toggleButton.click()
    await expect(passwordInput).toHaveAttribute('type', 'password')
  })

  test('should handle keyboard navigation', async ({ page }) => {
    // Tab through form elements
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="email-input"]')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="password-input"]')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="password-toggle"]')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('[data-testid="login-button"]')).toBeFocused()
  })

  test('should handle form submission with Enter key', async ({ page }) => {
    // Fill in credentials
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    
    // Press Enter to submit
    await page.keyboard.press('Enter')
    
    // Should redirect to dashboard
    await expect(page).toHaveURL(/\/dashboard/)
  })

  test('should handle pending user status', async ({ page }) => {
    // Mock pending user response
    await page.route('**/api/auth/login', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 'user-1',
            email: '<EMAIL>',
            name: 'Pending User',
            status: 'pending',
            role: 'user'
          },
          token: 'mock-token'
        })
      })
    })
    
    // Login with pending user
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    // Should show pending approval message
    await expect(page.locator('text=Account Pending Approval')).toBeVisible()
    await expect(page.locator('text=Your account is awaiting admin approval')).toBeVisible()
    
    // Should not show API key creation options
    await expect(page.locator('text=Create API Key')).not.toBeVisible()
  })

  test('should handle revoked user status', async ({ page }) => {
    // Mock revoked user response
    await page.route('**/api/auth/login', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: 'user-1',
            email: '<EMAIL>',
            name: 'Revoked User',
            status: 'revoked',
            role: 'user'
          },
          token: 'mock-token'
        })
      })
    })
    
    // Login with revoked user
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    // Should show access revoked message
    await expect(page.locator('text=Access Revoked')).toBeVisible()
    await expect(page.locator('text=Your access has been revoked')).toBeVisible()
  })
})

test.describe('Responsive Design', () => {
  test('should work on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await page.goto('/')
    
    // Login form should be responsive
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible()
    
    // Fill and submit form
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    // Dashboard should be responsive
    await expect(page).toHaveURL(/\/dashboard/)
    
    // Mobile navigation should be available
    await expect(page.locator('[data-testid="mobile-nav-toggle"]')).toBeVisible()
  })

  test('should work on tablet devices', async ({ page }) => {
    // Set tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 })
    
    await page.goto('/')
    
    // Should show appropriate layout for tablet
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible()
    
    // Complete login flow
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    await expect(page).toHaveURL(/\/dashboard/)
  })

  test('should work on desktop', async ({ page }) => {
    // Set desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 })
    
    await page.goto('/')
    
    // Should show full desktop layout
    await expect(page.locator('[data-testid="login-form"]')).toBeVisible()
    
    // Complete login flow
    await page.fill('[data-testid="email-input"]', '<EMAIL>')
    await page.fill('[data-testid="password-input"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    await expect(page).toHaveURL(/\/dashboard/)
    
    // Desktop navigation should be visible
    await expect(page.locator('[data-testid="desktop-nav"]')).toBeVisible()
  })
})
# Deployment Guide

This guide covers deploying the Home Server Admin Panel to different environments.

## Prerequisites

- Node.js 18+ or Bun
- Access to your deployment environment
- Environment-specific configuration files

## Environment Setup

### 1. Environment Variables

Copy the appropriate environment file for your deployment:

```bash
# For development
cp .env.example .env.local

# For staging
cp .env.staging .env.local

# For production
cp .env.production .env.local
```

Update the values in your `.env.local` file:

- `VITE_API_BASE_URL`: Your backend API URL
- `VITE_WS_URL`: Your WebSocket URL
- `VITE_ERROR_REPORTING_ENDPOINT`: Error reporting service URL
- Other environment-specific settings

### 2. Build Configuration

The application supports multiple build modes:

- **Development**: `bun run dev`
- **Staging**: `bun run build:staging`
- **Production**: `bun run build:production`

## Build Process

### Development Build
```bash
bun install
bun run dev
```

### Staging Build
```bash
bun install
bun run build:staging
bun run preview:staging
```

### Production Build
```bash
bun install
bun run build:production
bun run preview:production
```

## Bundle Analysis

Analyze your bundle size and optimization:

```bash
# Analyze development build
bun run build:analyze

# Analyze staging build
bun run build:analyze:staging

# Analyze production build
bun run build:analyze:production
```

## Deployment Options

### 1. Static Hosting (Recommended)

The application builds to static files that can be hosted on any static hosting service.

#### Netlify
1. Connect your repository to Netlify
2. Set build command: `bun run build:production`
3. Set publish directory: `dist`
4. Add environment variables in Netlify dashboard

#### Vercel
1. Connect your repository to Vercel
2. Set build command: `bun run build:production`
3. Set output directory: `dist`
4. Add environment variables in Vercel dashboard

#### AWS S3 + CloudFront
1. Build the application: `bun run build:production`
2. Upload `dist/` contents to S3 bucket
3. Configure CloudFront distribution
4. Set up proper routing for SPA

### 2. Docker Deployment

Create a `Dockerfile`:

```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app
COPY package.json bun.lock ./
RUN npm install -g bun && bun install

COPY . .
RUN bun run build:production

# Production stage
FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

Build and run:
```bash
docker build -t home-server-admin .
docker run -p 80:80 home-server-admin
```

### 3. Traditional Web Server

#### Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.html;

    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

#### Apache Configuration

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/html

    # Handle client-side routing
    <Directory /var/www/html>
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>

    # Cache static assets
    <LocationMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg)$">
        ExpiresActive On
        ExpiresDefault "access plus 1 year"
    </LocationMatch>
</VirtualHost>
```

## Performance Optimization

### 1. Build Optimization

The production build includes:
- Code splitting and lazy loading
- Tree shaking to remove unused code
- Minification and compression
- Asset optimization
- Bundle analysis

### 2. Caching Strategy

- Static assets are hashed for cache busting
- Long-term caching for unchanged files
- Service worker for offline functionality (if implemented)

### 3. CDN Configuration

For optimal performance, serve static assets from a CDN:

1. Upload built assets to CDN
2. Update `VITE_CDN_URL` environment variable
3. Configure proper cache headers

## Monitoring and Maintenance

### 1. Performance Monitoring

The application includes built-in performance monitoring:
- Web Vitals tracking
- Chart rendering performance
- User interaction analytics
- Error reporting

### 2. Health Checks

Implement health check endpoints:
- `/health` - Basic health check
- `/api/health` - API health check
- Monitor key metrics and alerts

### 3. Logging

Configure logging for:
- Application errors
- Performance metrics
- User interactions
- Security events

## Security Considerations

### 1. Environment Variables

- Never commit sensitive environment variables
- Use secure secret management in production
- Rotate API keys and tokens regularly

### 2. Content Security Policy

Add CSP headers to prevent XSS attacks:

```html
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline'; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data: https:;">
```

### 3. HTTPS

Always use HTTPS in production:
- Obtain SSL certificates
- Configure automatic renewal
- Redirect HTTP to HTTPS

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Clear node_modules and reinstall
   - Verify environment variables

2. **Runtime Errors**
   - Check browser console for errors
   - Verify API endpoints are accessible
   - Check network connectivity

3. **Performance Issues**
   - Use bundle analyzer to identify large chunks
   - Implement additional code splitting
   - Optimize images and assets

### Debug Mode

Enable debug mode in staging:
```bash
VITE_ENABLE_DEBUG_MODE=true bun run build:staging
```

## Rollback Strategy

1. Keep previous build artifacts
2. Implement blue-green deployment
3. Monitor key metrics after deployment
4. Have rollback procedures documented

## Support

For deployment issues:
1. Check the troubleshooting section
2. Review application logs
3. Contact the development team
4. Create an issue in the repository
{"name": "app3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build:staging": "tsc -b && vite build --mode staging", "build:production": "tsc -b && vite build --mode production", "build:analyze": "bun run build && node scripts/analyze-bundle.js", "build:analyze:staging": "bun run build:staging && node scripts/analyze-bundle.js", "build:analyze:production": "bun run build:production && node scripts/analyze-bundle.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "preview:production": "vite preview --mode production", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "e2e:install": "playwright install", "clean": "rm -rf dist node_modules/.vite", "typecheck": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.15", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.85.3", "@tanstack/react-query-devtools": "^5.85.3", "axios": "^1.11.0", "better-auth": "^1.3.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "jotai": "^2.13.1", "jotai-tanstack-query": "^0.11.0", "jspdf": "^3.0.1", "lucide-react": "^0.539.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-day-picker": "^9.8.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.0", "recharts": "^3.1.2", "tailwind-merge": "^3.3.1", "tailwindcss": "4.1.12"}, "devDependencies": {"@eslint/js": "^9.33.0", "@playwright/test": "^1.54.2", "@tailwindcss/postcss": "^4.1.12", "@tailwindcss/vite": "^4.1.12", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/html2canvas": "^1.0.0", "@types/node": "^24.2.1", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/recharts": "^2.0.1", "@vitejs/plugin-react": "^5.0.0", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "playwright": "^1.54.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vitest": "^3.2.4"}}
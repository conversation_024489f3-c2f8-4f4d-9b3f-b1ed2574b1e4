import type { FilterOptions, TimeRange } from './api'

export interface ApiPermission {
  resource: string
  actions: string[]
}

export interface ApiKey {
  id: string
  userId: string
  name: string
  description?: string
  key?: string // Only shown once during creation
  keyHash: string // Stored hash for verification
  status: 'active' | 'revoked'
  permissions: ApiPermission[]
  rateLimit: {
    requestsPerMinute: number
    requestsPerHour: number
    requestsPerDay: number
  }
  createdAt: Date
  updatedAt: Date
  lastUsedAt?: Date
  expiresAt?: Date
}

export interface CreateApiKeyRequest {
  name: string
  description?: string
  permissions: ApiPermission[]
  rateLimit?: Partial<ApiKey['rateLimit']>
  expiresAt?: Date
}

export interface UpdateApiKeyRequest {
  id: string
  name?: string
  description?: string
  permissions?: ApiPermission[]
  rateLimit?: Partial<ApiKey['rateLimit']>
  expiresAt?: Date
}

export interface ApiKeyFilters extends FilterOptions {
  status?: 'all' | 'active' | 'revoked'
  userId?: string
  hasExpiry?: boolean
}

export interface ApiKeyStats {
  total: number
  active: number
  revoked: number
  expiringSoon: number
  neverUsed: number
  totalUsage: number
  averageUsage: number
}

export interface ApiKeyUsage {
  keyId: string
  period: TimeRange
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  dataTransferred: number
  topEndpoints: Array<{
    endpoint: string
    count: number
    averageResponseTime: number
  }>
}

export interface ApiKeyManagementState {
  keys: ApiKey[]
  selectedKey: ApiKey | null
  filters: ApiKeyFilters
  stats: ApiKeyStats | null
  usage: Record<string, ApiKeyUsage>
}
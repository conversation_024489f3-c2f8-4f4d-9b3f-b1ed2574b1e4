import type { User } from './auth'
import type { TimeRange, FilterOptions, PaginationParams } from './api'

// Extended user management types
export interface UserFilters extends FilterOptions {
  status?: 'all' | 'pending' | 'approved' | 'revoked'
  role?: 'all' | 'admin' | 'user'
  apiAccessLevel?: 'all' | 'none' | 'basic' | 'premium'
}

export interface UserStats {
  total: number
  pending: number
  approved: number
  revoked: number
  admins: number
  users: number
  recentSignups: number
  activeUsers: number
}

export interface UserActivity {
  userId: string
  action: 'login' | 'logout' | 'api_key_created' | 'api_key_deleted' | 'profile_updated'
  timestamp: Date
  metadata?: Record<string, any>
}

export interface UserManagementState {
  users: User[]
  selectedUser: User | null
  filters: UserFilters
  stats: UserStats | null
  activities: UserActivity[]
  pagination: PaginationParams
}

export interface UserActionRequest {
  userId: string
  action: 'approve' | 'revoke' | 'delete' | 'promote' | 'demote'
  reason?: string
}

export interface CreateUserRequest {
  email: string
  name: string
  role: 'admin' | 'user'
  apiAccessLevel: 'none' | 'basic' | 'premium'
}

export interface UpdateUserRequest {
  id: string
  name?: string
  role?: 'admin' | 'user'
  status?: 'pending' | 'approved' | 'revoked'
  apiAccessLevel?: 'none' | 'basic' | 'premium'
}
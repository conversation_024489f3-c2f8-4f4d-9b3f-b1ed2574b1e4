// Base API types and interfaces
export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: Date
}

export interface ValidationError extends ApiError {
  field: string
  value: any
}

export interface LoadingState {
  isLoading: boolean
  error: ApiError | null
}

export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface TimeRange {
  start: Date
  end: Date
}

export interface FilterOptions {
  search?: string
  status?: string
  dateRange?: TimeRange
}

// Generic async operation result
export interface AsyncResult<T> {
  data: T | null
  error: ApiError | null
  isLoading: boolean
}
import type { TimeRange, FilterOptions } from './api'

export interface ApiUsageLog {
  id: string
  userId: string
  apiKeyId: string
  endpoint: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  statusCode: number
  responseTime: number
  requestSize: number
  responseSize: number
  timestamp: Date
  ipAddress?: string
  userAgent?: string
  errorMessage?: string
}

export interface UsageMetrics {
  userId?: string
  apiKeyId?: string
  period: TimeRange
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  totalDataTransferred: number
  requestsPerHour: number[]
  topEndpoints: Array<{
    endpoint: string
    count: number
    averageResponseTime: number
    errorRate: number
  }>
  errorBreakdown: Array<{
    statusCode: number
    count: number
    percentage: number
  }>
  responseTimeDistribution: Array<{
    range: string
    count: number
    percentage: number
  }>
}

export interface OllamaPerformanceMetrics {
  modelName: string
  totalRequests: number
  averageResponseTime: number
  tokensPerSecond: number
  successRate: number
  errorRate: number
  peakUsageTime: Date
  resourceUtilization: {
    cpu: number
    memory: number
    gpu?: number
  }
  concurrentRequests: number
  queueLength: number
}

export interface SystemMetrics {
  timestamp: Date
  activeUsers: number
  totalApiCalls: number
  averageResponseTime: number
  errorRate: number
  systemLoad: {
    cpu: number
    memory: number
    disk: number
  }
  ollamaMetrics: OllamaPerformanceMetrics[]
}

export interface AnalyticsFilters extends FilterOptions {
  userId?: string
  apiKeyId?: string
  endpoint?: string
  method?: string
  statusCode?: number
  timeRange: TimeRange
}

export interface TrendData {
  timestamp: Date
  value: number
  label?: string
}

export interface AnalyticsState {
  logs: ApiUsageLog[]
  metrics: UsageMetrics | null
  systemMetrics: SystemMetrics[]
  ollamaMetrics: OllamaPerformanceMetrics[]
  filters: AnalyticsFilters
  trends: {
    requests: TrendData[]
    responseTime: TrendData[]
    errors: TrendData[]
    users: TrendData[]
  }
}

export interface AnalyticsQuery {
  timeRange: TimeRange
  granularity: 'minute' | 'hour' | 'day' | 'week' | 'month'
  metrics: string[]
  filters?: Partial<AnalyticsFilters>
  groupBy?: string[]
}
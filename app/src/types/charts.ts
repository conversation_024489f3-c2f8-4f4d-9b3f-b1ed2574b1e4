export interface ChartData {
  [key: string]: string | number | Date
}

export interface ChartConfig {
  title?: string
  xAxisKey: string
  yAxisKey?: string
  dataKeys: Array<{
    key: string
    name: string
    color: string
    type?: 'monotone' | 'linear' | 'basis' | 'step'
  }>
  showGrid?: boolean
  showLegend?: boolean
  showTooltip?: boolean
  height?: number
  margin?: {
    top?: number
    right?: number
    bottom?: number
    left?: number
  }
}

export interface MetricCardData {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon?: React.ReactNode
  description?: string
}

export type ChartType = 'line' | 'bar' | 'pie' | 'area'

export interface BaseChartProps {
  data: ChartData[]
  config: ChartConfig
  className?: string
  loading?: boolean
  error?: string
}

export interface PieChartData {
  name: string
  value: number
  color?: string
  percentage?: string
}

export interface PieChartConfig {
  title?: string
  showLegend?: boolean
  showTooltip?: boolean
  height?: number
  innerRadius?: number
  outerRadius?: number
  colors?: string[]
}
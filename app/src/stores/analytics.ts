import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import type { 
  ApiUsageLog, 
  UsageMetrics, 
  OllamaPerformanceMetrics, 
  SystemMetrics,
  AnalyticsFilters,
  AnalyticsQuery,
  AnalyticsState,
  TrendData
} from '../types/analytics'
import type { TimeRange } from '../types/api'
import { 
  createAsyncAtom, 
  createFilteredAtom, 
  createComputedAtom,
  createDebouncedAtom
} from './base'
import { apiClient } from '../lib/api'
import { userAtom } from './auth'

// Base atoms for analytics state
export const usageLogsAtom = atom<ApiUsageLog[]>([])
export const usageMetricsAtom = atom<UsageMetrics | null>(null)
export const systemMetricsAtom = atom<SystemMetrics[]>([])
export const ollamaMetricsAtom = atom<OllamaPerformanceMetrics[]>([])

// Trend data atoms
export const requestTrendsAtom = atom<TrendData[]>([])
export const responseTimeTrendsAtom = atom<TrendData[]>([])
export const errorTrendsAtom = atom<TrendData[]>([])
export const userTrendsAtom = atom<TrendData[]>([])

// Filters atom with persistence
export const analyticsFiltersAtom = atomWithStorage<AnalyticsFilters>('analytics-filters', {
  timeRange: {
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
    end: new Date()
  },
  search: ''
})

// Debounced search for better UX
export const debouncedAnalyticsSearchAtom = createDebouncedAtom<string>(analyticsFiltersAtom, 300)

// Current user's analytics (filtered by user ID)
export const userAnalyticsAtom = atom((get) => {
  const logs = get(usageLogsAtom)
  const user = get(userAtom)
  
  if (!user) return []
  
  return logs.filter(log => log.userId === user.id)
})

// Filtered usage logs based on current filters
export const filteredUsageLogsAtom = createFilteredAtom<ApiUsageLog, AnalyticsFilters>(
  usageLogsAtom,
  analyticsFiltersAtom,
  (logs, filters) => {
    let filtered = [...logs]

    // Filter by user ID
    if (filters.userId) {
      filtered = filtered.filter(log => log.userId === filters.userId)
    }

    // Filter by API key ID
    if (filters.apiKeyId) {
      filtered = filtered.filter(log => log.apiKeyId === filters.apiKeyId)
    }

    // Filter by endpoint
    if (filters.endpoint) {
      filtered = filtered.filter(log => log.endpoint.includes(filters.endpoint!))
    }

    // Filter by method
    if (filters.method) {
      filtered = filtered.filter(log => log.method === filters.method)
    }

    // Filter by status code
    if (filters.statusCode) {
      filtered = filtered.filter(log => log.statusCode === filters.statusCode)
    }

    // Filter by time range
    if (filters.timeRange) {
      filtered = filtered.filter(log => 
        log.timestamp >= filters.timeRange!.start && 
        log.timestamp <= filters.timeRange!.end
      )
    }

    // Filter by search term
    if (filters.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(log => 
        log.endpoint.toLowerCase().includes(search) ||
        log.method.toLowerCase().includes(search) ||
        (log.errorMessage && log.errorMessage.toLowerCase().includes(search))
      )
    }

    return filtered
  }
)

// Sorted usage logs by timestamp (newest first)
export const sortedUsageLogsAtom = atom((get) => {
  const logs = get(filteredUsageLogsAtom)
  return [...logs].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  )
})

// Analytics statistics derived atoms
export const analyticsStatsSummaryAtom = createComputedAtom<ApiUsageLog[], {
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  totalDataTransferred: number
  uniqueUsers: number
  uniqueEndpoints: number
  errorRate: number
  successRate: number
}>(
  filteredUsageLogsAtom,
  (logs) => {
    if (logs.length === 0) {
      return {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        totalDataTransferred: 0,
        uniqueUsers: 0,
        uniqueEndpoints: 0,
        errorRate: 0,
        successRate: 0
      }
    }

    const successfulRequests = logs.filter(log => log.statusCode < 400).length
    const failedRequests = logs.length - successfulRequests
    const totalResponseTime = logs.reduce((sum, log) => sum + log.responseTime, 0)
    const totalDataTransferred = logs.reduce((sum, log) => sum + log.requestSize + log.responseSize, 0)
    const uniqueUsers = new Set(logs.map(log => log.userId)).size
    const uniqueEndpoints = new Set(logs.map(log => log.endpoint)).size

    return {
      totalRequests: logs.length,
      successfulRequests,
      failedRequests,
      averageResponseTime: Math.round(totalResponseTime / logs.length),
      totalDataTransferred,
      uniqueUsers,
      uniqueEndpoints,
      errorRate: failedRequests / logs.length,
      successRate: successfulRequests / logs.length
    }
  }
)

// Logs grouped by endpoint
export const logsByEndpointAtom = createComputedAtom<ApiUsageLog[], Record<string, ApiUsageLog[]>>(
  filteredUsageLogsAtom,
  (logs) => {
    return logs.reduce((acc, log) => {
      if (!acc[log.endpoint]) {
        acc[log.endpoint] = []
      }
      acc[log.endpoint].push(log)
      return acc
    }, {} as Record<string, ApiUsageLog[]>)
  }
)

// Logs grouped by status code
export const logsByStatusCodeAtom = createComputedAtom<ApiUsageLog[], Record<number, ApiUsageLog[]>>(
  filteredUsageLogsAtom,
  (logs) => {
    return logs.reduce((acc, log) => {
      if (!acc[log.statusCode]) {
        acc[log.statusCode] = []
      }
      acc[log.statusCode].push(log)
      return acc
    }, {} as Record<number, ApiUsageLog[]>)
  }
)

// Recent errors (last 24 hours)
export const recentErrorsAtom = atom((get) => {
  const logs = get(filteredUsageLogsAtom)
  const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)

  return logs.filter(log => 
    log.statusCode >= 400 &&
    log.timestamp >= twentyFourHoursAgo
  ).slice(0, 50) // Limit to 50 most recent errors
})

// Performance metrics derived atoms
export const performanceMetricsAtom = createComputedAtom<ApiUsageLog[], {
  p50ResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  slowestEndpoints: Array<{ endpoint: string; averageResponseTime: number; count: number }>
  fastestEndpoints: Array<{ endpoint: string; averageResponseTime: number; count: number }>
}>(
  filteredUsageLogsAtom,
  (logs) => {
    if (logs.length === 0) {
      return {
        p50ResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        slowestEndpoints: [],
        fastestEndpoints: []
      }
    }

    // Calculate percentiles
    const sortedResponseTimes = logs.map(log => log.responseTime).sort((a, b) => a - b)
    const p50Index = Math.floor(sortedResponseTimes.length * 0.5)
    const p95Index = Math.floor(sortedResponseTimes.length * 0.95)
    const p99Index = Math.floor(sortedResponseTimes.length * 0.99)

    // Calculate endpoint performance
    const endpointStats = logs.reduce((acc, log) => {
      if (!acc[log.endpoint]) {
        acc[log.endpoint] = { totalTime: 0, count: 0 }
      }
      acc[log.endpoint].totalTime += log.responseTime
      acc[log.endpoint].count += 1
      return acc
    }, {} as Record<string, { totalTime: number; count: number }>)

    const endpointPerformance = Object.entries(endpointStats)
      .map(([endpoint, stats]) => ({
        endpoint,
        averageResponseTime: Math.round(stats.totalTime / stats.count),
        count: stats.count
      }))
      .filter(ep => ep.count >= 5) // Only include endpoints with at least 5 requests

    return {
      p50ResponseTime: sortedResponseTimes[p50Index] || 0,
      p95ResponseTime: sortedResponseTimes[p95Index] || 0,
      p99ResponseTime: sortedResponseTimes[p99Index] || 0,
      slowestEndpoints: endpointPerformance
        .sort((a, b) => b.averageResponseTime - a.averageResponseTime)
        .slice(0, 5),
      fastestEndpoints: endpointPerformance
        .sort((a, b) => a.averageResponseTime - b.averageResponseTime)
        .slice(0, 5)
    }
  }
)

// Async action atoms for analytics operations

// Fetch usage logs
export const fetchUsageLogsAtom = createAsyncAtom(
  async (filters: AnalyticsFilters, limit = 100) => {
    const logs = await apiClient.getUsageLogs(filters, limit)
    return logs
  },
  'FETCH_USAGE_LOGS_ERROR'
)

// Fetch usage metrics
export const fetchUsageMetricsAtom = createAsyncAtom(
  async (query: AnalyticsQuery) => {
    const metrics = await apiClient.getUsageMetrics(query)
    return metrics
  },
  'FETCH_USAGE_METRICS_ERROR'
)

// Fetch Ollama performance metrics
export const fetchOllamaMetricsAtom = createAsyncAtom(
  async (timeRange: TimeRange) => {
    const metrics = await apiClient.getOllamaMetrics(timeRange)
    return metrics
  },
  'FETCH_OLLAMA_METRICS_ERROR'
)

// Fetch system metrics
export const fetchSystemMetricsAtom = createAsyncAtom(
  async (timeRange: TimeRange) => {
    const metrics = await apiClient.getSystemMetrics(timeRange)
    return metrics
  },
  'FETCH_SYSTEM_METRICS_ERROR'
)

// Fetch trend data
export const fetchTrendDataAtom = createAsyncAtom(
  async (metric: string, timeRange: TimeRange, granularity: 'hour' | 'day' | 'week' = 'day') => {
    const data = await apiClient.getTrendData(metric, timeRange, granularity)
    return data
  },
  'FETCH_TREND_DATA_ERROR'
)

// Combined action atoms

// Load complete analytics dashboard data
export const loadAnalyticsDashboardAtom = atom(
  null,
  async (get, set, filters?: AnalyticsFilters) => {
    const currentFilters = filters || get(analyticsFiltersAtom)
    
    // Create analytics query from filters
    const query: AnalyticsQuery = {
      timeRange: currentFilters.timeRange,
      granularity: 'day',
      metrics: ['requests', 'responseTime', 'errors'],
      filters: currentFilters
    }

    // Fetch all data in parallel
    const [logsResult, metricsResult, ollamaResult, systemResult] = await Promise.allSettled([
      get(fetchUsageLogsAtom.actionAtom)(currentFilters, 1000),
      get(fetchUsageMetricsAtom.actionAtom)(query),
      get(fetchOllamaMetricsAtom.actionAtom)(currentFilters.timeRange),
      get(fetchSystemMetricsAtom.actionAtom)(currentFilters.timeRange)
    ])

    // Update state with successful results
    if (logsResult.status === 'fulfilled') {
      set(usageLogsAtom, logsResult.value)
    }

    if (metricsResult.status === 'fulfilled') {
      set(usageMetricsAtom, metricsResult.value)
    }

    if (ollamaResult.status === 'fulfilled') {
      set(ollamaMetricsAtom, ollamaResult.value)
    }

    if (systemResult.status === 'fulfilled') {
      set(systemMetricsAtom, systemResult.value)
    }

    // Return any errors
    const errors = []
    if (logsResult.status === 'rejected') errors.push(logsResult.reason)
    if (metricsResult.status === 'rejected') errors.push(metricsResult.reason)
    if (ollamaResult.status === 'rejected') errors.push(ollamaResult.reason)
    if (systemResult.status === 'rejected') errors.push(systemResult.reason)
    
    return { errors }
  }
)

// Load trend data for charts
export const loadTrendDataAtom = atom(
  null,
  async (get, set, timeRange?: TimeRange, granularity: 'hour' | 'day' | 'week' = 'day') => {
    const currentTimeRange = timeRange || get(analyticsFiltersAtom).timeRange

    // Fetch trend data for different metrics in parallel
    const [requestsResult, responseTimeResult, errorsResult, usersResult] = await Promise.allSettled([
      get(fetchTrendDataAtom.actionAtom)('requests', currentTimeRange, granularity),
      get(fetchTrendDataAtom.actionAtom)('responseTime', currentTimeRange, granularity),
      get(fetchTrendDataAtom.actionAtom)('errors', currentTimeRange, granularity),
      get(fetchTrendDataAtom.actionAtom)('users', currentTimeRange, granularity)
    ])

    // Update trend atoms with successful results
    if (requestsResult.status === 'fulfilled') {
      set(requestTrendsAtom, requestsResult.value)
    }

    if (responseTimeResult.status === 'fulfilled') {
      set(responseTimeTrendsAtom, responseTimeResult.value)
    }

    if (errorsResult.status === 'fulfilled') {
      set(errorTrendsAtom, errorsResult.value)
    }

    if (usersResult.status === 'fulfilled') {
      set(userTrendsAtom, usersResult.value)
    }

    // Return any errors
    const errors = []
    if (requestsResult.status === 'rejected') errors.push(requestsResult.reason)
    if (responseTimeResult.status === 'rejected') errors.push(responseTimeResult.reason)
    if (errorsResult.status === 'rejected') errors.push(errorsResult.reason)
    if (usersResult.status === 'rejected') errors.push(usersResult.reason)
    
    return { errors }
  }
)

// Load user-specific analytics
export const loadUserAnalyticsAtom = atom(
  null,
  async (get, set, userId?: string) => {
    const user = get(userAtom)
    const targetUserId = userId || user?.id
    
    if (!targetUserId) throw new Error('User ID required')

    const filters: AnalyticsFilters = {
      ...get(analyticsFiltersAtom),
      userId: targetUserId
    }

    await get(loadAnalyticsDashboardAtom)(filters)
  }
)

// Load API key specific analytics
export const loadApiKeyAnalyticsAtom = atom(
  null,
  async (get, set, apiKeyId: string) => {
    const filters: AnalyticsFilters = {
      ...get(analyticsFiltersAtom),
      apiKeyId
    }

    await get(loadAnalyticsDashboardAtom)(filters)
  }
)

// Refresh analytics data
export const refreshAnalyticsDataAtom = atom(
  null,
  async (get, set) => {
    const filters = get(analyticsFiltersAtom)
    await Promise.all([
      get(loadAnalyticsDashboardAtom)(filters),
      get(loadTrendDataAtom)(filters.timeRange)
    ])
  }
)

// Filter update atoms
export const updateAnalyticsFiltersAtom = atom(
  null,
  (get, set, updates: Partial<AnalyticsFilters>) => {
    const currentFilters = get(analyticsFiltersAtom)
    const newFilters = { ...currentFilters, ...updates }
    set(analyticsFiltersAtom, newFilters)
  }
)

// Time range preset atoms
export const setTimeRangePresetAtom = atom(
  null,
  (get, set, preset: 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year') => {
    const now = new Date()
    let start: Date

    switch (preset) {
      case 'hour':
        start = new Date(now.getTime() - 60 * 60 * 1000)
        break
      case 'day':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000)
        break
      case 'week':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'month':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case 'quarter':
        start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case 'year':
        start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    }

    set(updateAnalyticsFiltersAtom, { timeRange: { start, end: now } })
  }
)

// Real-time data simulation (for demo purposes)
export const enableRealTimeUpdatesAtom = atom<boolean>(false)

export const realTimeUpdateAtom = atom(
  null,
  async (get, set) => {
    const isEnabled = get(enableRealTimeUpdatesAtom)
    if (!isEnabled) return

    // Simulate real-time log updates
    const currentLogs = get(usageLogsAtom)
    const newLog: ApiUsageLog = {
      id: `realtime-${Date.now()}`,
      userId: 'user-1',
      apiKeyId: 'key-1',
      endpoint: '/api/ollama/generate',
      method: 'POST',
      statusCode: 200,
      responseTime: Math.floor(Math.random() * 500) + 100,
      requestSize: Math.floor(Math.random() * 5000) + 500,
      responseSize: Math.floor(Math.random() * 20000) + 1000,
      timestamp: new Date(),
      ipAddress: '*************',
      userAgent: 'Real-time Client/1.0'
    }

    set(usageLogsAtom, [newLog, ...currentLogs.slice(0, 999)]) // Keep only last 1000 logs
  }
)

// Toggle real-time updates
export const toggleRealTimeUpdatesAtom = atom(
  null,
  (get, set) => {
    const current = get(enableRealTimeUpdatesAtom)
    set(enableRealTimeUpdatesAtom, !current)
  }
)
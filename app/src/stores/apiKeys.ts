import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import type { 
  <PERSON><PERSON><PERSON><PERSON>, 
  ApiKeyFilters, 
  ApiKeyStats, 
  ApiKeyUsage,
  ApiKeyManagementState,
  CreateApiKeyRequest,
  UpdateApiKeyRequest
} from '../types/apiKey'
import type { TimeRange } from '../types/api'
import { 
  createAsync<PERSON>tom, 
  createFilteredAtom, 
  createComputedAtom,
  createOptimisticUpdateAtom,
  createDebouncedAtom
} from './base'
import { apiClient } from '../lib/api'
import { userAtom } from './auth'

// Base atoms for API key management state
export const apiKeysAtom = atom<ApiKey[]>([])
export const selectedApiKeyAtom = atom<ApiKey | null>(null)
export const apiKeyStatsAtom = atom<ApiKeyStats | null>(null)
export const apiKeyUsageAtom = atom<Record<string, ApiKeyUsage>>({})

// Filters atom with persistence
export const apiKeyFiltersAtom = atomWithStorage<ApiKeyFilters>('api-key-filters', {
  status: 'all',
  search: '',
  hasExpiry: undefined
})

// Debounced search for better UX
export const debouncedApiKeySearchAtom = createDebouncedAtom<string>(apiKeyFiltersAtom, 300)

// Current user's API keys (filtered by user ID)
export const userApiKeysAtom = atom((get) => {
  const allKeys = get(apiKeysAtom)
  const user = get(userAtom)
  
  if (!user) return []
  
  return allKeys.filter(key => key.userId === user.id)
})

// Filtered API keys based on current filters
export const filteredApiKeysAtom = createFilteredAtom<ApiKey, ApiKeyFilters>(
  apiKeysAtom,
  apiKeyFiltersAtom,
  (keys, filters) => {
    let filtered = [...keys]

    // Filter by status
    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(key => key.status === filters.status)
    }

    // Filter by search term
    if (filters.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(key => 
        key.name.toLowerCase().includes(search) ||
        (key.description && key.description.toLowerCase().includes(search))
      )
    }

    // Filter by expiry
    if (filters.hasExpiry !== undefined) {
      filtered = filtered.filter(key => 
        filters.hasExpiry ? key.expiresAt !== undefined : key.expiresAt === undefined
      )
    }

    // Filter by user ID if specified
    if (filters.userId) {
      filtered = filtered.filter(key => key.userId === filters.userId)
    }

    return filtered
  }
)

// Sorted API keys by creation date (newest first)
export const sortedApiKeysAtom = atom((get) => {
  const keys = get(filteredApiKeysAtom)
  return [...keys].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )
})

// API key statistics derived atoms
export const apiKeyStatsSummaryAtom = createComputedAtom<ApiKey[], {
  totalKeys: number
  activeKeys: number
  revokedKeys: number
  expiringSoon: number
  neverUsed: number
  recentlyCreated: number
}>(
  apiKeysAtom,
  (keys) => {
    const now = new Date()
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const monthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)

    return {
      totalKeys: keys.length,
      activeKeys: keys.filter(k => k.status === 'active').length,
      revokedKeys: keys.filter(k => k.status === 'revoked').length,
      expiringSoon: keys.filter(k => 
        k.expiresAt && new Date(k.expiresAt) <= monthFromNow && k.status === 'active'
      ).length,
      neverUsed: keys.filter(k => !k.lastUsedAt && k.status === 'active').length,
      recentlyCreated: keys.filter(k => new Date(k.createdAt) >= weekAgo).length
    }
  }
)

// Keys grouped by status
export const keysByStatusAtom = createComputedAtom<ApiKey[], Record<string, ApiKey[]>>(
  apiKeysAtom,
  (keys) => {
    return keys.reduce((acc, key) => {
      if (!acc[key.status]) {
        acc[key.status] = []
      }
      acc[key.status].push(key)
      return acc
    }, {} as Record<string, ApiKey[]>)
  }
)

// Keys expiring soon (within 30 days)
export const expiringSoonKeysAtom = atom((get) => {
  const keys = get(apiKeysAtom)
  const now = new Date()
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)

  return keys.filter(key => 
    key.status === 'active' &&
    key.expiresAt && 
    new Date(key.expiresAt) <= thirtyDaysFromNow &&
    new Date(key.expiresAt) > now
  )
})

// Async action atoms for API key operations

// Fetch API keys
export const fetchApiKeysAtom = createAsyncAtom(
  async (filters?: ApiKeyFilters, userId?: string) => {
    const keys = await apiClient.getApiKeys(filters, userId)
    return keys
  },
  'FETCH_API_KEYS_ERROR'
)

// Fetch API keys for current user
export const fetchUserApiKeysAtom = atom(
  null,
  async (get, set) => {
    const user = get(userAtom)
    if (!user) throw new Error('User not authenticated')

    const keys = await get(fetchApiKeysAtom.actionAtom)(undefined, user.id)
    set(apiKeysAtom, keys)
    return keys
  }
)

// Create new API key
export const createApiKeyAtom = createOptimisticUpdateAtom(
  apiKeysAtom,
  async (keyData: CreateApiKeyRequest, userId: string) => {
    const newKey = await apiClient.createApiKey(keyData, userId)
    return newKey
  },
  (currentKeys, keyData, userId) => [
    ...currentKeys,
    {
      id: `temp-${Date.now()}`,
      userId,
      name: keyData.name,
      description: keyData.description,
      keyHash: 'temp-hash',
      status: 'active' as const,
      permissions: keyData.permissions,
      rateLimit: {
        requestsPerMinute: keyData.rateLimit?.requestsPerMinute || 60,
        requestsPerHour: keyData.rateLimit?.requestsPerHour || 1000,
        requestsPerDay: keyData.rateLimit?.requestsPerDay || 10000
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      expiresAt: keyData.expiresAt
    }
  ],
  'CREATE_API_KEY_ERROR'
)

// Update API key
export const updateApiKeyAtom = createOptimisticUpdateAtom(
  apiKeysAtom,
  async (keyData: UpdateApiKeyRequest) => {
    const updatedKey = await apiClient.updateApiKey(keyData)
    return updatedKey
  },
  (currentKeys, keyData) => 
    currentKeys.map(key => 
      key.id === keyData.id 
        ? { ...key, ...keyData, updatedAt: new Date() }
        : key
    ),
  'UPDATE_API_KEY_ERROR'
)

// Regenerate API key
export const regenerateApiKeyAtom = createOptimisticUpdateAtom(
  apiKeysAtom,
  async (keyId: string) => {
    const regeneratedKey = await apiClient.regenerateApiKey(keyId)
    return regeneratedKey
  },
  (currentKeys, keyId) => 
    currentKeys.map(key => 
      key.id === keyId 
        ? { ...key, updatedAt: new Date(), lastUsedAt: undefined }
        : key
    ),
  'REGENERATE_API_KEY_ERROR'
)

// Revoke API key
export const revokeApiKeyAtom = createOptimisticUpdateAtom(
  apiKeysAtom,
  async (keyId: string) => {
    const revokedKey = await apiClient.revokeApiKey(keyId)
    return revokedKey
  },
  (currentKeys, keyId) => 
    currentKeys.map(key => 
      key.id === keyId 
        ? { ...key, status: 'revoked' as const, updatedAt: new Date() }
        : key
    ),
  'REVOKE_API_KEY_ERROR'
)

// Delete API key
export const deleteApiKeyAtom = createOptimisticUpdateAtom(
  apiKeysAtom,
  async (keyId: string) => {
    await apiClient.deleteApiKey(keyId)
    return keyId
  },
  (currentKeys, keyId) => currentKeys.filter(key => key.id !== keyId),
  'DELETE_API_KEY_ERROR'
)

// Fetch API key statistics
export const fetchApiKeyStatsAtom = createAsyncAtom(
  async (userId?: string) => {
    const stats = await apiClient.getApiKeyStats(userId)
    return stats
  },
  'FETCH_API_KEY_STATS_ERROR'
)

// Fetch API key usage data
export const fetchApiKeyUsageAtom = createAsyncAtom(
  async (keyId: string, timeRange: TimeRange) => {
    const usage = await apiClient.getApiKeyUsage(keyId, timeRange)
    return usage
  },
  'FETCH_API_KEY_USAGE_ERROR'
)

// Fetch single API key by ID
export const fetchApiKeyByIdAtom = createAsyncAtom(
  async (keyId: string) => {
    const key = await apiClient.getApiKeyById(keyId)
    return key
  },
  'FETCH_API_KEY_BY_ID_ERROR'
)

// Combined action atoms

// Load API keys with stats for current user
export const loadUserApiKeysWithStatsAtom = atom(
  null,
  async (get, set) => {
    const user = get(userAtom)
    if (!user) throw new Error('User not authenticated')

    // Fetch keys and stats in parallel
    const [keysResult, statsResult] = await Promise.allSettled([
      get(fetchApiKeysAtom.actionAtom)(undefined, user.id),
      get(fetchApiKeyStatsAtom.actionAtom)(user.id)
    ])

    if (keysResult.status === 'fulfilled') {
      set(apiKeysAtom, keysResult.value)
    }

    if (statsResult.status === 'fulfilled') {
      set(apiKeyStatsAtom, statsResult.value)
    }

    // Return any errors
    const errors = []
    if (keysResult.status === 'rejected') errors.push(keysResult.reason)
    if (statsResult.status === 'rejected') errors.push(statsResult.reason)
    
    return { errors }
  }
)

// Load all API keys with stats (admin view)
export const loadAllApiKeysWithStatsAtom = atom(
  null,
  async (get, set, filters?: ApiKeyFilters) => {
    // Fetch keys and stats in parallel
    const [keysResult, statsResult] = await Promise.allSettled([
      get(fetchApiKeysAtom.actionAtom)(filters),
      get(fetchApiKeyStatsAtom.actionAtom)()
    ])

    if (keysResult.status === 'fulfilled') {
      set(apiKeysAtom, keysResult.value)
    }

    if (statsResult.status === 'fulfilled') {
      set(apiKeyStatsAtom, statsResult.value)
    }

    // Return any errors
    const errors = []
    if (keysResult.status === 'rejected') errors.push(keysResult.reason)
    if (statsResult.status === 'rejected') errors.push(statsResult.reason)
    
    return { errors }
  }
)

// Select API key and load its usage data
export const selectApiKeyWithUsageAtom = atom(
  null,
  async (get, set, key: ApiKey, timeRange?: TimeRange) => {
    set(selectedApiKeyAtom, key)
    
    if (!timeRange) {
      // Default to last 30 days
      const now = new Date()
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      timeRange = { start: thirtyDaysAgo, end: now }
    }

    try {
      const usage = await get(fetchApiKeyUsageAtom.actionAtom)(key.id, timeRange)
      set(apiKeyUsageAtom, prev => ({ ...prev, [key.id]: usage }))
    } catch (error) {
      console.error('Failed to load API key usage:', error)
    }
  }
)

// Refresh API key data
export const refreshApiKeyDataAtom = atom(
  null,
  async (get, set) => {
    const user = get(userAtom)
    const filters = get(apiKeyFiltersAtom)
    
    if (user?.role === 'admin') {
      await get(loadAllApiKeysWithStatsAtom)(filters)
    } else {
      await get(loadUserApiKeysWithStatsAtom)()
    }
  }
)

// Filter update atoms
export const updateApiKeyFiltersAtom = atom(
  null,
  (get, set, updates: Partial<ApiKeyFilters>) => {
    const currentFilters = get(apiKeyFiltersAtom)
    const newFilters = { ...currentFilters, ...updates }
    set(apiKeyFiltersAtom, newFilters)
  }
)

// Clear selected API key
export const clearSelectedApiKeyAtom = atom(
  null,
  (_get, set) => {
    set(selectedApiKeyAtom, null)
  }
)

// Bulk operations for admin
export const bulkRevokeApiKeysAtom = atom(
  null,
  async (get, set, keyIds: string[]) => {
    const results = await Promise.allSettled(
      keyIds.map(keyId => get(revokeApiKeyAtom.optimisticActionAtom)(keyId))
    )

    const errors = results
      .filter(result => result.status === 'rejected')
      .map(result => (result as PromiseRejectedResult).reason)

    return { errors, successCount: results.length - errors.length }
  }
)

export const bulkDeleteApiKeysAtom = atom(
  null,
  async (get, set, keyIds: string[]) => {
    const results = await Promise.allSettled(
      keyIds.map(keyId => get(deleteApiKeyAtom.optimisticActionAtom)(keyId))
    )

    const errors = results
      .filter(result => result.status === 'rejected')
      .map(result => (result as PromiseRejectedResult).reason)

    return { errors, successCount: results.length - errors.length }
  }
)
import { atom } from 'jotai'
import type { ApiError, LoadingState, AsyncResult } from '../types/api'

// Base error handling atoms
export const globalErrorAtom = atom<ApiError | null>(null)
export const globalLoadingAtom = atom<boolean>(false)

// Utility function to create async atoms with error handling
export function createAsyncAtom<T, Args extends any[]>(
  asyncFn: (...args: Args) => Promise<T>,
  errorCode: string = 'ASYNC_ERROR'
) {
  const loadingAtom = atom<boolean>(false)
  const errorAtom = atom<ApiError | null>(null)
  const dataAtom = atom<T | null>(null)

  const actionAtom = atom(
    null,
    async (get, set, ...args: Args) => {
      set(loadingAtom, true)
      set(errorAtom, null)

      try {
        const result = await asyncFn(...args)
        set(dataAtom, result)
        return result
      } catch (error) {
        const apiError: ApiError = {
          code: errorCode,
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          timestamp: new Date(),
          details: error instanceof Error ? { stack: error.stack } : { error }
        }
        set(errorAtom, apiError)
        set(globalErrorAtom, apiError)
        throw apiError
      } finally {
        set(loadingAtom, false)
      }
    }
  )

  const resultAtom = atom<AsyncResult<T>>((get) => ({
    data: get(dataAtom),
    error: get(errorAtom),
    isLoading: get(loadingAtom)
  }))

  return {
    actionAtom,
    resultAtom,
    loadingAtom,
    errorAtom,
    dataAtom
  }
}

// Utility function to create loading state atoms
export function createLoadingStateAtom(initialState: boolean = false) {
  return atom<LoadingState>({
    isLoading: initialState,
    error: null
  })
}

// Utility function to create filtered atom
export function createFilteredAtom<T, F>(
  dataAtom: any,
  filtersAtom: any,
  filterFn: (data: T[], filters: F) => T[]
) {
  return atom((get) => {
    const data = get(dataAtom)
    const filters = get(filtersAtom)
    return data ? filterFn(data, filters) : []
  })
}

// Utility function to create computed/derived atoms
export function createComputedAtom<T, R>(
  sourceAtom: any,
  computeFn: (data: T) => R
) {
  return atom((get) => {
    const data = get(sourceAtom)
    return data ? computeFn(data) : null
  })
}

// Global error clearing atom
export const clearGlobalErrorAtom = atom(
  null,
  (_get, set) => {
    set(globalErrorAtom, null)
  }
)

// Global loading state management
export const setGlobalLoadingAtom = atom(
  null,
  (_get, set, isLoading: boolean) => {
    set(globalLoadingAtom, isLoading)
  }
)

// Utility to handle optimistic updates
export function createOptimisticUpdateAtom<T, Args extends any[]>(
  dataAtom: any,
  asyncFn: (...args: Args) => Promise<T>,
  optimisticUpdateFn: (currentData: T[], ...args: Args) => T[],
  errorCode: string = 'OPTIMISTIC_UPDATE_ERROR'
) {
  const { actionAtom, errorAtom, loadingAtom } = createAsyncAtom(asyncFn, errorCode)

  const optimisticActionAtom = atom(
    null,
    async (get, set, ...args: Args) => {
      const currentData = get(dataAtom) || []
      
      // Apply optimistic update
      const optimisticData = optimisticUpdateFn(currentData, ...args)
      set(dataAtom, optimisticData)

      try {
        // Perform actual async operation
        const result = await asyncFn(...args)
        return result
      } catch (error) {
        // Revert optimistic update on error
        set(dataAtom, currentData)
        const apiError: ApiError = {
          code: errorCode,
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          timestamp: new Date(),
          details: error instanceof Error ? { stack: error.stack } : { error }
        }
        set(errorAtom, apiError)
        set(globalErrorAtom, apiError)
        throw apiError
      }
    }
  )

  return {
    optimisticActionAtom,
    errorAtom,
    loadingAtom
  }
}

// Debounced atom utility for search/filter operations
export function createDebouncedAtom<T>(
  sourceAtom: any,
  delay: number = 300
) {
  const debouncedAtom = atom<T | null>(null)
  let timeoutId: NodeJS.Timeout | null = null

  return atom(
    (get) => get(debouncedAtom),
    (get, set, value: T) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      
      timeoutId = setTimeout(() => {
        set(debouncedAtom, value)
      }, delay)
    }
  )
}
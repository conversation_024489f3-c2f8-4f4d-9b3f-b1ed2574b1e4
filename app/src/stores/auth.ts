import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import { authClient } from '../lib/auth'
import type { Session, User, LoginCredentials, SignupData, AuthError } from '../types/auth'

// Internal atom to track the last known session for comparison
export const lastKnownSessionAtom = atom<Session | null>(null)

// Base atoms for authentication state
export const sessionAtom = atomWithStorage<Session | null>('auth-session', null)
export const isLoadingAtom = atom<boolean>(false)
export const authErrorAtom = atom<AuthError | null>(null)

// Derived atoms
export const isAuthenticatedAtom = atom((get) => {
  const session = get(sessionAtom)
  return session !== null && new Date(session.expiresAt) > new Date()
})

export const userAtom = atom((get) => {
  const session = get(sessionAtom)
  return session?.user || null
})

export const userRoleAtom = atom((get) => {
  const user = get(userAtom)
  return user?.role || 'user'
})

export const isAdminAtom = atom((get) => {
  const role = get(userRoleAtom)
  return role === 'admin'
})


export const isEmailVerifiedAtom = atom((get) => {
  const user = get(userAtom)
  return user?.emailVerified || false
})


// Action atoms for authentication operations
export const loginAtom = atom(
  null,
  async (_get, set, credentials: LoginCredentials) => {
    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      const response = await authClient.signIn.email({
        email: credentials.email,
        password: credentials.password,
      })
      const { data: sessionData } = await authClient.getSession();
      if (sessionData) {

        const session: Session = {
          id: sessionData.session.id,
          userId: sessionData.user.id,
          user: {
            ...sessionData.user,
          } as User,
          token: sessionData.session.token,
          createdAt: new Date(sessionData.session.createdAt),
          expiresAt: new Date(sessionData.session.expiresAt),
        }

        // const canCreateProject = await authClient.admin.hasPermission({
        //   userId: sessionData.user.id,
        //   permission: { "apiKey": ["create", "update"] }
        // })

        set(sessionAtom, session)
        return session
      } else {
        throw new Error(response.error?.message || 'Login failed')
      }
    } catch (error) {
      const authError: AuthError = {
        code: 'LOGIN_FAILED',
        message: error instanceof Error ? error.message : 'Login failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

export const signupAtom = atom(
  null,
  async (_get, set, signupData: SignupData) => {
    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      const response = await authClient.signUp.email({
        email: signupData.email,
        password: signupData.password,
        name: signupData.name,
      })

      if (response.data) {
        // After signup, get the actual session
        const sessionResponse = await authClient.getSession()

        if (sessionResponse.data?.session) {
          const session: Session = {
            id: sessionResponse.data.session.id,
            userId: sessionResponse.data.user.id,
            user: {
              ...sessionResponse.data.user,
              role: (sessionResponse.data.user.role as 'admin' | 'user' | 'whitelisted') || 'user'
            } as User,
            token: sessionResponse.data.session.token,
            createdAt: new Date(sessionResponse.data.session.createdAt),
            expiresAt: new Date(sessionResponse.data.session.expiresAt),
          }

          set(sessionAtom, session)
          return session
        } else {
          // Fallback: create a basic session from signup response
          const session: Session = {
            id: 'session-' + response.data.user.id,
            userId: response.data.user.id,
            user: {
              ...response.data.user,
              role: 'user' as const
            } as User,
            token: (response.data as any).token || '',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
            createdAt: new Date(),
          }

          set(sessionAtom, session)
          return session
        }
      } else {
        throw new Error(response.error?.message || 'Signup failed')
      }
    } catch (error) {
      const authError: AuthError = {
        code: 'SIGNUP_FAILED',
        message: error instanceof Error ? error.message : 'Signup failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

export const logoutAtom = atom(
  null,
  async (_get, set) => {
    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      await authClient.signOut()
      set(sessionAtom, null)
    } catch (error) {
      const authError: AuthError = {
        code: 'LOGOUT_FAILED',
        message: error instanceof Error ? error.message : 'Logout failed',
      }
      set(authErrorAtom, authError)
      // Still clear session even if logout request fails
      set(sessionAtom, null)
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

export const refreshSessionAtom = atom(
  null,
  async (get, set) => {
    const currentSession = get(sessionAtom)
    if (!currentSession) return null

    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      const response = await authClient.getSession()

      if (response.data) {
        const session: Session = {
          id: response.data.session?.id || 'session-' + response.data.user.id,
          userId: response.data.user.id,
          user: {
            ...response.data.user,
            role: (response.data.user.role as 'admin' | 'user' | 'whitelisted') || 'user'
          } as User,
          token: response.data.session?.token || '',
          expiresAt: response.data.session?.expiresAt ? new Date(response.data.session.expiresAt) : new Date(Date.now() + 24 * 60 * 60 * 1000),
          createdAt: response.data.session?.createdAt ? new Date(response.data.session.createdAt) : new Date(),
        }

        set(sessionAtom, session)
        return session
      } else {
        // Session is invalid, clear it
        set(sessionAtom, null)
        return null
      }
    } catch (error) {
      const authError: AuthError = {
        code: 'SESSION_REFRESH_FAILED',
        message: error instanceof Error ? error.message : 'Session refresh failed',
      }
      set(authErrorAtom, authError)
      set(sessionAtom, null)
      return null
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

// Utility atom to check if session needs refresh
export const shouldRefreshSessionAtom = atom((get) => {
  const session = get(sessionAtom)
  if (!session) return false

  const now = new Date()
  const expiresAt = new Date(session.expiresAt)
  const timeUntilExpiry = expiresAt.getTime() - now.getTime()

  // Refresh if session expires in less than 5 minutes
  return timeUntilExpiry < 5 * 60 * 1000
})

// Update user profile atom
export const updateUserProfileAtom = atom(
  null,
  async (get, set, profileData: Partial<User>) => {
    const currentSession = get(sessionAtom)
    if (!currentSession) {
      throw new Error('User not authenticated')
    }

    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      // Mock API call - replace with actual API call
      const updatedUser = { ...currentSession.user, ...profileData, updatedAt: new Date() }
      const updatedSession = { ...currentSession, user: updatedUser }

      set(sessionAtom, updatedSession)
      return updatedUser
    } catch (error) {
      const authError: AuthError = {
        code: 'PROFILE_UPDATE_FAILED',
        message: error instanceof Error ? error.message : 'Profile update failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

// Change password atom
export const changePasswordAtom = atom(
  null,
  async (get, set, _passwordData: { currentPassword: string; newPassword: string }) => {
    const currentSession = get(sessionAtom)
    if (!currentSession) {
      throw new Error('User not authenticated')
    }

    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      // Mock API call - replace with actual API call
      // In a real implementation, this would call the backend to change the password
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API delay

      return { success: true }
    } catch (error) {
      const authError: AuthError = {
        code: 'PASSWORD_CHANGE_FAILED',
        message: error instanceof Error ? error.message : 'Password change failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

// Update user preferences atom
export const updateUserPreferencesAtom = atom(
  null,
  async (get, set, preferences: any) => {
    const currentSession = get(sessionAtom)
    if (!currentSession) {
      throw new Error('User not authenticated')
    }

    set(isLoadingAtom, true)
    set(authErrorAtom, null)

    try {
      // Mock API call - replace with actual API call
      const updatedUser = {
        ...currentSession.user,
        preferences: { ...currentSession.user.preferences, ...preferences },
        updatedAt: new Date()
      }
      const updatedSession = { ...currentSession, user: updatedUser }

      set(sessionAtom, updatedSession)
      return updatedUser
    } catch (error) {
      const authError: AuthError = {
        code: 'PREFERENCES_UPDATE_FAILED',
        message: error instanceof Error ? error.message : 'Preferences update failed',
      }
      set(authErrorAtom, authError)
      throw authError
    } finally {
      set(isLoadingAtom, false)
    }
  }
)

export const clearAuthErrorAtom = atom(
  null,
  (_get, set) => {
    set(authErrorAtom, null)
  }
)

// Storage change detection atom
export const sessionStorageMonitorAtom = atom(
  null,
  async (get, set) => {
    const currentSession = get(sessionAtom)
    const lastKnown = get(lastKnownSessionAtom)

    // Check if session has changed externally (e.g., manual localStorage edit)
    if (JSON.stringify(currentSession) !== JSON.stringify(lastKnown)) {
      set(lastKnownSessionAtom, currentSession)

      // If session exists but seems modified, validate it by calling getSession
      if (currentSession) {
        try {
          const response = await authClient.getSession()

          if (response.data?.session) {
            const validatedSession: Session = {
              id: response.data.session.id,
              userId: response.data.user.id,
              user: {
                ...response.data.user,
                role: response.data.user.role as 'admin' | 'user' | 'whitelisted' || 'user'
              } as User,
              token: response.data.session.token,
              createdAt: new Date(response.data.session.createdAt),
              expiresAt: new Date(response.data.session.expiresAt),
            }

            // Update session if it's different from what's stored
            if (JSON.stringify(validatedSession) !== JSON.stringify(currentSession)) {
              set(sessionAtom, validatedSession)
            }
          } else {
            // Session is invalid, clear it
            set(sessionAtom, null)
          }
        } catch (error) {
          console.warn('Session validation failed:', error)
          // Don't clear session on network errors, only on auth errors
          if (error instanceof Error && error.message.includes('auth')) {
            set(sessionAtom, null)
          }
        }
      }
    }
  }
)


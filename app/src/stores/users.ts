import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'
import type { User } from '../types/auth'
import type { 
  UserFilters, 
  UserStats, 
  UserActivity, 
  UserManagementState,
  CreateUserRequest,
  UpdateUserRequest,
  UserActionRequest
} from '../types/user'
import type { PaginationParams } from '../types/api'
import { 
  createAsyncAtom, 
  createFilteredAtom, 
  createComputedAtom,
  createOptimisticUpdateAtom,
  createDebouncedAtom
} from './base'
import { apiClient } from '../lib/api'

// Base atoms for user management state
export const usersAtom = atom<User[]>([])
export const selectedUserAtom = atom<User | null>(null)
export const userStatsAtom = atom<UserStats | null>(null)
export const userActivitiesAtom = atom<UserActivity[]>([])

// Filters and pagination atoms
export const userFiltersAtom = atomWithStorage<UserFilters>('user-filters', {
  status: 'all',
  role: 'all',
  apiAccessLevel: 'all',
  search: '',
  dateRange: undefined
})

export const userPaginationAtom = atom<PaginationParams>({
  page: 1,
  limit: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc'
})

// Debounced search atom for better UX
export const debouncedSearchAtom = createDebouncedAtom<string>(userFiltersAtom, 300)

// Derived/computed atoms
export const filteredUsersAtom = createFilteredAtom<User, UserFilters>(
  usersAtom,
  userFiltersAtom,
  (users, filters) => {
    let filtered = [...users]

    // Filter by status
    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(user => user.status === filters.status)
    }

    // Filter by role
    if (filters.role && filters.role !== 'all') {
      filtered = filtered.filter(user => user.role === filters.role)
    }

    // Filter by API access level
    if (filters.apiAccessLevel && filters.apiAccessLevel !== 'all') {
      filtered = filtered.filter(user => user.apiAccessLevel === filters.apiAccessLevel)
    }

    // Filter by search term
    if (filters.search) {
      const search = filters.search.toLowerCase()
      filtered = filtered.filter(user => 
        user.name.toLowerCase().includes(search) ||
        user.email.toLowerCase().includes(search)
      )
    }

    // Filter by date range
    if (filters.dateRange) {
      filtered = filtered.filter(user => {
        const userDate = new Date(user.createdAt)
        return userDate >= filters.dateRange!.start && userDate <= filters.dateRange!.end
      })
    }

    return filtered
  }
)

export const sortedUsersAtom = atom((get) => {
  const users = get(filteredUsersAtom)
  const pagination = get(userPaginationAtom)

  if (!pagination.sortBy) return users

  return [...users].sort((a, b) => {
    const aValue = a[pagination.sortBy as keyof User]
    const bValue = b[pagination.sortBy as keyof User]

    if (aValue === undefined || bValue === undefined) return 0

    let comparison = 0
    if (aValue < bValue) comparison = -1
    if (aValue > bValue) comparison = 1

    return pagination.sortOrder === 'desc' ? -comparison : comparison
  })
})

export const paginatedUsersAtom = atom((get) => {
  const users = get(sortedUsersAtom)
  const pagination = get(userPaginationAtom)

  const startIndex = (pagination.page - 1) * pagination.limit
  const endIndex = startIndex + pagination.limit

  return users.slice(startIndex, endIndex)
})

// User statistics derived atoms
export const userStatsSummaryAtom = createComputedAtom<User[], {
  totalUsers: number
  pendingApprovals: number
  activeUsers: number
  adminUsers: number
  recentSignups: number
}>(
  usersAtom,
  (users) => {
    const now = new Date()
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    return {
      totalUsers: users.length,
      pendingApprovals: users.filter(u => u.status === 'pending').length,
      activeUsers: users.filter(u => u.status === 'approved').length,
      adminUsers: users.filter(u => u.role === 'admin').length,
      recentSignups: users.filter(u => new Date(u.createdAt) >= weekAgo).length
    }
  }
)

// Async action atoms for user CRUD operations

// Fetch users with filters and pagination
export const fetchUsersAtom = createAsyncAtom(
  async (filters?: UserFilters, page = 1, limit = 10) => {
    const response = await apiClient.getUsers(filters, page, limit)
    return response
  },
  'FETCH_USERS_ERROR'
)

// Create new user
export const createUserAtom = createOptimisticUpdateAtom(
  usersAtom,
  async (userData: CreateUserRequest) => {
    const newUser = await apiClient.createUser(userData)
    return newUser
  },
  (currentUsers, userData) => [
    ...currentUsers,
    {
      id: `temp-${Date.now()}`,
      email: userData.email,
      name: userData.name,
      role: userData.role,
      status: 'pending' as const,
      createdAt: new Date(),
      updatedAt: new Date(),
      apiAccessLevel: userData.apiAccessLevel
    }
  ],
  'CREATE_USER_ERROR'
)

// Update user
export const updateUserAtom = createOptimisticUpdateAtom(
  usersAtom,
  async (userData: UpdateUserRequest) => {
    const updatedUser = await apiClient.updateUser(userData)
    return updatedUser
  },
  (currentUsers, userData) => 
    currentUsers.map(user => 
      user.id === userData.id 
        ? { ...user, ...userData, updatedAt: new Date() }
        : user
    ),
  'UPDATE_USER_ERROR'
)

// User action operations (approve, revoke, etc.)
export const performUserActionAtom = createOptimisticUpdateAtom(
  usersAtom,
  async (actionRequest: UserActionRequest) => {
    const updatedUser = await apiClient.performUserAction(actionRequest)
    return updatedUser
  },
  (currentUsers, actionRequest) => 
    currentUsers.map(user => {
      if (user.id !== actionRequest.userId) return user
      
      const updates: Partial<User> = { updatedAt: new Date() }
      
      switch (actionRequest.action) {
        case 'approve':
          updates.status = 'approved'
          break
        case 'revoke':
          updates.status = 'revoked'
          break
        case 'promote':
          updates.role = 'admin'
          break
        case 'demote':
          updates.role = 'user'
          break
      }
      
      return { ...user, ...updates }
    }),
  'USER_ACTION_ERROR'
)

// Delete user
export const deleteUserAtom = createOptimisticUpdateAtom(
  usersAtom,
  async (userId: string) => {
    await apiClient.deleteUser(userId)
    return userId
  },
  (currentUsers, userId) => currentUsers.filter(user => user.id !== userId),
  'DELETE_USER_ERROR'
)

// Fetch user statistics
export const fetchUserStatsAtom = createAsyncAtom(
  async () => {
    const stats = await apiClient.getUserStats()
    return stats
  },
  'FETCH_USER_STATS_ERROR'
)

// Fetch user activities
export const fetchUserActivitiesAtom = createAsyncAtom(
  async (userId?: string, limit = 50) => {
    const activities = await apiClient.getUserActivities(userId, limit)
    return activities
  },
  'FETCH_USER_ACTIVITIES_ERROR'
)

// Fetch single user by ID
export const fetchUserByIdAtom = createAsyncAtom(
  async (userId: string) => {
    const user = await apiClient.getUserById(userId)
    return user
  },
  'FETCH_USER_BY_ID_ERROR'
)

// Combined action atoms that update multiple states

// Load users with stats
export const loadUsersWithStatsAtom = atom(
  null,
  async (get, set, filters?: UserFilters, page = 1, limit = 10) => {
    // Fetch users and stats in parallel
    const [usersResult, statsResult] = await Promise.allSettled([
      get(fetchUsersAtom.actionAtom)(filters, page, limit),
      get(fetchUserStatsAtom.actionAtom)()
    ])

    if (usersResult.status === 'fulfilled') {
      set(usersAtom, usersResult.value.data)
      set(userPaginationAtom, prev => ({
        ...prev,
        page: usersResult.value.page,
        limit: usersResult.value.limit
      }))
    }

    if (statsResult.status === 'fulfilled') {
      set(userStatsAtom, statsResult.value)
    }

    // Return any errors
    const errors = []
    if (usersResult.status === 'rejected') errors.push(usersResult.reason)
    if (statsResult.status === 'rejected') errors.push(statsResult.reason)
    
    return { errors }
  }
)

// Refresh user data
export const refreshUserDataAtom = atom(
  null,
  async (get, set) => {
    const filters = get(userFiltersAtom)
    const pagination = get(userPaginationAtom)
    
    await get(loadUsersWithStatsAtom)(filters, pagination.page, pagination.limit)
  }
)

// Filter update atoms
export const updateUserFiltersAtom = atom(
  null,
  (get, set, updates: Partial<UserFilters>) => {
    const currentFilters = get(userFiltersAtom)
    const newFilters = { ...currentFilters, ...updates }
    set(userFiltersAtom, newFilters)
    
    // Reset pagination when filters change
    set(userPaginationAtom, prev => ({ ...prev, page: 1 }))
  }
)

export const updateUserPaginationAtom = atom(
  null,
  (get, set, updates: Partial<PaginationParams>) => {
    const currentPagination = get(userPaginationAtom)
    set(userPaginationAtom, { ...currentPagination, ...updates })
  }
)

// Clear selected user
export const clearSelectedUserAtom = atom(
  null,
  (_get, set) => {
    set(selectedUserAtom, null)
  }
)

// Select user and load activities
export const selectUserWithActivitiesAtom = atom(
  null,
  async (get, set, user: User) => {
    set(selectedUserAtom, user)
    
    // Load user activities
    try {
      const activities = await get(fetchUserActivitiesAtom.actionAtom)(user.id)
      set(userActivitiesAtom, activities)
    } catch (error) {
      console.error('Failed to load user activities:', error)
    }
  }
)
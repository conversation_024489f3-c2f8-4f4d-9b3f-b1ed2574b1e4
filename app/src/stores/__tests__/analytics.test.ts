import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createStore } from 'jotai'
import type { Store } from 'jotai'
import type { 
  ApiUsageLog, 
  UsageMetrics, 
  OllamaPerformanceMetrics, 
  SystemMetrics,
  AnalyticsFilters,
  AnalyticsQuery,
  TrendData
} from '../../types/analytics'
import type { User } from '../../types/auth'
import type { TimeRange } from '../../types/api'

// Mock the API client
const mockApiClient = {
  getUsageLogs: vi.fn(),
  getUsageMetrics: vi.fn(),
  getOllamaMetrics: vi.fn(),
  getSystemMetrics: vi.fn(),
  getTrendData: vi.fn()
}

vi.mock('../../lib/api', () => ({
  apiClient: mockApiClient
}))

// Import after mocking
const {
  usageLogs<PERSON>tom,
  usageMetricsAtom,
  systemMetricsAtom,
  ollamaMetricsAtom,
  requestTrendsAtom,
  responseTimeTrendsAtom,
  errorTrendsAtom,
  userTrendsAtom,
  analyticsFiltersAtom,
  userAnalyticsAtom,
  filteredUsageLogsAtom,
  sortedUsageLogsAtom,
  analyticsStatsSummaryAtom,
  logsByEndpointAtom,
  logsByStatusCodeAtom,
  recentErrorsAtom,
  performanceMetricsAtom,
  fetchUsageLogsAtom,
  fetchUsageMetricsAtom,
  fetchOllamaMetricsAtom,
  fetchSystemMetricsAtom,
  fetchTrendDataAtom,
  loadAnalyticsDashboardAtom,
  loadTrendDataAtom,
  loadUserAnalyticsAtom,
  loadApiKeyAnalyticsAtom,
  updateAnalyticsFiltersAtom,
  setTimeRangePresetAtom,
  enableRealTimeUpdatesAtom,
  realTimeUpdateAtom,
  toggleRealTimeUpdatesAtom
} = await import('../analytics')

const { userAtom } = await import('../auth')

describe('Analytics Store', () => {
  let store: Store

  const mockUser: User = {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user',
    status: 'approved',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    apiAccessLevel: 'basic'
  }

  const mockUsageLogs: ApiUsageLog[] = [
    {
      id: 'log-1',
      userId: 'user-1',
      apiKeyId: 'key-1',
      endpoint: '/api/ollama/generate',
      method: 'POST',
      statusCode: 200,
      responseTime: 250,
      requestSize: 1024,
      responseSize: 2048,
      timestamp: new Date('2024-01-01T10:00:00Z'),
      ipAddress: '***********',
      userAgent: 'Test Client/1.0'
    },
    {
      id: 'log-2',
      userId: 'user-1',
      apiKeyId: 'key-1',
      endpoint: '/api/ollama/chat',
      method: 'POST',
      statusCode: 200,
      responseTime: 180,
      requestSize: 512,
      responseSize: 1024,
      timestamp: new Date('2024-01-01T11:00:00Z'),
      ipAddress: '***********',
      userAgent: 'Test Client/1.0'
    },
    {
      id: 'log-3',
      userId: 'user-2',
      apiKeyId: 'key-2',
      endpoint: '/api/ollama/generate',
      method: 'POST',
      statusCode: 500,
      responseTime: 100,
      requestSize: 1024,
      responseSize: 256,
      timestamp: new Date('2024-01-01T12:00:00Z'),
      errorMessage: 'Internal server error'
    },
    {
      id: 'log-4',
      userId: 'user-1',
      apiKeyId: 'key-1',
      endpoint: '/api/ollama/models',
      method: 'GET',
      statusCode: 404,
      responseTime: 50,
      requestSize: 0,
      responseSize: 128,
      timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
      errorMessage: 'Model not found'
    }
  ]

  const mockUsageMetrics: UsageMetrics = {
    userId: 'user-1',
    period: {
      start: new Date('2024-01-01'),
      end: new Date('2024-01-31')
    },
    totalRequests: 1000,
    successfulRequests: 950,
    failedRequests: 50,
    averageResponseTime: 215,
    totalDataTransferred: 1024000,
    requestsPerHour: new Array(24).fill(0).map((_, i) => Math.floor(Math.random() * 50)),
    topEndpoints: [
      {
        endpoint: '/api/ollama/generate',
        count: 600,
        averageResponseTime: 250,
        errorRate: 0.02
      },
      {
        endpoint: '/api/ollama/chat',
        count: 300,
        averageResponseTime: 180,
        errorRate: 0.01
      }
    ],
    errorBreakdown: [
      { statusCode: 500, count: 30, percentage: 60 },
      { statusCode: 404, count: 15, percentage: 30 },
      { statusCode: 429, count: 5, percentage: 10 }
    ],
    responseTimeDistribution: [
      { range: '0-100ms', count: 200, percentage: 20 },
      { range: '100-500ms', count: 700, percentage: 70 },
      { range: '500ms+', count: 100, percentage: 10 }
    ]
  }

  const mockOllamaMetrics: OllamaPerformanceMetrics[] = [
    {
      modelName: 'llama2',
      totalRequests: 500,
      averageResponseTime: 300,
      tokensPerSecond: 25,
      successRate: 0.95,
      errorRate: 0.05,
      peakUsageTime: new Date('2024-01-01T14:00:00Z'),
      resourceUtilization: {
        cpu: 75,
        memory: 60,
        gpu: 85
      },
      concurrentRequests: 5,
      queueLength: 2
    },
    {
      modelName: 'codellama',
      totalRequests: 300,
      averageResponseTime: 450,
      tokensPerSecond: 20,
      successRate: 0.92,
      errorRate: 0.08,
      peakUsageTime: new Date('2024-01-01T16:00:00Z'),
      resourceUtilization: {
        cpu: 80,
        memory: 70,
        gpu: 90
      },
      concurrentRequests: 3,
      queueLength: 1
    }
  ]

  const mockSystemMetrics: SystemMetrics[] = [
    {
      timestamp: new Date('2024-01-01T10:00:00Z'),
      activeUsers: 25,
      totalApiCalls: 1500,
      averageResponseTime: 250,
      errorRate: 0.05,
      systemLoad: {
        cpu: 65,
        memory: 70,
        disk: 45
      },
      ollamaMetrics: mockOllamaMetrics
    },
    {
      timestamp: new Date('2024-01-01T11:00:00Z'),
      activeUsers: 30,
      totalApiCalls: 1800,
      averageResponseTime: 220,
      errorRate: 0.03,
      systemLoad: {
        cpu: 70,
        memory: 75,
        disk: 50
      },
      ollamaMetrics: mockOllamaMetrics
    }
  ]

  const mockTrendData: TrendData[] = [
    { timestamp: new Date('2024-01-01T00:00:00Z'), value: 100, label: 'Jan 1' },
    { timestamp: new Date('2024-01-02T00:00:00Z'), value: 150, label: 'Jan 2' },
    { timestamp: new Date('2024-01-03T00:00:00Z'), value: 120, label: 'Jan 3' },
    { timestamp: new Date('2024-01-04T00:00:00Z'), value: 180, label: 'Jan 4' }
  ]

  beforeEach(() => {
    store = createStore()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Base atoms', () => {
    it('should initialize with empty usage logs array', () => {
      const logs = store.get(usageLogsAtom)
      expect(logs).toEqual([])
    })

    it('should initialize with null usage metrics', () => {
      const metrics = store.get(usageMetricsAtom)
      expect(metrics).toBeNull()
    })

    it('should initialize with empty system metrics array', () => {
      const metrics = store.get(systemMetricsAtom)
      expect(metrics).toEqual([])
    })

    it('should initialize with empty ollama metrics array', () => {
      const metrics = store.get(ollamaMetricsAtom)
      expect(metrics).toEqual([])
    })

    it('should initialize with empty trend data arrays', () => {
      expect(store.get(requestTrendsAtom)).toEqual([])
      expect(store.get(responseTimeTrendsAtom)).toEqual([])
      expect(store.get(errorTrendsAtom)).toEqual([])
      expect(store.get(userTrendsAtom)).toEqual([])
    })
  })

  describe('Filter atoms', () => {
    it('should initialize with default filters', () => {
      const filters = store.get(analyticsFiltersAtom)
      expect(filters.search).toBe('')
      expect(filters.timeRange.start).toBeInstanceOf(Date)
      expect(filters.timeRange.end).toBeInstanceOf(Date)
    })

    it('should update filters correctly', () => {
      const newFilters: Partial<AnalyticsFilters> = {
        userId: 'user-1',
        endpoint: '/api/ollama/generate',
        method: 'POST'
      }

      store.set(updateAnalyticsFiltersAtom, newFilters)

      const filters = store.get(analyticsFiltersAtom)
      expect(filters.userId).toBe('user-1')
      expect(filters.endpoint).toBe('/api/ollama/generate')
      expect(filters.method).toBe('POST')
    })
  })

  describe('Derived atoms', () => {
    beforeEach(() => {
      store.set(usageLogsAtom, mockUsageLogs)
      store.set(userAtom, mockUser)
    })

    describe('userAnalyticsAtom', () => {
      it('should return logs for current user only', () => {
        const userLogs = store.get(userAnalyticsAtom)
        expect(userLogs).toHaveLength(3)
        expect(userLogs.every(log => log.userId === 'user-1')).toBe(true)
      })

      it('should return empty array when no user', () => {
        store.set(userAtom, null)
        const userLogs = store.get(userAnalyticsAtom)
        expect(userLogs).toEqual([])
      })
    })

    describe('filteredUsageLogsAtom', () => {
      it('should return all logs when no filters applied', () => {
        const filtered = store.get(filteredUsageLogsAtom)
        expect(filtered).toEqual(mockUsageLogs)
      })

      it('should filter by user ID', () => {
        store.set(analyticsFiltersAtom, { 
          ...store.get(analyticsFiltersAtom), 
          userId: 'user-1' 
        })
        
        const filtered = store.get(filteredUsageLogsAtom)
        expect(filtered).toHaveLength(3)
        expect(filtered.every(log => log.userId === 'user-1')).toBe(true)
      })

      it('should filter by API key ID', () => {
        store.set(analyticsFiltersAtom, { 
          ...store.get(analyticsFiltersAtom), 
          apiKeyId: 'key-1' 
        })
        
        const filtered = store.get(filteredUsageLogsAtom)
        expect(filtered).toHaveLength(3)
        expect(filtered.every(log => log.apiKeyId === 'key-1')).toBe(true)
      })

      it('should filter by endpoint', () => {
        store.set(analyticsFiltersAtom, { 
          ...store.get(analyticsFiltersAtom), 
          endpoint: '/api/ollama/generate' 
        })
        
        const filtered = store.get(filteredUsageLogsAtom)
        expect(filtered).toHaveLength(2)
        expect(filtered.every(log => log.endpoint.includes('/api/ollama/generate'))).toBe(true)
      })

      it('should filter by method', () => {
        store.set(analyticsFiltersAtom, { 
          ...store.get(analyticsFiltersAtom), 
          method: 'GET' 
        })
        
        const filtered = store.get(filteredUsageLogsAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].method).toBe('GET')
      })

      it('should filter by status code', () => {
        store.set(analyticsFiltersAtom, { 
          ...store.get(analyticsFiltersAtom), 
          statusCode: 200 
        })
        
        const filtered = store.get(filteredUsageLogsAtom)
        expect(filtered).toHaveLength(2)
        expect(filtered.every(log => log.statusCode === 200)).toBe(true)
      })

      it('should filter by time range', () => {
        const timeRange: TimeRange = {
          start: new Date('2024-01-01T09:00:00Z'),
          end: new Date('2024-01-01T11:30:00Z')
        }
        store.set(analyticsFiltersAtom, { 
          ...store.get(analyticsFiltersAtom), 
          timeRange 
        })
        
        const filtered = store.get(filteredUsageLogsAtom)
        expect(filtered).toHaveLength(2) // log-1 and log-2
      })

      it('should filter by search term', () => {
        store.set(analyticsFiltersAtom, { 
          ...store.get(analyticsFiltersAtom), 
          search: 'chat' 
        })
        
        const filtered = store.get(filteredUsageLogsAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].endpoint).toContain('chat')
      })
    })

    describe('sortedUsageLogsAtom', () => {
      it('should sort logs by timestamp (newest first)', () => {
        const sorted = store.get(sortedUsageLogsAtom)
        expect(sorted[0].id).toBe('log-3') // Most recent
        expect(sorted[sorted.length - 1].id).toBe('log-4') // Oldest
      })
    })

    describe('analyticsStatsSummaryAtom', () => {
      it('should calculate analytics statistics correctly', () => {
        const stats = store.get(analyticsStatsSummaryAtom)
        
        expect(stats.totalRequests).toBe(4)
        expect(stats.successfulRequests).toBe(2) // status < 400
        expect(stats.failedRequests).toBe(2) // status >= 400
        expect(stats.averageResponseTime).toBe(145) // (250+180+100+50)/4
        expect(stats.totalDataTransferred).toBe(5632) // sum of request + response sizes
        expect(stats.uniqueUsers).toBe(2)
        expect(stats.uniqueEndpoints).toBe(3)
        expect(stats.errorRate).toBe(0.5)
        expect(stats.successRate).toBe(0.5)
      })

      it('should handle empty logs array', () => {
        store.set(usageLogsAtom, [])
        
        const stats = store.get(analyticsStatsSummaryAtom)
        expect(stats.totalRequests).toBe(0)
        expect(stats.successfulRequests).toBe(0)
        expect(stats.failedRequests).toBe(0)
        expect(stats.averageResponseTime).toBe(0)
        expect(stats.totalDataTransferred).toBe(0)
        expect(stats.uniqueUsers).toBe(0)
        expect(stats.uniqueEndpoints).toBe(0)
        expect(stats.errorRate).toBe(0)
        expect(stats.successRate).toBe(0)
      })
    })

    describe('logsByEndpointAtom', () => {
      it('should group logs by endpoint', () => {
        const grouped = store.get(logsByEndpointAtom)
        
        expect(grouped['/api/ollama/generate']).toHaveLength(2)
        expect(grouped['/api/ollama/chat']).toHaveLength(1)
        expect(grouped['/api/ollama/models']).toHaveLength(1)
      })
    })

    describe('logsByStatusCodeAtom', () => {
      it('should group logs by status code', () => {
        const grouped = store.get(logsByStatusCodeAtom)
        
        expect(grouped[200]).toHaveLength(2)
        expect(grouped[500]).toHaveLength(1)
        expect(grouped[404]).toHaveLength(1)
      })
    })

    describe('recentErrorsAtom', () => {
      it('should return recent errors (last 24 hours)', () => {
        const recentErrors = store.get(recentErrorsAtom)
        expect(recentErrors).toHaveLength(1) // Only log-3 is within 24 hours and has error
        expect(recentErrors[0].statusCode).toBeGreaterThanOrEqual(400)
      })
    })

    describe('performanceMetricsAtom', () => {
      it('should calculate performance metrics correctly', () => {
        const metrics = store.get(performanceMetricsAtom)
        
        // Response times: [50, 100, 180, 250] sorted
        expect(metrics.p50ResponseTime).toBe(100) // 50th percentile
        expect(metrics.p95ResponseTime).toBe(250) // 95th percentile
        expect(metrics.p99ResponseTime).toBe(250) // 99th percentile
        
        expect(metrics.slowestEndpoints).toBeDefined()
        expect(metrics.fastestEndpoints).toBeDefined()
      })

      it('should handle empty logs for performance metrics', () => {
        store.set(usageLogsAtom, [])
        
        const metrics = store.get(performanceMetricsAtom)
        expect(metrics.p50ResponseTime).toBe(0)
        expect(metrics.p95ResponseTime).toBe(0)
        expect(metrics.p99ResponseTime).toBe(0)
        expect(metrics.slowestEndpoints).toEqual([])
        expect(metrics.fastestEndpoints).toEqual([])
      })
    })
  })

  describe('Async action atoms', () => {
    describe('fetchUsageLogsAtom', () => {
      it('should fetch usage logs successfully', async () => {
        const filters: AnalyticsFilters = {
          timeRange: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31')
          },
          search: ''
        }
        mockApiClient.getUsageLogs.mockResolvedValue(mockUsageLogs)

        const result = await store.set(fetchUsageLogsAtom.actionAtom, filters, 100)

        expect(mockApiClient.getUsageLogs).toHaveBeenCalledWith(filters, 100)
        expect(result).toEqual(mockUsageLogs)
      })

      it('should handle fetch error', async () => {
        const error = new Error('Failed to fetch usage logs')
        mockApiClient.getUsageLogs.mockRejectedValue(error)

        const filters: AnalyticsFilters = {
          timeRange: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31')
          },
          search: ''
        }

        await expect(
          store.set(fetchUsageLogsAtom.actionAtom, filters, 100)
        ).rejects.toThrow('Failed to fetch usage logs')

        expect(store.get(fetchUsageLogsAtom.errorAtom)).toBeTruthy()
      })
    })

    describe('fetchUsageMetricsAtom', () => {
      it('should fetch usage metrics successfully', async () => {
        const query: AnalyticsQuery = {
          timeRange: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31')
          },
          granularity: 'day',
          metrics: ['requests', 'responseTime']
        }
        mockApiClient.getUsageMetrics.mockResolvedValue(mockUsageMetrics)

        const result = await store.set(fetchUsageMetricsAtom.actionAtom, query)

        expect(mockApiClient.getUsageMetrics).toHaveBeenCalledWith(query)
        expect(result).toEqual(mockUsageMetrics)
      })
    })

    describe('fetchOllamaMetricsAtom', () => {
      it('should fetch Ollama metrics successfully', async () => {
        const timeRange: TimeRange = {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        }
        mockApiClient.getOllamaMetrics.mockResolvedValue(mockOllamaMetrics)

        const result = await store.set(fetchOllamaMetricsAtom.actionAtom, timeRange)

        expect(mockApiClient.getOllamaMetrics).toHaveBeenCalledWith(timeRange)
        expect(result).toEqual(mockOllamaMetrics)
      })
    })

    describe('fetchSystemMetricsAtom', () => {
      it('should fetch system metrics successfully', async () => {
        const timeRange: TimeRange = {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        }
        mockApiClient.getSystemMetrics.mockResolvedValue(mockSystemMetrics)

        const result = await store.set(fetchSystemMetricsAtom.actionAtom, timeRange)

        expect(mockApiClient.getSystemMetrics).toHaveBeenCalledWith(timeRange)
        expect(result).toEqual(mockSystemMetrics)
      })
    })

    describe('fetchTrendDataAtom', () => {
      it('should fetch trend data successfully', async () => {
        const timeRange: TimeRange = {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        }
        mockApiClient.getTrendData.mockResolvedValue(mockTrendData)

        const result = await store.set(fetchTrendDataAtom.actionAtom, 'requests', timeRange, 'day')

        expect(mockApiClient.getTrendData).toHaveBeenCalledWith('requests', timeRange, 'day')
        expect(result).toEqual(mockTrendData)
      })
    })
  })

  describe('Combined action atoms', () => {
    describe('loadAnalyticsDashboardAtom', () => {
      it('should load complete analytics dashboard data', async () => {
        const filters: AnalyticsFilters = {
          timeRange: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31')
          },
          search: ''
        }

        mockApiClient.getUsageLogs.mockResolvedValue(mockUsageLogs)
        mockApiClient.getUsageMetrics.mockResolvedValue(mockUsageMetrics)
        mockApiClient.getOllamaMetrics.mockResolvedValue(mockOllamaMetrics)
        mockApiClient.getSystemMetrics.mockResolvedValue(mockSystemMetrics)

        const result = await store.set(loadAnalyticsDashboardAtom, filters)

        expect(mockApiClient.getUsageLogs).toHaveBeenCalled()
        expect(mockApiClient.getUsageMetrics).toHaveBeenCalled()
        expect(mockApiClient.getOllamaMetrics).toHaveBeenCalled()
        expect(mockApiClient.getSystemMetrics).toHaveBeenCalled()

        expect(store.get(usageLogsAtom)).toEqual(mockUsageLogs)
        expect(store.get(usageMetricsAtom)).toEqual(mockUsageMetrics)
        expect(store.get(ollamaMetricsAtom)).toEqual(mockOllamaMetrics)
        expect(store.get(systemMetricsAtom)).toEqual(mockSystemMetrics)
        expect(result.errors).toEqual([])
      })

      it('should handle partial failures', async () => {
        const filters: AnalyticsFilters = {
          timeRange: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31')
          },
          search: ''
        }

        mockApiClient.getUsageLogs.mockResolvedValue(mockUsageLogs)
        mockApiClient.getUsageMetrics.mockRejectedValue(new Error('Metrics failed'))
        mockApiClient.getOllamaMetrics.mockResolvedValue(mockOllamaMetrics)
        mockApiClient.getSystemMetrics.mockRejectedValue(new Error('System metrics failed'))

        const result = await store.set(loadAnalyticsDashboardAtom, filters)

        expect(store.get(usageLogsAtom)).toEqual(mockUsageLogs)
        expect(store.get(usageMetricsAtom)).toBeNull()
        expect(store.get(ollamaMetricsAtom)).toEqual(mockOllamaMetrics)
        expect(store.get(systemMetricsAtom)).toEqual([])
        expect(result.errors).toHaveLength(2)
      })
    })

    describe('loadTrendDataAtom', () => {
      it('should load trend data for all metrics', async () => {
        const timeRange: TimeRange = {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        }

        mockApiClient.getTrendData.mockResolvedValue(mockTrendData)

        const result = await store.set(loadTrendDataAtom, timeRange, 'day')

        expect(mockApiClient.getTrendData).toHaveBeenCalledTimes(4) // requests, responseTime, errors, users
        expect(store.get(requestTrendsAtom)).toEqual(mockTrendData)
        expect(store.get(responseTimeTrendsAtom)).toEqual(mockTrendData)
        expect(store.get(errorTrendsAtom)).toEqual(mockTrendData)
        expect(store.get(userTrendsAtom)).toEqual(mockTrendData)
        expect(result.errors).toEqual([])
      })
    })

    describe('loadUserAnalyticsAtom', () => {
      beforeEach(() => {
        store.set(userAtom, mockUser)
      })

      it('should load analytics for current user', async () => {
        mockApiClient.getUsageLogs.mockResolvedValue(mockUsageLogs.filter(log => log.userId === 'user-1'))
        mockApiClient.getUsageMetrics.mockResolvedValue(mockUsageMetrics)
        mockApiClient.getOllamaMetrics.mockResolvedValue(mockOllamaMetrics)
        mockApiClient.getSystemMetrics.mockResolvedValue(mockSystemMetrics)

        await store.set(loadUserAnalyticsAtom)

        expect(mockApiClient.getUsageLogs).toHaveBeenCalledWith(
          expect.objectContaining({ userId: 'user-1' }),
          1000
        )
      })

      it('should load analytics for specific user', async () => {
        mockApiClient.getUsageLogs.mockResolvedValue(mockUsageLogs.filter(log => log.userId === 'user-2'))
        mockApiClient.getUsageMetrics.mockResolvedValue(mockUsageMetrics)
        mockApiClient.getOllamaMetrics.mockResolvedValue(mockOllamaMetrics)
        mockApiClient.getSystemMetrics.mockResolvedValue(mockSystemMetrics)

        await store.set(loadUserAnalyticsAtom, 'user-2')

        expect(mockApiClient.getUsageLogs).toHaveBeenCalledWith(
          expect.objectContaining({ userId: 'user-2' }),
          1000
        )
      })

      it('should throw error when no user ID available', async () => {
        store.set(userAtom, null)

        await expect(store.set(loadUserAnalyticsAtom)).rejects.toThrow('User ID required')
      })
    })

    describe('loadApiKeyAnalyticsAtom', () => {
      it('should load analytics for specific API key', async () => {
        mockApiClient.getUsageLogs.mockResolvedValue(mockUsageLogs.filter(log => log.apiKeyId === 'key-1'))
        mockApiClient.getUsageMetrics.mockResolvedValue(mockUsageMetrics)
        mockApiClient.getOllamaMetrics.mockResolvedValue(mockOllamaMetrics)
        mockApiClient.getSystemMetrics.mockResolvedValue(mockSystemMetrics)

        await store.set(loadApiKeyAnalyticsAtom, 'key-1')

        expect(mockApiClient.getUsageLogs).toHaveBeenCalledWith(
          expect.objectContaining({ apiKeyId: 'key-1' }),
          1000
        )
      })
    })
  })

  describe('Time range preset atoms', () => {
    describe('setTimeRangePresetAtom', () => {
      it('should set hour preset correctly', () => {
        store.set(setTimeRangePresetAtom, 'hour')
        
        const filters = store.get(analyticsFiltersAtom)
        const timeDiff = filters.timeRange.end.getTime() - filters.timeRange.start.getTime()
        expect(timeDiff).toBe(60 * 60 * 1000) // 1 hour
      })

      it('should set day preset correctly', () => {
        store.set(setTimeRangePresetAtom, 'day')
        
        const filters = store.get(analyticsFiltersAtom)
        const timeDiff = filters.timeRange.end.getTime() - filters.timeRange.start.getTime()
        expect(timeDiff).toBe(24 * 60 * 60 * 1000) // 1 day
      })

      it('should set week preset correctly', () => {
        store.set(setTimeRangePresetAtom, 'week')
        
        const filters = store.get(analyticsFiltersAtom)
        const timeDiff = filters.timeRange.end.getTime() - filters.timeRange.start.getTime()
        expect(timeDiff).toBe(7 * 24 * 60 * 60 * 1000) // 1 week
      })

      it('should set month preset correctly', () => {
        store.set(setTimeRangePresetAtom, 'month')
        
        const filters = store.get(analyticsFiltersAtom)
        const timeDiff = filters.timeRange.end.getTime() - filters.timeRange.start.getTime()
        expect(timeDiff).toBe(30 * 24 * 60 * 60 * 1000) // 30 days
      })
    })
  })

  describe('Real-time updates', () => {
    describe('enableRealTimeUpdatesAtom', () => {
      it('should initialize with real-time updates disabled', () => {
        const enabled = store.get(enableRealTimeUpdatesAtom)
        expect(enabled).toBe(false)
      })
    })

    describe('toggleRealTimeUpdatesAtom', () => {
      it('should toggle real-time updates', () => {
        expect(store.get(enableRealTimeUpdatesAtom)).toBe(false)
        
        store.set(toggleRealTimeUpdatesAtom)
        expect(store.get(enableRealTimeUpdatesAtom)).toBe(true)
        
        store.set(toggleRealTimeUpdatesAtom)
        expect(store.get(enableRealTimeUpdatesAtom)).toBe(false)
      })
    })

    describe('realTimeUpdateAtom', () => {
      it('should not add logs when real-time updates disabled', async () => {
        store.set(usageLogsAtom, mockUsageLogs)
        store.set(enableRealTimeUpdatesAtom, false)

        await store.set(realTimeUpdateAtom)

        const logs = store.get(usageLogsAtom)
        expect(logs).toEqual(mockUsageLogs) // Should remain unchanged
      })

      it('should add new log when real-time updates enabled', async () => {
        store.set(usageLogsAtom, mockUsageLogs)
        store.set(enableRealTimeUpdatesAtom, true)

        await store.set(realTimeUpdateAtom)

        const logs = store.get(usageLogsAtom)
        expect(logs.length).toBe(mockUsageLogs.length + 1)
        expect(logs[0].id).toMatch(/^realtime-/)
      })

      it('should limit logs to 1000 entries', async () => {
        const manyLogs = new Array(1000).fill(null).map((_, i) => ({
          ...mockUsageLogs[0],
          id: `log-${i}`,
          timestamp: new Date(Date.now() - i * 1000)
        }))
        
        store.set(usageLogsAtom, manyLogs)
        store.set(enableRealTimeUpdatesAtom, true)

        await store.set(realTimeUpdateAtom)

        const logs = store.get(usageLogsAtom)
        expect(logs.length).toBe(1000) // Should not exceed 1000
        expect(logs[0].id).toMatch(/^realtime-/) // New log should be first
      })
    })
  })

  describe('Integration scenarios', () => {
    beforeEach(() => {
      store.set(userAtom, mockUser)
    })

    it('should handle complete analytics workflow', async () => {
      // Load initial analytics data
      mockApiClient.getUsageLogs.mockResolvedValue(mockUsageLogs)
      mockApiClient.getUsageMetrics.mockResolvedValue(mockUsageMetrics)
      mockApiClient.getOllamaMetrics.mockResolvedValue(mockOllamaMetrics)
      mockApiClient.getSystemMetrics.mockResolvedValue(mockSystemMetrics)

      await store.set(loadAnalyticsDashboardAtom)

      expect(store.get(usageLogsAtom)).toEqual(mockUsageLogs)
      expect(store.get(analyticsStatsSummaryAtom).totalRequests).toBe(4)

      // Filter analytics
      store.set(updateAnalyticsFiltersAtom, { userId: 'user-1' })
      const filtered = store.get(filteredUsageLogsAtom)
      expect(filtered).toHaveLength(3)

      // Check performance metrics
      const performance = store.get(performanceMetricsAtom)
      expect(performance.p50ResponseTime).toBeGreaterThan(0)
      expect(performance.slowestEndpoints.length).toBeGreaterThan(0)
    })

    it('should handle time range filtering and presets', async () => {
      store.set(usageLogsAtom, mockUsageLogs)

      // Set week preset
      store.set(setTimeRangePresetAtom, 'week')
      const weekFilters = store.get(analyticsFiltersAtom)
      expect(weekFilters.timeRange.end.getTime() - weekFilters.timeRange.start.getTime())
        .toBe(7 * 24 * 60 * 60 * 1000)

      // Filter by time range should work
      const filtered = store.get(filteredUsageLogsAtom)
      expect(filtered.length).toBeGreaterThan(0)

      // Set day preset
      store.set(setTimeRangePresetAtom, 'day')
      const dayFilters = store.get(analyticsFiltersAtom)
      expect(dayFilters.timeRange.end.getTime() - dayFilters.timeRange.start.getTime())
        .toBe(24 * 60 * 60 * 1000)
    })

    it('should handle real-time updates workflow', async () => {
      store.set(usageLogsAtom, mockUsageLogs)
      
      // Enable real-time updates
      store.set(toggleRealTimeUpdatesAtom)
      expect(store.get(enableRealTimeUpdatesAtom)).toBe(true)

      // Simulate real-time update
      await store.set(realTimeUpdateAtom)
      
      const logs = store.get(usageLogsAtom)
      expect(logs.length).toBe(mockUsageLogs.length + 1)
      
      // Disable real-time updates
      store.set(toggleRealTimeUpdatesAtom)
      expect(store.get(enableRealTimeUpdatesAtom)).toBe(false)
    })
  })
})
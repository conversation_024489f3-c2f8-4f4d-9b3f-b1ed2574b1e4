import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createStore } from 'jotai'
import type { Store } from 'jotai'
import type { User } from '../../types/auth'
import type { UserFilters, UserStats, UserActivity, CreateUserRequest, UpdateUserRequest, UserActionRequest } from '../../types/user'

// Mock the API client
const mockApiClient = {
  getUsers: vi.fn(),
  createUser: vi.fn(),
  updateUser: vi.fn(),
  performUserAction: vi.fn(),
  deleteUser: vi.fn(),
  getUserStats: vi.fn(),
  getUserActivities: vi.fn(),
  getUserById: vi.fn()
}

vi.mock('../../lib/api', () => ({
  apiClient: mockApiClient
}))

// Import after mocking
const {
  users<PERSON>tom,
  selectedUser<PERSON>tom,
  userStats<PERSON>tom,
  userActivities<PERSON>tom,
  userFilters<PERSON>tom,
  user<PERSON>agination<PERSON>tom,
  filteredUsersAtom,
  sortedUsersAtom,
  paginatedUsersAtom,
  userStatsSummaryAtom,
  fetchUsersAtom,
  createUserAtom,
  updateUserAtom,
  performUserActionAtom,
  deleteUserAtom,
  fetchUserStatsAtom,
  fetchUserActivitiesAtom,
  loadUsersWithStatsAtom,
  updateUserFiltersAtom,
  updateUserPaginationAtom,
  selectUserWithActivitiesAtom
} = await import('../users')

describe('Users Store', () => {
  let store: Store

  const mockUsers: User[] = [
    {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'User One',
      role: 'user',
      status: 'approved',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      apiAccessLevel: 'basic'
    },
    {
      id: 'user-2',
      email: '<EMAIL>',
      name: 'User Two',
      role: 'admin',
      status: 'pending',
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      apiAccessLevel: 'premium'
    },
    {
      id: 'user-3',
      email: '<EMAIL>',
      name: 'User Three',
      role: 'user',
      status: 'revoked',
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-03'),
      apiAccessLevel: 'none'
    }
  ]

  const mockUserStats: UserStats = {
    total: 3,
    pending: 1,
    approved: 1,
    revoked: 1,
    admins: 1,
    users: 2,
    recentSignups: 2,
    activeUsers: 1
  }

  const mockUserActivities: UserActivity[] = [
    {
      userId: 'user-1',
      action: 'login',
      timestamp: new Date('2024-01-01T10:00:00Z'),
      metadata: { ip: '***********' }
    },
    {
      userId: 'user-1',
      action: 'api_key_created',
      timestamp: new Date('2024-01-01T11:00:00Z'),
      metadata: { keyName: 'Test Key' }
    }
  ]

  beforeEach(() => {
    store = createStore()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Base atoms', () => {
    it('should initialize with empty users array', () => {
      const users = store.get(usersAtom)
      expect(users).toEqual([])
    })

    it('should initialize with null selected user', () => {
      const selectedUser = store.get(selectedUserAtom)
      expect(selectedUser).toBeNull()
    })

    it('should initialize with null user stats', () => {
      const stats = store.get(userStatsAtom)
      expect(stats).toBeNull()
    })

    it('should initialize with empty activities array', () => {
      const activities = store.get(userActivitiesAtom)
      expect(activities).toEqual([])
    })
  })

  describe('Filter atoms', () => {
    it('should initialize with default filters', () => {
      const filters = store.get(userFiltersAtom)
      expect(filters).toEqual({
        status: 'all',
        role: 'all',
        apiAccessLevel: 'all',
        search: '',
        dateRange: undefined
      })
    })

    it('should update filters correctly', () => {
      const newFilters: Partial<UserFilters> = {
        status: 'approved',
        search: 'test'
      }

      store.set(updateUserFiltersAtom, newFilters)

      const filters = store.get(userFiltersAtom)
      expect(filters.status).toBe('approved')
      expect(filters.search).toBe('test')
      expect(filters.role).toBe('all') // Should preserve other values
    })

    it('should reset pagination when filters change', () => {
      store.set(userPaginationAtom, { page: 3, limit: 10, sortBy: 'name', sortOrder: 'asc' })
      
      store.set(updateUserFiltersAtom, { status: 'approved' })

      const pagination = store.get(userPaginationAtom)
      expect(pagination.page).toBe(1) // Should reset to page 1
      expect(pagination.limit).toBe(10) // Should preserve other values
    })
  })

  describe('Derived atoms', () => {
    beforeEach(() => {
      store.set(usersAtom, mockUsers)
    })

    describe('filteredUsersAtom', () => {
      it('should return all users when no filters applied', () => {
        const filtered = store.get(filteredUsersAtom)
        expect(filtered).toEqual(mockUsers)
      })

      it('should filter by status', () => {
        store.set(userFiltersAtom, { ...store.get(userFiltersAtom), status: 'approved' })
        
        const filtered = store.get(filteredUsersAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].status).toBe('approved')
      })

      it('should filter by role', () => {
        store.set(userFiltersAtom, { ...store.get(userFiltersAtom), role: 'admin' })
        
        const filtered = store.get(filteredUsersAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].role).toBe('admin')
      })

      it('should filter by API access level', () => {
        store.set(userFiltersAtom, { ...store.get(userFiltersAtom), apiAccessLevel: 'premium' })
        
        const filtered = store.get(filteredUsersAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].apiAccessLevel).toBe('premium')
      })

      it('should filter by search term (name)', () => {
        store.set(userFiltersAtom, { ...store.get(userFiltersAtom), search: 'One' })
        
        const filtered = store.get(filteredUsersAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].name).toBe('User One')
      })

      it('should filter by search term (email)', () => {
        store.set(userFiltersAtom, { ...store.get(userFiltersAtom), search: 'user2@' })
        
        const filtered = store.get(filteredUsersAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].email).toBe('<EMAIL>')
      })

      it('should filter by date range', () => {
        const dateRange = {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-02')
        }
        store.set(userFiltersAtom, { ...store.get(userFiltersAtom), dateRange })
        
        const filtered = store.get(filteredUsersAtom)
        expect(filtered).toHaveLength(2) // user-1 and user-2
      })

      it('should apply multiple filters', () => {
        store.set(userFiltersAtom, {
          ...store.get(userFiltersAtom),
          status: 'approved',
          role: 'user'
        })
        
        const filtered = store.get(filteredUsersAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].id).toBe('user-1')
      })
    })

    describe('sortedUsersAtom', () => {
      it('should sort users by creation date descending by default', () => {
        store.set(userPaginationAtom, {
          ...store.get(userPaginationAtom),
          sortBy: 'createdAt',
          sortOrder: 'desc'
        })
        
        const sorted = store.get(sortedUsersAtom)
        expect(sorted[0].id).toBe('user-3') // Most recent
        expect(sorted[2].id).toBe('user-1') // Oldest
      })

      it('should sort users by name ascending', () => {
        store.set(userPaginationAtom, {
          ...store.get(userPaginationAtom),
          sortBy: 'name',
          sortOrder: 'asc'
        })
        
        const sorted = store.get(sortedUsersAtom)
        expect(sorted[0].name).toBe('User One')
        expect(sorted[2].name).toBe('User Three')
      })
    })

    describe('paginatedUsersAtom', () => {
      it('should paginate users correctly', () => {
        store.set(userPaginationAtom, {
          ...store.get(userPaginationAtom),
          page: 1,
          limit: 2
        })
        
        const paginated = store.get(paginatedUsersAtom)
        expect(paginated).toHaveLength(2)
      })

      it('should return correct page of users', () => {
        store.set(userPaginationAtom, {
          ...store.get(userPaginationAtom),
          page: 2,
          limit: 2,
          sortBy: 'name',
          sortOrder: 'asc'
        })
        
        const paginated = store.get(paginatedUsersAtom)
        expect(paginated).toHaveLength(1)
        expect(paginated[0].name).toBe('User Three')
      })
    })

    describe('userStatsSummaryAtom', () => {
      it('should calculate user statistics correctly', () => {
        const stats = store.get(userStatsSummaryAtom)
        
        expect(stats).toEqual({
          totalUsers: 3,
          pendingApprovals: 1,
          activeUsers: 1,
          adminUsers: 1,
          recentSignups: 3 // All users are within a week for this test
        })
      })

      it('should handle empty users array', () => {
        store.set(usersAtom, [])
        
        const stats = store.get(userStatsSummaryAtom)
        expect(stats).toEqual({
          totalUsers: 0,
          pendingApprovals: 0,
          activeUsers: 0,
          adminUsers: 0,
          recentSignups: 0
        })
      })
    })
  })

  describe('Async action atoms', () => {
    describe('fetchUsersAtom', () => {
      it('should fetch users successfully', async () => {
        const mockResponse = {
          data: mockUsers,
          page: 1,
          limit: 10,
          total: 3
        }
        mockApiClient.getUsers.mockResolvedValue(mockResponse)

        const result = await store.set(fetchUsersAtom.actionAtom, undefined, 1, 10)

        expect(mockApiClient.getUsers).toHaveBeenCalledWith(undefined, 1, 10)
        expect(result).toEqual(mockResponse)
      })

      it('should handle fetch users error', async () => {
        const error = new Error('Failed to fetch users')
        mockApiClient.getUsers.mockRejectedValue(error)

        await expect(
          store.set(fetchUsersAtom.actionAtom, undefined, 1, 10)
        ).rejects.toThrow('Failed to fetch users')

        expect(store.get(fetchUsersAtom.errorAtom)).toBeTruthy()
      })
    })

    describe('createUserAtom', () => {
      it('should create user with optimistic update', async () => {
        const newUserData: CreateUserRequest = {
          email: '<EMAIL>',
          name: 'New User',
          role: 'user',
          apiAccessLevel: 'basic'
        }

        const createdUser: User = {
          id: 'user-4',
          ...newUserData,
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date()
        }

        mockApiClient.createUser.mockResolvedValue(createdUser)
        store.set(usersAtom, mockUsers)

        const result = await store.set(createUserAtom.optimisticActionAtom, newUserData)

        expect(mockApiClient.createUser).toHaveBeenCalledWith(newUserData)
        expect(result).toEqual(createdUser)
        
        // Check that user was added to the list
        const users = store.get(usersAtom)
        expect(users).toHaveLength(4)
      })

      it('should revert optimistic update on error', async () => {
        const newUserData: CreateUserRequest = {
          email: '<EMAIL>',
          name: 'Invalid User',
          role: 'user',
          apiAccessLevel: 'basic'
        }

        mockApiClient.createUser.mockRejectedValue(new Error('Email already exists'))
        store.set(usersAtom, mockUsers)

        await expect(
          store.set(createUserAtom.optimisticActionAtom, newUserData)
        ).rejects.toThrow('Email already exists')

        // Should revert to original users
        const users = store.get(usersAtom)
        expect(users).toEqual(mockUsers)
      })
    })

    describe('updateUserAtom', () => {
      it('should update user with optimistic update', async () => {
        const updateData: UpdateUserRequest = {
          id: 'user-1',
          name: 'Updated User One',
          status: 'approved'
        }

        const updatedUser = { ...mockUsers[0], ...updateData, updatedAt: new Date() }
        mockApiClient.updateUser.mockResolvedValue(updatedUser)
        store.set(usersAtom, mockUsers)

        const result = await store.set(updateUserAtom.optimisticActionAtom, updateData)

        expect(mockApiClient.updateUser).toHaveBeenCalledWith(updateData)
        expect(result).toEqual(updatedUser)
        
        // Check that user was updated in the list
        const users = store.get(usersAtom)
        const user = users.find(u => u.id === 'user-1')
        expect(user?.name).toBe('Updated User One')
      })
    })

    describe('performUserActionAtom', () => {
      it('should perform user action (approve)', async () => {
        const actionRequest: UserActionRequest = {
          userId: 'user-2',
          action: 'approve'
        }

        const updatedUser = { ...mockUsers[1], status: 'approved' as const, updatedAt: new Date() }
        mockApiClient.performUserAction.mockResolvedValue(updatedUser)
        store.set(usersAtom, mockUsers)

        const result = await store.set(performUserActionAtom.optimisticActionAtom, actionRequest)

        expect(mockApiClient.performUserAction).toHaveBeenCalledWith(actionRequest)
        expect(result).toEqual(updatedUser)
        
        // Check that user status was updated
        const users = store.get(usersAtom)
        const user = users.find(u => u.id === 'user-2')
        expect(user?.status).toBe('approved')
      })

      it('should perform user action (promote to admin)', async () => {
        const actionRequest: UserActionRequest = {
          userId: 'user-1',
          action: 'promote'
        }

        const updatedUser = { ...mockUsers[0], role: 'admin' as const, updatedAt: new Date() }
        mockApiClient.performUserAction.mockResolvedValue(updatedUser)
        store.set(usersAtom, mockUsers)

        await store.set(performUserActionAtom.optimisticActionAtom, actionRequest)

        const users = store.get(usersAtom)
        const user = users.find(u => u.id === 'user-1')
        expect(user?.role).toBe('admin')
      })
    })

    describe('deleteUserAtom', () => {
      it('should delete user with optimistic update', async () => {
        mockApiClient.deleteUser.mockResolvedValue(undefined)
        store.set(usersAtom, mockUsers)

        await store.set(deleteUserAtom.optimisticActionAtom, 'user-2')

        expect(mockApiClient.deleteUser).toHaveBeenCalledWith('user-2')
        
        // Check that user was removed from the list
        const users = store.get(usersAtom)
        expect(users).toHaveLength(2)
        expect(users.find(u => u.id === 'user-2')).toBeUndefined()
      })
    })

    describe('fetchUserStatsAtom', () => {
      it('should fetch user statistics', async () => {
        mockApiClient.getUserStats.mockResolvedValue(mockUserStats)

        const result = await store.set(fetchUserStatsAtom.actionAtom)

        expect(mockApiClient.getUserStats).toHaveBeenCalled()
        expect(result).toEqual(mockUserStats)
      })
    })

    describe('fetchUserActivitiesAtom', () => {
      it('should fetch user activities', async () => {
        mockApiClient.getUserActivities.mockResolvedValue(mockUserActivities)

        const result = await store.set(fetchUserActivitiesAtom.actionAtom, 'user-1', 50)

        expect(mockApiClient.getUserActivities).toHaveBeenCalledWith('user-1', 50)
        expect(result).toEqual(mockUserActivities)
      })
    })
  })

  describe('Combined action atoms', () => {
    describe('loadUsersWithStatsAtom', () => {
      it('should load users and stats in parallel', async () => {
        const mockUsersResponse = {
          data: mockUsers,
          page: 1,
          limit: 10,
          total: 3
        }

        mockApiClient.getUsers.mockResolvedValue(mockUsersResponse)
        mockApiClient.getUserStats.mockResolvedValue(mockUserStats)

        const result = await store.set(loadUsersWithStatsAtom, undefined, 1, 10)

        expect(mockApiClient.getUsers).toHaveBeenCalledWith(undefined, 1, 10)
        expect(mockApiClient.getUserStats).toHaveBeenCalled()
        expect(store.get(usersAtom)).toEqual(mockUsers)
        expect(store.get(userStatsAtom)).toEqual(mockUserStats)
        expect(result.errors).toEqual([])
      })

      it('should handle partial failures', async () => {
        const mockUsersResponse = {
          data: mockUsers,
          page: 1,
          limit: 10,
          total: 3
        }

        mockApiClient.getUsers.mockResolvedValue(mockUsersResponse)
        mockApiClient.getUserStats.mockRejectedValue(new Error('Stats failed'))

        const result = await store.set(loadUsersWithStatsAtom, undefined, 1, 10)

        expect(store.get(usersAtom)).toEqual(mockUsers)
        expect(store.get(userStatsAtom)).toBeNull()
        expect(result.errors).toHaveLength(1)
      })
    })

    describe('selectUserWithActivitiesAtom', () => {
      it('should select user and load activities', async () => {
        mockApiClient.getUserActivities.mockResolvedValue(mockUserActivities)

        await store.set(selectUserWithActivitiesAtom, mockUsers[0])

        expect(store.get(selectedUserAtom)).toEqual(mockUsers[0])
        expect(mockApiClient.getUserActivities).toHaveBeenCalledWith('user-1')
        expect(store.get(userActivitiesAtom)).toEqual(mockUserActivities)
      })

      it('should handle activities loading failure gracefully', async () => {
        mockApiClient.getUserActivities.mockRejectedValue(new Error('Activities failed'))
        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

        await store.set(selectUserWithActivitiesAtom, mockUsers[0])

        expect(store.get(selectedUserAtom)).toEqual(mockUsers[0])
        expect(consoleSpy).toHaveBeenCalledWith('Failed to load user activities:', expect.any(Error))
        
        consoleSpy.mockRestore()
      })
    })
  })

  describe('Integration scenarios', () => {
    it('should handle complete user management workflow', async () => {
      // Load initial users
      const mockUsersResponse = {
        data: mockUsers,
        page: 1,
        limit: 10,
        total: 3
      }
      mockApiClient.getUsers.mockResolvedValue(mockUsersResponse)
      mockApiClient.getUserStats.mockResolvedValue(mockUserStats)

      await store.set(loadUsersWithStatsAtom)

      expect(store.get(usersAtom)).toEqual(mockUsers)
      expect(store.get(userStatsSummaryAtom).totalUsers).toBe(3)

      // Filter users
      store.set(updateUserFiltersAtom, { status: 'pending' })
      const filtered = store.get(filteredUsersAtom)
      expect(filtered).toHaveLength(1)
      expect(filtered[0].status).toBe('pending')

      // Approve pending user
      const actionRequest: UserActionRequest = {
        userId: 'user-2',
        action: 'approve'
      }
      const approvedUser = { ...mockUsers[1], status: 'approved' as const }
      mockApiClient.performUserAction.mockResolvedValue(approvedUser)

      await store.set(performUserActionAtom.optimisticActionAtom, actionRequest)

      // Verify user was approved
      const users = store.get(usersAtom)
      const user = users.find(u => u.id === 'user-2')
      expect(user?.status).toBe('approved')
    })

    it('should handle pagination and sorting', async () => {
      store.set(usersAtom, mockUsers)

      // Set pagination
      store.set(updateUserPaginationAtom, {
        page: 1,
        limit: 2,
        sortBy: 'name',
        sortOrder: 'asc'
      })

      const paginated = store.get(paginatedUsersAtom)
      expect(paginated).toHaveLength(2)
      expect(paginated[0].name).toBe('User One')
      expect(paginated[1].name).toBe('User Three')

      // Go to next page
      store.set(updateUserPaginationAtom, { page: 2 })
      const nextPage = store.get(paginatedUsersAtom)
      expect(nextPage).toHaveLength(1)
      expect(nextPage[0].name).toBe('User Two')
    })
  })
})
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createStore } from 'jotai'
import type { Store } from 'jotai'
import type { ApiKey, ApiKeyFilters, ApiKeyStats, ApiKeyUsage, CreateApiKeyRequest, UpdateApiKeyRequest } from '../../types/apiKey'
import type { User } from '../../types/auth'
import type { TimeRange } from '../../types/api'

// Mock the API client
const mockApiClient = {
  getApiKeys: vi.fn(),
  createApiKey: vi.fn(),
  updateApiKey: vi.fn(),
  regenerateApiKey: vi.fn(),
  revokeApiKey: vi.fn(),
  deleteApiKey: vi.fn(),
  getApiKeyStats: vi.fn(),
  getApiKeyUsage: vi.fn(),
  getApiKeyById: vi.fn()
}

vi.mock('../../lib/api', () => ({
  apiClient: mockApiClient
}))

// Import after mocking
const {
  apiKeys<PERSON>tom,
  selectedApiKeyAtom,
  apiKeyStatsAtom,
  apiKeyUsageAtom,
  apiKeyFiltersAtom,
  userApiKeysAtom,
  filteredApiKeysAtom,
  sortedApiKeysAtom,
  apiKeyStatsSummaryAtom,
  keysByStatusAtom,
  expiringSoonKeysAtom,
  fetchApiKeysAtom,
  createApiKeyAtom,
  updateApiKeyAtom,
  regenerateApiKeyAtom,
  revokeApiKeyAtom,
  deleteApiKeyAtom,
  fetchApiKeyStatsAtom,
  fetchApiKeyUsageAtom,
  loadUserApiKeysWithStatsAtom,
  loadAllApiKeysWithStatsAtom,
  selectApiKeyWithUsageAtom,
  updateApiKeyFiltersAtom,
  bulkRevokeApiKeysAtom,
  bulkDeleteApiKeysAtom
} = await import('../apiKeys')

const { userAtom } = await import('../auth')

describe('API Keys Store', () => {
  let store: Store

  const mockUser: User = {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user',
    status: 'approved',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    apiAccessLevel: 'basic'
  }

  const mockApiKeys: ApiKey[] = [
    {
      id: 'key-1',
      userId: 'user-1',
      name: 'Production Key',
      description: 'Key for production use',
      keyHash: 'hash-1',
      status: 'active',
      permissions: [{ resource: 'ollama', actions: ['read', 'write'] }],
      rateLimit: {
        requestsPerMinute: 60,
        requestsPerHour: 1000,
        requestsPerDay: 10000
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      lastUsedAt: new Date('2024-01-02')
    },
    {
      id: 'key-2',
      userId: 'user-1',
      name: 'Development Key',
      description: 'Key for development',
      keyHash: 'hash-2',
      status: 'active',
      permissions: [{ resource: 'ollama', actions: ['read'] }],
      rateLimit: {
        requestsPerMinute: 30,
        requestsPerHour: 500,
        requestsPerDay: 5000
      },
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      expiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000) // 15 days from now
    },
    {
      id: 'key-3',
      userId: 'user-2',
      name: 'Revoked Key',
      keyHash: 'hash-3',
      status: 'revoked',
      permissions: [{ resource: 'ollama', actions: ['read'] }],
      rateLimit: {
        requestsPerMinute: 60,
        requestsPerHour: 1000,
        requestsPerDay: 10000
      },
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-03')
    }
  ]

  const mockApiKeyStats: ApiKeyStats = {
    total: 3,
    active: 2,
    revoked: 1,
    expiringSoon: 1,
    neverUsed: 1,
    totalUsage: 1500,
    averageUsage: 500
  }

  const mockApiKeyUsage: ApiKeyUsage = {
    keyId: 'key-1',
    period: {
      start: new Date('2024-01-01'),
      end: new Date('2024-01-31')
    },
    totalRequests: 1000,
    successfulRequests: 950,
    failedRequests: 50,
    averageResponseTime: 250,
    dataTransferred: 1024000,
    topEndpoints: [
      {
        endpoint: '/api/ollama/generate',
        count: 800,
        averageResponseTime: 200
      },
      {
        endpoint: '/api/ollama/chat',
        count: 200,
        averageResponseTime: 400
      }
    ]
  }

  beforeEach(() => {
    store = createStore()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Base atoms', () => {
    it('should initialize with empty API keys array', () => {
      const keys = store.get(apiKeysAtom)
      expect(keys).toEqual([])
    })

    it('should initialize with null selected API key', () => {
      const selectedKey = store.get(selectedApiKeyAtom)
      expect(selectedKey).toBeNull()
    })

    it('should initialize with null API key stats', () => {
      const stats = store.get(apiKeyStatsAtom)
      expect(stats).toBeNull()
    })

    it('should initialize with empty usage object', () => {
      const usage = store.get(apiKeyUsageAtom)
      expect(usage).toEqual({})
    })
  })

  describe('Filter atoms', () => {
    it('should initialize with default filters', () => {
      const filters = store.get(apiKeyFiltersAtom)
      expect(filters).toEqual({
        status: 'all',
        search: '',
        hasExpiry: undefined
      })
    })

    it('should update filters correctly', () => {
      const newFilters: Partial<ApiKeyFilters> = {
        status: 'active',
        search: 'prod'
      }

      store.set(updateApiKeyFiltersAtom, newFilters)

      const filters = store.get(apiKeyFiltersAtom)
      expect(filters.status).toBe('active')
      expect(filters.search).toBe('prod')
    })
  })

  describe('Derived atoms', () => {
    beforeEach(() => {
      store.set(apiKeysAtom, mockApiKeys)
      store.set(userAtom, mockUser)
    })

    describe('userApiKeysAtom', () => {
      it('should return keys for current user only', () => {
        const userKeys = store.get(userApiKeysAtom)
        expect(userKeys).toHaveLength(2)
        expect(userKeys.every(key => key.userId === 'user-1')).toBe(true)
      })

      it('should return empty array when no user', () => {
        store.set(userAtom, null)
        const userKeys = store.get(userApiKeysAtom)
        expect(userKeys).toEqual([])
      })
    })

    describe('filteredApiKeysAtom', () => {
      it('should return all keys when no filters applied', () => {
        const filtered = store.get(filteredApiKeysAtom)
        expect(filtered).toEqual(mockApiKeys)
      })

      it('should filter by status', () => {
        store.set(apiKeyFiltersAtom, { ...store.get(apiKeyFiltersAtom), status: 'active' })
        
        const filtered = store.get(filteredApiKeysAtom)
        expect(filtered).toHaveLength(2)
        expect(filtered.every(key => key.status === 'active')).toBe(true)
      })

      it('should filter by search term (name)', () => {
        store.set(apiKeyFiltersAtom, { ...store.get(apiKeyFiltersAtom), search: 'Production' })
        
        const filtered = store.get(filteredApiKeysAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].name).toBe('Production Key')
      })

      it('should filter by search term (description)', () => {
        store.set(apiKeyFiltersAtom, { ...store.get(apiKeyFiltersAtom), search: 'development' })
        
        const filtered = store.get(filteredApiKeysAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].description).toBe('Key for development')
      })

      it('should filter by expiry status', () => {
        store.set(apiKeyFiltersAtom, { ...store.get(apiKeyFiltersAtom), hasExpiry: true })
        
        const filtered = store.get(filteredApiKeysAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].expiresAt).toBeDefined()
      })

      it('should filter by user ID', () => {
        store.set(apiKeyFiltersAtom, { ...store.get(apiKeyFiltersAtom), userId: 'user-2' })
        
        const filtered = store.get(filteredApiKeysAtom)
        expect(filtered).toHaveLength(1)
        expect(filtered[0].userId).toBe('user-2')
      })
    })

    describe('sortedApiKeysAtom', () => {
      it('should sort keys by creation date (newest first)', () => {
        const sorted = store.get(sortedApiKeysAtom)
        expect(sorted[0].id).toBe('key-3') // Most recent
        expect(sorted[2].id).toBe('key-1') // Oldest
      })
    })

    describe('apiKeyStatsSummaryAtom', () => {
      it('should calculate API key statistics correctly', () => {
        const stats = store.get(apiKeyStatsSummaryAtom)
        
        expect(stats).toEqual({
          totalKeys: 3,
          activeKeys: 2,
          revokedKeys: 1,
          expiringSoon: 1, // key-2 expires in 15 days
          neverUsed: 1, // key-2 has no lastUsedAt
          recentlyCreated: 3 // All keys are within a week for this test
        })
      })

      it('should handle empty keys array', () => {
        store.set(apiKeysAtom, [])
        
        const stats = store.get(apiKeyStatsSummaryAtom)
        expect(stats).toEqual({
          totalKeys: 0,
          activeKeys: 0,
          revokedKeys: 0,
          expiringSoon: 0,
          neverUsed: 0,
          recentlyCreated: 0
        })
      })
    })

    describe('keysByStatusAtom', () => {
      it('should group keys by status', () => {
        const grouped = store.get(keysByStatusAtom)
        
        expect(grouped.active).toHaveLength(2)
        expect(grouped.revoked).toHaveLength(1)
      })
    })

    describe('expiringSoonKeysAtom', () => {
      it('should return keys expiring within 30 days', () => {
        const expiring = store.get(expiringSoonKeysAtom)
        expect(expiring).toHaveLength(1)
        expect(expiring[0].id).toBe('key-2')
      })

      it('should not include revoked keys', () => {
        const expiringSoonRevoked = {
          ...mockApiKeys[2],
          status: 'revoked' as const,
          expiresAt: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000)
        }
        store.set(apiKeysAtom, [...mockApiKeys, expiringSoonRevoked])
        
        const expiring = store.get(expiringSoonKeysAtom)
        expect(expiring.every(key => key.status === 'active')).toBe(true)
      })
    })
  })

  describe('Async action atoms', () => {
    describe('fetchApiKeysAtom', () => {
      it('should fetch API keys successfully', async () => {
        mockApiClient.getApiKeys.mockResolvedValue(mockApiKeys)

        const result = await store.set(fetchApiKeysAtom.actionAtom, undefined, 'user-1')

        expect(mockApiClient.getApiKeys).toHaveBeenCalledWith(undefined, 'user-1')
        expect(result).toEqual(mockApiKeys)
      })

      it('should handle fetch error', async () => {
        const error = new Error('Failed to fetch API keys')
        mockApiClient.getApiKeys.mockRejectedValue(error)

        await expect(
          store.set(fetchApiKeysAtom.actionAtom, undefined, 'user-1')
        ).rejects.toThrow('Failed to fetch API keys')

        expect(store.get(fetchApiKeysAtom.errorAtom)).toBeTruthy()
      })
    })

    describe('createApiKeyAtom', () => {
      it('should create API key with optimistic update', async () => {
        const newKeyData: CreateApiKeyRequest = {
          name: 'New Test Key',
          description: 'Test key description',
          permissions: [{ resource: 'ollama', actions: ['read'] }],
          rateLimit: {
            requestsPerMinute: 30,
            requestsPerHour: 500,
            requestsPerDay: 5000
          }
        }

        const createdKey: ApiKey = {
          id: 'key-4',
          userId: 'user-1',
          ...newKeyData,
          keyHash: 'hash-4',
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
          rateLimit: {
            requestsPerMinute: 30,
            requestsPerHour: 500,
            requestsPerDay: 5000
          }
        }

        mockApiClient.createApiKey.mockResolvedValue(createdKey)
        store.set(apiKeysAtom, mockApiKeys)

        const result = await store.set(createApiKeyAtom.optimisticActionAtom, newKeyData, 'user-1')

        expect(mockApiClient.createApiKey).toHaveBeenCalledWith(newKeyData, 'user-1')
        expect(result).toEqual(createdKey)
        
        // Check that key was added to the list
        const keys = store.get(apiKeysAtom)
        expect(keys).toHaveLength(4)
      })

      it('should revert optimistic update on error', async () => {
        const newKeyData: CreateApiKeyRequest = {
          name: 'Invalid Key',
          permissions: [{ resource: 'ollama', actions: ['read'] }]
        }

        mockApiClient.createApiKey.mockRejectedValue(new Error('Key creation failed'))
        store.set(apiKeysAtom, mockApiKeys)

        await expect(
          store.set(createApiKeyAtom.optimisticActionAtom, newKeyData, 'user-1')
        ).rejects.toThrow('Key creation failed')

        // Should revert to original keys
        const keys = store.get(apiKeysAtom)
        expect(keys).toEqual(mockApiKeys)
      })
    })

    describe('updateApiKeyAtom', () => {
      it('should update API key with optimistic update', async () => {
        const updateData: UpdateApiKeyRequest = {
          id: 'key-1',
          name: 'Updated Production Key',
          description: 'Updated description'
        }

        const updatedKey = { ...mockApiKeys[0], ...updateData, updatedAt: new Date() }
        mockApiClient.updateApiKey.mockResolvedValue(updatedKey)
        store.set(apiKeysAtom, mockApiKeys)

        const result = await store.set(updateApiKeyAtom.optimisticActionAtom, updateData)

        expect(mockApiClient.updateApiKey).toHaveBeenCalledWith(updateData)
        expect(result).toEqual(updatedKey)
        
        // Check that key was updated in the list
        const keys = store.get(apiKeysAtom)
        const key = keys.find(k => k.id === 'key-1')
        expect(key?.name).toBe('Updated Production Key')
      })
    })

    describe('regenerateApiKeyAtom', () => {
      it('should regenerate API key', async () => {
        const regeneratedKey = { 
          ...mockApiKeys[0], 
          updatedAt: new Date(),
          lastUsedAt: undefined 
        }
        mockApiClient.regenerateApiKey.mockResolvedValue(regeneratedKey)
        store.set(apiKeysAtom, mockApiKeys)

        const result = await store.set(regenerateApiKeyAtom.optimisticActionAtom, 'key-1')

        expect(mockApiClient.regenerateApiKey).toHaveBeenCalledWith('key-1')
        expect(result).toEqual(regeneratedKey)
        
        // Check that key was updated
        const keys = store.get(apiKeysAtom)
        const key = keys.find(k => k.id === 'key-1')
        expect(key?.lastUsedAt).toBeUndefined()
      })
    })

    describe('revokeApiKeyAtom', () => {
      it('should revoke API key', async () => {
        const revokedKey = { 
          ...mockApiKeys[0], 
          status: 'revoked' as const,
          updatedAt: new Date()
        }
        mockApiClient.revokeApiKey.mockResolvedValue(revokedKey)
        store.set(apiKeysAtom, mockApiKeys)

        const result = await store.set(revokeApiKeyAtom.optimisticActionAtom, 'key-1')

        expect(mockApiClient.revokeApiKey).toHaveBeenCalledWith('key-1')
        expect(result).toEqual(revokedKey)
        
        // Check that key status was updated
        const keys = store.get(apiKeysAtom)
        const key = keys.find(k => k.id === 'key-1')
        expect(key?.status).toBe('revoked')
      })
    })

    describe('deleteApiKeyAtom', () => {
      it('should delete API key', async () => {
        mockApiClient.deleteApiKey.mockResolvedValue(undefined)
        store.set(apiKeysAtom, mockApiKeys)

        await store.set(deleteApiKeyAtom.optimisticActionAtom, 'key-1')

        expect(mockApiClient.deleteApiKey).toHaveBeenCalledWith('key-1')
        
        // Check that key was removed from the list
        const keys = store.get(apiKeysAtom)
        expect(keys).toHaveLength(2)
        expect(keys.find(k => k.id === 'key-1')).toBeUndefined()
      })
    })

    describe('fetchApiKeyStatsAtom', () => {
      it('should fetch API key statistics', async () => {
        mockApiClient.getApiKeyStats.mockResolvedValue(mockApiKeyStats)

        const result = await store.set(fetchApiKeyStatsAtom.actionAtom, 'user-1')

        expect(mockApiClient.getApiKeyStats).toHaveBeenCalledWith('user-1')
        expect(result).toEqual(mockApiKeyStats)
      })
    })

    describe('fetchApiKeyUsageAtom', () => {
      it('should fetch API key usage', async () => {
        const timeRange: TimeRange = {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        }
        mockApiClient.getApiKeyUsage.mockResolvedValue(mockApiKeyUsage)

        const result = await store.set(fetchApiKeyUsageAtom.actionAtom, 'key-1', timeRange)

        expect(mockApiClient.getApiKeyUsage).toHaveBeenCalledWith('key-1', timeRange)
        expect(result).toEqual(mockApiKeyUsage)
      })
    })
  })

  describe('Combined action atoms', () => {
    beforeEach(() => {
      store.set(userAtom, mockUser)
    })

    describe('loadUserApiKeysWithStatsAtom', () => {
      it('should load user API keys and stats in parallel', async () => {
        mockApiClient.getApiKeys.mockResolvedValue(mockApiKeys.filter(k => k.userId === 'user-1'))
        mockApiClient.getApiKeyStats.mockResolvedValue(mockApiKeyStats)

        const result = await store.set(loadUserApiKeysWithStatsAtom)

        expect(mockApiClient.getApiKeys).toHaveBeenCalledWith(undefined, 'user-1')
        expect(mockApiClient.getApiKeyStats).toHaveBeenCalledWith('user-1')
        expect(store.get(apiKeysAtom)).toHaveLength(2)
        expect(store.get(apiKeyStatsAtom)).toEqual(mockApiKeyStats)
        expect(result.errors).toEqual([])
      })

      it('should handle partial failures', async () => {
        mockApiClient.getApiKeys.mockResolvedValue(mockApiKeys.filter(k => k.userId === 'user-1'))
        mockApiClient.getApiKeyStats.mockRejectedValue(new Error('Stats failed'))

        const result = await store.set(loadUserApiKeysWithStatsAtom)

        expect(store.get(apiKeysAtom)).toHaveLength(2)
        expect(store.get(apiKeyStatsAtom)).toBeNull()
        expect(result.errors).toHaveLength(1)
      })

      it('should throw error when user not authenticated', async () => {
        store.set(userAtom, null)

        await expect(store.set(loadUserApiKeysWithStatsAtom)).rejects.toThrow('User not authenticated')
      })
    })

    describe('loadAllApiKeysWithStatsAtom', () => {
      it('should load all API keys and stats for admin', async () => {
        const filters: ApiKeyFilters = { status: 'active', search: '', hasExpiry: undefined }
        mockApiClient.getApiKeys.mockResolvedValue(mockApiKeys)
        mockApiClient.getApiKeyStats.mockResolvedValue(mockApiKeyStats)

        const result = await store.set(loadAllApiKeysWithStatsAtom, filters)

        expect(mockApiClient.getApiKeys).toHaveBeenCalledWith(filters)
        expect(mockApiClient.getApiKeyStats).toHaveBeenCalledWith()
        expect(store.get(apiKeysAtom)).toEqual(mockApiKeys)
        expect(store.get(apiKeyStatsAtom)).toEqual(mockApiKeyStats)
        expect(result.errors).toEqual([])
      })
    })

    describe('selectApiKeyWithUsageAtom', () => {
      it('should select API key and load usage data', async () => {
        mockApiClient.getApiKeyUsage.mockResolvedValue(mockApiKeyUsage)

        const timeRange: TimeRange = {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        }

        await store.set(selectApiKeyWithUsageAtom, mockApiKeys[0], timeRange)

        expect(store.get(selectedApiKeyAtom)).toEqual(mockApiKeys[0])
        expect(mockApiClient.getApiKeyUsage).toHaveBeenCalledWith('key-1', timeRange)
        expect(store.get(apiKeyUsageAtom)['key-1']).toEqual(mockApiKeyUsage)
      })

      it('should use default time range when not provided', async () => {
        mockApiClient.getApiKeyUsage.mockResolvedValue(mockApiKeyUsage)

        await store.set(selectApiKeyWithUsageAtom, mockApiKeys[0])

        expect(store.get(selectedApiKeyAtom)).toEqual(mockApiKeys[0])
        expect(mockApiClient.getApiKeyUsage).toHaveBeenCalledWith('key-1', expect.any(Object))
      })

      it('should handle usage loading failure gracefully', async () => {
        mockApiClient.getApiKeyUsage.mockRejectedValue(new Error('Usage failed'))
        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

        await store.set(selectApiKeyWithUsageAtom, mockApiKeys[0])

        expect(store.get(selectedApiKeyAtom)).toEqual(mockApiKeys[0])
        expect(consoleSpy).toHaveBeenCalledWith('Failed to load API key usage:', expect.any(Error))
        
        consoleSpy.mockRestore()
      })
    })

    describe('bulkRevokeApiKeysAtom', () => {
      it('should revoke multiple API keys', async () => {
        const keyIds = ['key-1', 'key-2']
        const revokedKey1 = { ...mockApiKeys[0], status: 'revoked' as const }
        const revokedKey2 = { ...mockApiKeys[1], status: 'revoked' as const }
        
        mockApiClient.revokeApiKey
          .mockResolvedValueOnce(revokedKey1)
          .mockResolvedValueOnce(revokedKey2)
        
        store.set(apiKeysAtom, mockApiKeys)

        const result = await store.set(bulkRevokeApiKeysAtom, keyIds)

        expect(mockApiClient.revokeApiKey).toHaveBeenCalledTimes(2)
        expect(result.successCount).toBe(2)
        expect(result.errors).toEqual([])
      })

      it('should handle partial failures in bulk revoke', async () => {
        const keyIds = ['key-1', 'key-2']
        const revokedKey1 = { ...mockApiKeys[0], status: 'revoked' as const }
        
        mockApiClient.revokeApiKey
          .mockResolvedValueOnce(revokedKey1)
          .mockRejectedValueOnce(new Error('Revoke failed'))
        
        store.set(apiKeysAtom, mockApiKeys)

        const result = await store.set(bulkRevokeApiKeysAtom, keyIds)

        expect(result.successCount).toBe(1)
        expect(result.errors).toHaveLength(1)
      })
    })

    describe('bulkDeleteApiKeysAtom', () => {
      it('should delete multiple API keys', async () => {
        const keyIds = ['key-1', 'key-2']
        
        mockApiClient.deleteApiKey
          .mockResolvedValueOnce(undefined)
          .mockResolvedValueOnce(undefined)
        
        store.set(apiKeysAtom, mockApiKeys)

        const result = await store.set(bulkDeleteApiKeysAtom, keyIds)

        expect(mockApiClient.deleteApiKey).toHaveBeenCalledTimes(2)
        expect(result.successCount).toBe(2)
        expect(result.errors).toEqual([])
        
        // Check that keys were removed
        const keys = store.get(apiKeysAtom)
        expect(keys).toHaveLength(1)
        expect(keys[0].id).toBe('key-3')
      })
    })
  })

  describe('Integration scenarios', () => {
    beforeEach(() => {
      store.set(userAtom, mockUser)
    })

    it('should handle complete API key management workflow', async () => {
      // Load initial keys
      mockApiClient.getApiKeys.mockResolvedValue(mockApiKeys.filter(k => k.userId === 'user-1'))
      mockApiClient.getApiKeyStats.mockResolvedValue(mockApiKeyStats)

      await store.set(loadUserApiKeysWithStatsAtom)

      expect(store.get(apiKeysAtom)).toHaveLength(2)
      expect(store.get(apiKeyStatsSummaryAtom).totalKeys).toBe(2)

      // Filter keys
      store.set(updateApiKeyFiltersAtom, { status: 'active' })
      const filtered = store.get(filteredApiKeysAtom)
      expect(filtered).toHaveLength(2)

      // Create new key
      const newKeyData: CreateApiKeyRequest = {
        name: 'Test Key',
        permissions: [{ resource: 'ollama', actions: ['read'] }]
      }
      const createdKey: ApiKey = {
        id: 'key-4',
        userId: 'user-1',
        ...newKeyData,
        keyHash: 'hash-4',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
        rateLimit: {
          requestsPerMinute: 60,
          requestsPerHour: 1000,
          requestsPerDay: 10000
        }
      }
      mockApiClient.createApiKey.mockResolvedValue(createdKey)

      await store.set(createApiKeyAtom.optimisticActionAtom, newKeyData, 'user-1')

      // Verify key was created
      const keys = store.get(apiKeysAtom)
      expect(keys).toHaveLength(3)
      expect(keys.find(k => k.name === 'Test Key')).toBeDefined()
    })

    it('should handle key expiry detection', async () => {
      store.set(apiKeysAtom, mockApiKeys)

      const expiring = store.get(expiringSoonKeysAtom)
      expect(expiring).toHaveLength(1)
      expect(expiring[0].id).toBe('key-2')

      // Update key to expire sooner
      const soonExpiring = {
        ...mockApiKeys[1],
        expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) // 5 days
      }
      store.set(apiKeysAtom, [mockApiKeys[0], soonExpiring, mockApiKeys[2]])

      const stillExpiring = store.get(expiringSoonKeysAtom)
      expect(stillExpiring).toHaveLength(1)
      expect(stillExpiring[0].expiresAt).toEqual(soonExpiring.expiresAt)
    })
  })
})
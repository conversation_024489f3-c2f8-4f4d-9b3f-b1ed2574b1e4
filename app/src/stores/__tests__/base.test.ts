import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createStore } from 'jotai'
import { atom } from 'jotai'
import type { Store } from 'jotai'
import {
  globalErrorAtom,
  globalLoadingAtom,
  createAsyncAtom,
  createLoadingStateAtom,
  createFilteredAtom,
  createComputedAtom,
  clearGlobalErrorAtom,
  setGlobalLoadingAtom,
  createOptimisticUpdateAtom,
  createDebouncedAtom
} from '../base'
import type { ApiError, LoadingState } from '../../types/api'

describe('Base Store Utilities', () => {
  let store: Store

  beforeEach(() => {
    store = createStore()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Global state atoms', () => {
    describe('globalErrorAtom', () => {
      it('should initialize with null error', () => {
        const error = store.get(globalErrorAtom)
        expect(error).toBeNull()
      })

      it('should store error correctly', () => {
        const testError: ApiError = {
          code: 'TEST_ERROR',
          message: 'Test error message',
          timestamp: new Date(),
          details: { test: 'data' }
        }

        store.set(globalErrorAtom, testError)
        const error = store.get(globalErrorAtom)
        expect(error).toEqual(testError)
      })
    })

    describe('globalLoadingAtom', () => {
      it('should initialize with false loading state', () => {
        const loading = store.get(globalLoadingAtom)
        expect(loading).toBe(false)
      })

      it('should update loading state correctly', () => {
        store.set(globalLoadingAtom, true)
        expect(store.get(globalLoadingAtom)).toBe(true)

        store.set(globalLoadingAtom, false)
        expect(store.get(globalLoadingAtom)).toBe(false)
      })
    })

    describe('clearGlobalErrorAtom', () => {
      it('should clear global error', () => {
        const testError: ApiError = {
          code: 'TEST_ERROR',
          message: 'Test error message',
          timestamp: new Date()
        }

        store.set(globalErrorAtom, testError)
        expect(store.get(globalErrorAtom)).toEqual(testError)

        store.set(clearGlobalErrorAtom)
        expect(store.get(globalErrorAtom)).toBeNull()
      })
    })

    describe('setGlobalLoadingAtom', () => {
      it('should set global loading state', () => {
        store.set(setGlobalLoadingAtom, true)
        expect(store.get(globalLoadingAtom)).toBe(true)

        store.set(setGlobalLoadingAtom, false)
        expect(store.get(globalLoadingAtom)).toBe(false)
      })
    })
  })

  describe('createAsyncAtom', () => {
    it('should create async atom with correct initial state', () => {
      const mockAsyncFn = vi.fn().mockResolvedValue('test result')
      const asyncAtom = createAsyncAtom(mockAsyncFn, 'TEST_ERROR')

      expect(store.get(asyncAtom.dataAtom)).toBeNull()
      expect(store.get(asyncAtom.errorAtom)).toBeNull()
      expect(store.get(asyncAtom.loadingAtom)).toBe(false)
    })

    it('should handle successful async operation', async () => {
      const mockAsyncFn = vi.fn().mockResolvedValue('test result')
      const asyncAtom = createAsyncAtom(mockAsyncFn, 'TEST_ERROR')

      const result = await store.set(asyncAtom.actionAtom, 'test arg')

      expect(mockAsyncFn).toHaveBeenCalledWith('test arg')
      expect(result).toBe('test result')
      expect(store.get(asyncAtom.dataAtom)).toBe('test result')
      expect(store.get(asyncAtom.errorAtom)).toBeNull()
      expect(store.get(asyncAtom.loadingAtom)).toBe(false)
    })

    it('should handle async operation error', async () => {
      const mockError = new Error('Test error')
      const mockAsyncFn = vi.fn().mockRejectedValue(mockError)
      const asyncAtom = createAsyncAtom(mockAsyncFn, 'TEST_ERROR')

      await expect(store.set(asyncAtom.actionAtom, 'test arg')).rejects.toThrow('Test error')

      expect(store.get(asyncAtom.dataAtom)).toBeNull()
      expect(store.get(asyncAtom.errorAtom)).toEqual({
        code: 'TEST_ERROR',
        message: 'Test error',
        timestamp: expect.any(Date),
        details: { stack: expect.any(String) }
      })
      expect(store.get(asyncAtom.loadingAtom)).toBe(false)
      expect(store.get(globalErrorAtom)).toBeTruthy()
    })

    it('should set loading state during async operation', async () => {
      let resolveAsync: (value: string) => void
      const asyncPromise = new Promise<string>(resolve => {
        resolveAsync = resolve
      })
      const mockAsyncFn = vi.fn().mockReturnValue(asyncPromise)
      const asyncAtom = createAsyncAtom(mockAsyncFn, 'TEST_ERROR')

      const actionPromise = store.set(asyncAtom.actionAtom, 'test arg')

      // Check loading state is true during async operation
      expect(store.get(asyncAtom.loadingAtom)).toBe(true)

      resolveAsync!('test result')
      await actionPromise

      expect(store.get(asyncAtom.loadingAtom)).toBe(false)
    })

    it('should create result atom with correct structure', () => {
      const mockAsyncFn = vi.fn().mockResolvedValue('test result')
      const asyncAtom = createAsyncAtom(mockAsyncFn, 'TEST_ERROR')

      const result = store.get(asyncAtom.resultAtom)
      expect(result).toEqual({
        data: null,
        error: null,
        isLoading: false
      })
    })

    it('should handle non-Error objects in catch block', async () => {
      const mockAsyncFn = vi.fn().mockRejectedValue('string error')
      const asyncAtom = createAsyncAtom(mockAsyncFn, 'TEST_ERROR')

      await expect(store.set(asyncAtom.actionAtom)).rejects.toThrow()

      const error = store.get(asyncAtom.errorAtom)
      expect(error?.message).toBe('Unknown error occurred')
      expect(error?.details).toEqual({ error: 'string error' })
    })
  })

  describe('createLoadingStateAtom', () => {
    it('should create loading state atom with default initial state', () => {
      const loadingAtom = createLoadingStateAtom()
      const state = store.get(loadingAtom)

      expect(state).toEqual({
        isLoading: false,
        error: null
      })
    })

    it('should create loading state atom with custom initial state', () => {
      const loadingAtom = createLoadingStateAtom(true)
      const state = store.get(loadingAtom)

      expect(state).toEqual({
        isLoading: true,
        error: null
      })
    })
  })

  describe('createFilteredAtom', () => {
    it('should create filtered atom that filters data correctly', () => {
      const dataAtom = atom([1, 2, 3, 4, 5])
      const filtersAtom = atom({ min: 3 })
      
      const filteredAtom = createFilteredAtom(
        dataAtom,
        filtersAtom,
        (data: number[], filters: { min: number }) => 
          data.filter(item => item >= filters.min)
      )

      const filtered = store.get(filteredAtom)
      expect(filtered).toEqual([3, 4, 5])
    })

    it('should return empty array when data is null', () => {
      const dataAtom = atom(null)
      const filtersAtom = atom({ min: 3 })
      
      const filteredAtom = createFilteredAtom(
        dataAtom,
        filtersAtom,
        (data: number[], filters: { min: number }) => 
          data.filter(item => item >= filters.min)
      )

      const filtered = store.get(filteredAtom)
      expect(filtered).toEqual([])
    })

    it('should react to filter changes', () => {
      const dataAtom = atom([1, 2, 3, 4, 5])
      const filtersAtom = atom({ min: 3 })
      
      const filteredAtom = createFilteredAtom(
        dataAtom,
        filtersAtom,
        (data: number[], filters: { min: number }) => 
          data.filter(item => item >= filters.min)
      )

      expect(store.get(filteredAtom)).toEqual([3, 4, 5])

      store.set(filtersAtom, { min: 4 })
      expect(store.get(filteredAtom)).toEqual([4, 5])
    })
  })

  describe('createComputedAtom', () => {
    it('should create computed atom that computes data correctly', () => {
      const dataAtom = atom([1, 2, 3, 4, 5])
      
      const computedAtom = createComputedAtom(
        dataAtom,
        (data: number[]) => ({
          sum: data.reduce((a, b) => a + b, 0),
          count: data.length,
          average: data.reduce((a, b) => a + b, 0) / data.length
        })
      )

      const computed = store.get(computedAtom)
      expect(computed).toEqual({
        sum: 15,
        count: 5,
        average: 3
      })
    })

    it('should return null when data is null', () => {
      const dataAtom = atom(null)
      
      const computedAtom = createComputedAtom(
        dataAtom,
        (data: number[]) => data.length
      )

      const computed = store.get(computedAtom)
      expect(computed).toBeNull()
    })

    it('should react to data changes', () => {
      const dataAtom = atom([1, 2, 3])
      
      const computedAtom = createComputedAtom(
        dataAtom,
        (data: number[]) => data.reduce((a, b) => a + b, 0)
      )

      expect(store.get(computedAtom)).toBe(6)

      store.set(dataAtom, [1, 2, 3, 4])
      expect(store.get(computedAtom)).toBe(10)
    })
  })

  describe('createOptimisticUpdateAtom', () => {
    it('should create optimistic update atom with successful operation', async () => {
      const dataAtom = atom([1, 2, 3])
      const mockAsyncFn = vi.fn().mockResolvedValue(4)
      const optimisticUpdateFn = (currentData: number[], newItem: number) => [...currentData, newItem]
      
      const optimisticAtom = createOptimisticUpdateAtom(
        dataAtom,
        mockAsyncFn,
        optimisticUpdateFn,
        'TEST_ERROR'
      )

      const result = await store.set(optimisticAtom.optimisticActionAtom, 4)

      expect(mockAsyncFn).toHaveBeenCalledWith(4)
      expect(result).toBe(4)
      expect(store.get(dataAtom)).toEqual([1, 2, 3, 4])
    })

    it('should revert optimistic update on error', async () => {
      const dataAtom = atom([1, 2, 3])
      const mockAsyncFn = vi.fn().mockRejectedValue(new Error('Operation failed'))
      const optimisticUpdateFn = (currentData: number[], newItem: number) => [...currentData, newItem]
      
      const optimisticAtom = createOptimisticUpdateAtom(
        dataAtom,
        mockAsyncFn,
        optimisticUpdateFn,
        'TEST_ERROR'
      )

      await expect(store.set(optimisticAtom.optimisticActionAtom, 4)).rejects.toThrow('Operation failed')

      // Should revert to original data
      expect(store.get(dataAtom)).toEqual([1, 2, 3])
    })

    it('should handle optimistic update with empty initial data', async () => {
      const dataAtom = atom([])
      const mockAsyncFn = vi.fn().mockResolvedValue(1)
      const optimisticUpdateFn = (currentData: number[], newItem: number) => [...currentData, newItem]
      
      const optimisticAtom = createOptimisticUpdateAtom(
        dataAtom,
        mockAsyncFn,
        optimisticUpdateFn,
        'TEST_ERROR'
      )

      await store.set(optimisticAtom.optimisticActionAtom, 1)

      expect(store.get(dataAtom)).toEqual([1])
    })

    it('should handle optimistic update with null initial data', async () => {
      const dataAtom = atom(null)
      const mockAsyncFn = vi.fn().mockResolvedValue(1)
      const optimisticUpdateFn = (currentData: number[] | null, newItem: number) => 
        currentData ? [...currentData, newItem] : [newItem]
      
      const optimisticAtom = createOptimisticUpdateAtom(
        dataAtom,
        mockAsyncFn,
        optimisticUpdateFn,
        'TEST_ERROR'
      )

      await store.set(optimisticAtom.optimisticActionAtom, 1)

      expect(store.get(dataAtom)).toEqual([1])
    })
  })

  describe('createDebouncedAtom', () => {
    beforeEach(() => {
      vi.useFakeTimers()
    })

    afterEach(() => {
      vi.useRealTimers()
    })

    it('should create debounced atom with initial null value', () => {
      const sourceAtom = atom('initial')
      const debouncedAtom = createDebouncedAtom(sourceAtom, 300)

      const value = store.get(debouncedAtom)
      expect(value).toBeNull()
    })

    it('should debounce value updates', () => {
      const sourceAtom = atom('initial')
      const debouncedAtom = createDebouncedAtom(sourceAtom, 300)

      // Set multiple values quickly
      store.set(debouncedAtom, 'value1')
      store.set(debouncedAtom, 'value2')
      store.set(debouncedAtom, 'value3')

      // Value should still be null before timeout
      expect(store.get(debouncedAtom)).toBeNull()

      // Fast-forward time
      vi.advanceTimersByTime(300)

      // Should have the last value
      expect(store.get(debouncedAtom)).toBe('value3')
    })

    it('should cancel previous timeout when new value is set', () => {
      const sourceAtom = atom('initial')
      const debouncedAtom = createDebouncedAtom(sourceAtom, 300)

      store.set(debouncedAtom, 'value1')
      
      // Advance time partially
      vi.advanceTimersByTime(150)
      expect(store.get(debouncedAtom)).toBeNull()

      // Set new value, should cancel previous timeout
      store.set(debouncedAtom, 'value2')
      
      // Advance time to original timeout
      vi.advanceTimersByTime(150)
      expect(store.get(debouncedAtom)).toBeNull()

      // Advance time to new timeout
      vi.advanceTimersByTime(150)
      expect(store.get(debouncedAtom)).toBe('value2')
    })

    it('should use custom delay', () => {
      const sourceAtom = atom('initial')
      const debouncedAtom = createDebouncedAtom(sourceAtom, 500)

      store.set(debouncedAtom, 'test')

      // Should not update before custom delay
      vi.advanceTimersByTime(300)
      expect(store.get(debouncedAtom)).toBeNull()

      // Should update after custom delay
      vi.advanceTimersByTime(200)
      expect(store.get(debouncedAtom)).toBe('test')
    })
  })

  describe('Integration scenarios', () => {
    it('should work together in complex scenarios', async () => {
      // Create a data atom
      const dataAtom = atom<string[]>(['apple', 'banana', 'cherry'])
      
      // Create filters atom
      const filtersAtom = atom({ search: '' })
      
      // Create filtered atom
      const filteredAtom = createFilteredAtom(
        dataAtom,
        filtersAtom,
        (data: string[], filters: { search: string }) =>
          data.filter(item => item.toLowerCase().includes(filters.search.toLowerCase()))
      )
      
      // Create computed atom for statistics
      const statsAtom = createComputedAtom(
        filteredAtom,
        (data: string[]) => ({
          count: data.length,
          totalLength: data.reduce((sum, item) => sum + item.length, 0)
        })
      )
      
      // Create async atom for adding items
      const addItemFn = vi.fn().mockImplementation(async (item: string) => {
        await new Promise(resolve => setTimeout(resolve, 100))
        return item
      })
      
      const addItemAtom = createOptimisticUpdateAtom(
        dataAtom,
        addItemFn,
        (currentData: string[], newItem: string) => [...currentData, newItem],
        'ADD_ITEM_ERROR'
      )

      // Initial state
      expect(store.get(filteredAtom)).toEqual(['apple', 'banana', 'cherry'])
      expect(store.get(statsAtom)).toEqual({ count: 3, totalLength: 17 })

      // Filter data
      store.set(filtersAtom, { search: 'a' })
      expect(store.get(filteredAtom)).toEqual(['apple', 'banana'])
      expect(store.get(statsAtom)).toEqual({ count: 2, totalLength: 11 })

      // Add new item optimistically
      await store.set(addItemAtom.optimisticActionAtom, 'avocado')
      
      expect(store.get(dataAtom)).toEqual(['apple', 'banana', 'cherry', 'avocado'])
      expect(store.get(filteredAtom)).toEqual(['apple', 'banana', 'avocado'])
      expect(store.get(statsAtom)).toEqual({ count: 3, totalLength: 18 })
    })

    it('should handle error propagation correctly', async () => {
      const mockAsyncFn = vi.fn().mockRejectedValue(new Error('Network error'))
      const asyncAtom = createAsyncAtom(mockAsyncFn, 'NETWORK_ERROR')

      await expect(store.set(asyncAtom.actionAtom)).rejects.toThrow('Network error')

      // Check that error is set in both local and global error atoms
      expect(store.get(asyncAtom.errorAtom)).toEqual({
        code: 'NETWORK_ERROR',
        message: 'Network error',
        timestamp: expect.any(Date),
        details: { stack: expect.any(String) }
      })
      expect(store.get(globalErrorAtom)).toBeTruthy()

      // Clear global error
      store.set(clearGlobalErrorAtom)
      expect(store.get(globalErrorAtom)).toBeNull()
    })
  })
})
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { createStore } from 'jotai'
import type { Store } from 'jotai'
import type { Session, User, LoginCredentials, SignupData, AuthError } from '../../types/auth'

// Mock the auth client
const mockAuthClient = {
  signIn: {
    email: vi.fn()
  },
  signUp: {
    email: vi.fn()
  },
  signOut: vi.fn(),
  getSession: vi.fn()
}

vi.mock('../../lib/auth', () => ({
  authClient: mockAuthClient
}))

// Import after mocking
const {
  session<PERSON>tom,
  isLoading<PERSON>tom,
  authErrorAtom,
  isAuthenticated<PERSON>tom,
  user<PERSON>tom,
  userRole<PERSON>tom,
  isAdminAtom,
  userStatusAtom,
  canAccessApi<PERSON>tom,
  login<PERSON>tom,
  signup<PERSON>tom,
  logout<PERSON>tom,
  refreshSessionAtom,
  shouldRefreshSessionAtom,
  updateUserProfileAtom,
  changePasswordAtom,
  updateUserPre<PERSON>s<PERSON>tom,
  clearAuthErrorAtom
} = await import('../auth')

describe('Auth Store', () => {
  let store: Store

  const mockUser: User = {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user',
    status: 'approved',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    apiAccessLevel: 'basic',
    preferences: {
      emailNotifications: true,
      securityAlerts: true,
      loginNotifications: false,
      apiKeyAlerts: true,
      twoFactorEnabled: false
    }
  }

  const mockSession: Session = {
    id: 'session-1',
    userId: 'user-1',
    user: mockUser,
    token: 'mock-token',
    expiresAt: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
    createdAt: new Date()
  }

  const mockAdminUser: User = {
    ...mockUser,
    id: 'admin-1',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin'
  }

  const mockAdminSession: Session = {
    ...mockSession,
    id: 'session-admin',
    userId: 'admin-1',
    user: mockAdminUser
  }

  beforeEach(() => {
    store = createStore()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Base atoms', () => {
    it('should initialize with null session', () => {
      const session = store.get(sessionAtom)
      expect(session).toBeNull()
    })

    it('should initialize with loading false', () => {
      const isLoading = store.get(isLoadingAtom)
      expect(isLoading).toBe(false)
    })

    it('should initialize with no auth error', () => {
      const error = store.get(authErrorAtom)
      expect(error).toBeNull()
    })
  })

  describe('Derived atoms', () => {
    describe('isAuthenticatedAtom', () => {
      it('should return false when no session', () => {
        const isAuthenticated = store.get(isAuthenticatedAtom)
        expect(isAuthenticated).toBe(false)
      })

      it('should return true when valid session exists', () => {
        store.set(sessionAtom, mockSession)
        const isAuthenticated = store.get(isAuthenticatedAtom)
        expect(isAuthenticated).toBe(true)
      })

      it('should return false when session is expired', () => {
        const expiredSession = {
          ...mockSession,
          expiresAt: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
        }
        store.set(sessionAtom, expiredSession)
        const isAuthenticated = store.get(isAuthenticatedAtom)
        expect(isAuthenticated).toBe(false)
      })
    })

    describe('userAtom', () => {
      it('should return null when no session', () => {
        const user = store.get(userAtom)
        expect(user).toBeNull()
      })

      it('should return user when session exists', () => {
        store.set(sessionAtom, mockSession)
        const user = store.get(userAtom)
        expect(user).toEqual(mockUser)
      })
    })

    describe('userRoleAtom', () => {
      it('should return "user" when no session', () => {
        const role = store.get(userRoleAtom)
        expect(role).toBe('user')
      })

      it('should return user role when session exists', () => {
        store.set(sessionAtom, mockSession)
        const role = store.get(userRoleAtom)
        expect(role).toBe('user')
      })

      it('should return admin role for admin user', () => {
        store.set(sessionAtom, mockAdminSession)
        const role = store.get(userRoleAtom)
        expect(role).toBe('admin')
      })
    })

    describe('isAdminAtom', () => {
      it('should return false for regular user', () => {
        store.set(sessionAtom, mockSession)
        const isAdmin = store.get(isAdminAtom)
        expect(isAdmin).toBe(false)
      })

      it('should return true for admin user', () => {
        store.set(sessionAtom, mockAdminSession)
        const isAdmin = store.get(isAdminAtom)
        expect(isAdmin).toBe(true)
      })
    })

    describe('userStatusAtom', () => {
      it('should return "pending" when no session', () => {
        const status = store.get(userStatusAtom)
        expect(status).toBe('pending')
      })

      it('should return user status when session exists', () => {
        store.set(sessionAtom, mockSession)
        const status = store.get(userStatusAtom)
        expect(status).toBe('approved')
      })
    })

    describe('canAccessApiAtom', () => {
      it('should return false for pending user', () => {
        const pendingSession = {
          ...mockSession,
          user: { ...mockUser, status: 'pending' as const }
        }
        store.set(sessionAtom, pendingSession)
        const canAccess = store.get(canAccessApiAtom)
        expect(canAccess).toBe(false)
      })

      it('should return true for approved user', () => {
        store.set(sessionAtom, mockSession)
        const canAccess = store.get(canAccessApiAtom)
        expect(canAccess).toBe(true)
      })

      it('should return false for revoked user', () => {
        const revokedSession = {
          ...mockSession,
          user: { ...mockUser, status: 'revoked' as const }
        }
        store.set(sessionAtom, revokedSession)
        const canAccess = store.get(canAccessApiAtom)
        expect(canAccess).toBe(false)
      })
    })

    describe('shouldRefreshSessionAtom', () => {
      it('should return false when no session', () => {
        const shouldRefresh = store.get(shouldRefreshSessionAtom)
        expect(shouldRefresh).toBe(false)
      })

      it('should return false when session has plenty of time left', () => {
        const futureSession = {
          ...mockSession,
          expiresAt: new Date(Date.now() + 60 * 60 * 1000) // 1 hour from now
        }
        store.set(sessionAtom, futureSession)
        const shouldRefresh = store.get(shouldRefreshSessionAtom)
        expect(shouldRefresh).toBe(false)
      })

      it('should return true when session expires soon', () => {
        const soonToExpireSession = {
          ...mockSession,
          expiresAt: new Date(Date.now() + 2 * 60 * 1000) // 2 minutes from now
        }
        store.set(sessionAtom, soonToExpireSession)
        const shouldRefresh = store.get(shouldRefreshSessionAtom)
        expect(shouldRefresh).toBe(true)
      })
    })
  })

  describe('Action atoms', () => {
    describe('loginAtom', () => {
      const credentials: LoginCredentials = {
        email: '<EMAIL>',
        password: 'password123'
      }

      it('should successfully login with valid credentials', async () => {
        mockAuthClient.signIn.email.mockResolvedValue({
          data: {
            user: mockUser,
            token: 'mock-token',
            expiresAt: mockSession.expiresAt.toISOString()
          }
        })

        const result = await store.set(loginAtom, credentials)

        expect(mockAuthClient.signIn.email).toHaveBeenCalledWith(credentials)
        expect(store.get(sessionAtom)).toBeTruthy()
        expect(store.get(isLoadingAtom)).toBe(false)
        expect(store.get(authErrorAtom)).toBeNull()
        expect(result.user).toEqual(mockUser)
      })

      it('should handle login failure', async () => {
        const errorMessage = 'Invalid credentials'
        mockAuthClient.signIn.email.mockResolvedValue({
          error: { message: errorMessage }
        })

        await expect(store.set(loginAtom, credentials)).rejects.toThrow()

        expect(store.get(sessionAtom)).toBeNull()
        expect(store.get(isLoadingAtom)).toBe(false)
        expect(store.get(authErrorAtom)).toEqual({
          code: 'LOGIN_FAILED',
          message: errorMessage
        })
      })

      it('should handle network error during login', async () => {
        const networkError = new Error('Network error')
        mockAuthClient.signIn.email.mockRejectedValue(networkError)

        await expect(store.set(loginAtom, credentials)).rejects.toThrow()

        expect(store.get(authErrorAtom)).toEqual({
          code: 'LOGIN_FAILED',
          message: 'Network error'
        })
      })

      it('should set loading state during login', async () => {
        let resolveLogin: (value: any) => void
        const loginPromise = new Promise(resolve => {
          resolveLogin = resolve
        })
        mockAuthClient.signIn.email.mockReturnValue(loginPromise)

        const loginAction = store.set(loginAtom, credentials)
        
        // Check loading state is true during async operation
        expect(store.get(isLoadingAtom)).toBe(true)

        resolveLogin!({
          data: {
            user: mockUser,
            token: 'mock-token',
            expiresAt: mockSession.expiresAt.toISOString()
          }
        })

        await loginAction
        expect(store.get(isLoadingAtom)).toBe(false)
      })
    })

    describe('signupAtom', () => {
      const signupData: SignupData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User'
      }

      it('should successfully signup with valid data', async () => {
        mockAuthClient.signUp.email.mockResolvedValue({
          data: {
            user: mockUser,
            token: 'mock-token',
            expiresAt: mockSession.expiresAt.toISOString()
          }
        })

        const result = await store.set(signupAtom, signupData)

        expect(mockAuthClient.signUp.email).toHaveBeenCalledWith(signupData)
        expect(store.get(sessionAtom)).toBeTruthy()
        expect(store.get(authErrorAtom)).toBeNull()
        expect(result.user).toEqual(mockUser)
      })

      it('should handle signup failure', async () => {
        const errorMessage = 'Email already exists'
        mockAuthClient.signUp.email.mockResolvedValue({
          error: { message: errorMessage }
        })

        await expect(store.set(signupAtom, signupData)).rejects.toThrow()

        expect(store.get(authErrorAtom)).toEqual({
          code: 'SIGNUP_FAILED',
          message: errorMessage
        })
      })
    })

    describe('logoutAtom', () => {
      beforeEach(() => {
        store.set(sessionAtom, mockSession)
      })

      it('should successfully logout', async () => {
        mockAuthClient.signOut.mockResolvedValue({})

        await store.set(logoutAtom)

        expect(mockAuthClient.signOut).toHaveBeenCalled()
        expect(store.get(sessionAtom)).toBeNull()
        expect(store.get(authErrorAtom)).toBeNull()
      })

      it('should clear session even if logout request fails', async () => {
        mockAuthClient.signOut.mockRejectedValue(new Error('Logout failed'))

        await store.set(logoutAtom)

        expect(store.get(sessionAtom)).toBeNull()
        expect(store.get(authErrorAtom)).toEqual({
          code: 'LOGOUT_FAILED',
          message: 'Logout failed'
        })
      })
    })

    describe('refreshSessionAtom', () => {
      beforeEach(() => {
        store.set(sessionAtom, mockSession)
      })

      it('should successfully refresh valid session', async () => {
        const refreshedUser = { ...mockUser, updatedAt: new Date() }
        mockAuthClient.getSession.mockResolvedValue({
          data: {
            user: refreshedUser,
            token: 'new-token',
            expiresAt: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString()
          }
        })

        const result = await store.set(refreshSessionAtom)

        expect(mockAuthClient.getSession).toHaveBeenCalled()
        expect(result).toBeTruthy()
        expect(store.get(sessionAtom)?.user).toEqual(refreshedUser)
        expect(store.get(authErrorAtom)).toBeNull()
      })

      it('should clear session when refresh fails', async () => {
        mockAuthClient.getSession.mockResolvedValue({
          error: { message: 'Session expired' }
        })

        const result = await store.set(refreshSessionAtom)

        expect(result).toBeNull()
        expect(store.get(sessionAtom)).toBeNull()
      })

      it('should return null when no current session', async () => {
        store.set(sessionAtom, null)

        const result = await store.set(refreshSessionAtom)

        expect(result).toBeNull()
        expect(mockAuthClient.getSession).not.toHaveBeenCalled()
      })
    })

    describe('updateUserProfileAtom', () => {
      beforeEach(() => {
        store.set(sessionAtom, mockSession)
      })

      it('should successfully update user profile', async () => {
        const profileUpdates = { name: 'Updated Name', bio: 'New bio' }

        const result = await store.set(updateUserProfileAtom, profileUpdates)

        expect(result.name).toBe('Updated Name')
        expect(result.bio).toBe('New bio')
        expect(store.get(sessionAtom)?.user.name).toBe('Updated Name')
      })

      it('should throw error when not authenticated', async () => {
        store.set(sessionAtom, null)

        await expect(
          store.set(updateUserProfileAtom, { name: 'New Name' })
        ).rejects.toThrow('User not authenticated')
      })
    })

    describe('changePasswordAtom', () => {
      beforeEach(() => {
        store.set(sessionAtom, mockSession)
      })

      it('should successfully change password', async () => {
        const passwordData = {
          currentPassword: 'oldpassword',
          newPassword: 'newpassword'
        }

        const result = await store.set(changePasswordAtom, passwordData)

        expect(result.success).toBe(true)
        expect(store.get(authErrorAtom)).toBeNull()
      })

      it('should throw error when not authenticated', async () => {
        store.set(sessionAtom, null)

        await expect(
          store.set(changePasswordAtom, {
            currentPassword: 'old',
            newPassword: 'new'
          })
        ).rejects.toThrow('User not authenticated')
      })
    })

    describe('updateUserPreferencesAtom', () => {
      beforeEach(() => {
        store.set(sessionAtom, mockSession)
      })

      it('should successfully update user preferences', async () => {
        const newPreferences = {
          emailNotifications: false,
          twoFactorEnabled: true
        }

        const result = await store.set(updateUserPreferencesAtom, newPreferences)

        expect(result.preferences?.emailNotifications).toBe(false)
        expect(result.preferences?.twoFactorEnabled).toBe(true)
        expect(store.get(sessionAtom)?.user.preferences?.emailNotifications).toBe(false)
      })
    })

    describe('clearAuthErrorAtom', () => {
      it('should clear auth error', () => {
        const error: AuthError = {
          code: 'TEST_ERROR',
          message: 'Test error message'
        }
        store.set(authErrorAtom, error)

        store.set(clearAuthErrorAtom)

        expect(store.get(authErrorAtom)).toBeNull()
      })
    })
  })

  describe('Integration scenarios', () => {
    it('should handle complete login flow', async () => {
      // Initial state
      expect(store.get(isAuthenticatedAtom)).toBe(false)
      expect(store.get(userAtom)).toBeNull()

      // Mock successful login
      mockAuthClient.signIn.email.mockResolvedValue({
        data: {
          user: mockUser,
          token: 'mock-token',
          expiresAt: mockSession.expiresAt.toISOString()
        }
      })

      // Perform login
      await store.set(loginAtom, {
        email: '<EMAIL>',
        password: 'password123'
      })

      // Verify authenticated state
      expect(store.get(isAuthenticatedAtom)).toBe(true)
      expect(store.get(userAtom)).toEqual(mockUser)
      expect(store.get(userRoleAtom)).toBe('user')
      expect(store.get(canAccessApiAtom)).toBe(true)
    })

    it('should handle session expiry and refresh', async () => {
      // Set session that will expire soon
      const expiringSoonSession = {
        ...mockSession,
        expiresAt: new Date(Date.now() + 2 * 60 * 1000) // 2 minutes
      }
      store.set(sessionAtom, expiringSoonSession)

      // Should indicate refresh needed
      expect(store.get(shouldRefreshSessionAtom)).toBe(true)

      // Mock successful refresh
      mockAuthClient.getSession.mockResolvedValue({
        data: {
          user: mockUser,
          token: 'refreshed-token',
          expiresAt: new Date(Date.now() + 60 * 60 * 1000).toISOString()
        }
      })

      // Perform refresh
      await store.set(refreshSessionAtom)

      // Should no longer need refresh
      expect(store.get(shouldRefreshSessionAtom)).toBe(false)
      expect(store.get(isAuthenticatedAtom)).toBe(true)
    })

    it('should handle role-based access control', async () => {
      // Test regular user
      store.set(sessionAtom, mockSession)
      expect(store.get(isAdminAtom)).toBe(false)

      // Test admin user
      store.set(sessionAtom, mockAdminSession)
      expect(store.get(isAdminAtom)).toBe(true)
    })
  })
})
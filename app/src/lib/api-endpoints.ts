// API endpoint constants and configuration
export const API_BASE_URL = import.meta.env['VITE_API_BASE_URL'] || '/api'

// API Endpoints
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    SESSION: '/auth/session',
    SIGNUP: '/auth/signup'
  },

  // User Management
  USERS: {
    BASE: '/users',
    BY_ID: (id: string) => `/users/${id}`,
    STATS: '/users/stats',
    ACTIVITIES: '/users/activities',
    BULK_ACTIONS: '/users/bulk-actions'
  },

  // API Key Management
  API_KEYS: {
    BASE: '/api-keys',
    BY_ID: (id: string) => `/api-keys/${id}`,
    BY_USER: (userId: string) => `/users/${userId}/api-keys`,
    REGENERATE: (id: string) => `/api-keys/${id}/regenerate`,
    REVOKE: (id: string) => `/api-keys/${id}/revoke`,
    STATS: '/api-keys/stats',
    USAGE: (id: string) => `/api-keys/${id}/usage`
  },

  // Analytics and Usage
  ANALYTICS: {
    USAGE_LOGS: '/analytics/usage-logs',
    USAGE_METRICS: '/analytics/usage-metrics',
    OLLAMA_METRICS: '/analytics/ollama-metrics',
    SYSTEM_METRICS: '/analytics/system-metrics',
    TREND_DATA: '/analytics/trend-data',
    EXPORT: '/analytics/export'
  },

  // Ollama Proxy
  OLLAMA: {
    GENERATE: '/ollama/generate',
    CHAT: '/ollama/chat',
    MODELS: '/ollama/models',
    EMBEDDINGS: '/ollama/embeddings'
  },

  // WebSocket endpoints
  WEBSOCKET: {
    USAGE_UPDATES: '/ws/usage-updates',
    SYSTEM_STATUS: '/ws/system-status',
    NOTIFICATIONS: '/ws/notifications'
  }
} as const

// Request timeout configurations
export const TIMEOUT_CONFIG = {
  DEFAULT: 10000, // 10 seconds
  UPLOAD: 30000, // 30 seconds
  LONG_RUNNING: 60000, // 1 minute
  WEBSOCKET: 5000 // 5 seconds for initial connection
} as const

// Retry configuration
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second base delay
  RETRY_MULTIPLIER: 2, // Exponential backoff
  RETRYABLE_STATUS_CODES: [408, 429, 500, 502, 503, 504],
  RETRYABLE_METHODS: ['GET', 'PUT', 'DELETE'] // Don't retry POST by default
} as const
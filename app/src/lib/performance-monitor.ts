// Performance monitoring utilities for production optimization

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  url?: string
  userId?: string
}

interface ChartRenderMetric {
  chartType: string
  renderTime: number
  dataPoints: number
  timestamp: number
}

interface UserInteraction {
  type: 'click' | 'navigation' | 'form_submit' | 'chart_interaction'
  element: string
  timestamp: number
  userId?: string
  metadata?: Record<string, any>
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private chartMetrics: ChartRenderMetric[] = []
  private interactions: UserInteraction[] = []
  private isEnabled: boolean = false

  constructor() {
    this.isEnabled = process.env['NODE_ENV'] === 'production'
    if (this.isEnabled) {
      this.initializeWebVitals()
      this.setupNavigationObserver()
    }
  }

  // Initialize Web Vitals monitoring
  private initializeWebVitals() {
    // Mock Web Vitals implementation since we don't have the library
    this.observePerformanceEntries()
  }

  private observePerformanceEntries() {
    if ('PerformanceObserver' in window) {
      // Observe Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        this.recordMetric('LCP', lastEntry.startTime)
      })
      
      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      } catch (e) {
        // Fallback for browsers that don't support LCP
      }

      // Observe First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          this.recordMetric('FID', entry.processingStart - entry.startTime)
        })
      })

      try {
        fidObserver.observe({ entryTypes: ['first-input'] })
      } catch (e) {
        // Fallback for browsers that don't support FID
      }

      // Observe Cumulative Layout Shift (CLS)
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
        this.recordMetric('CLS', clsValue)
      })

      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (e) {
        // Fallback for browsers that don't support CLS
      }
    }
  }

  private setupNavigationObserver() {
    // Monitor navigation timing
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        this.recordMetric('TTFB', navigation.responseStart - navigation.requestStart)
        this.recordMetric('DOMContentLoaded', navigation.domContentLoadedEventEnd - navigation.navigationStart)
        this.recordMetric('LoadComplete', navigation.loadEventEnd - navigation.navigationStart)
      }
    })
  }

  // Record a performance metric
  recordMetric(name: string, value: number, url?: string, userId?: string) {
    if (!this.isEnabled) return

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url: url || window.location.pathname,
      userId
    }

    this.metrics.push(metric)
    
    // Keep only last 100 metrics to prevent memory leaks
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100)
    }

    // Send to analytics if configured
    this.sendMetricToAnalytics(metric)
  }

  // Record chart rendering performance
  recordChartRender(chartType: string, renderTime: number, dataPoints: number) {
    if (!this.isEnabled) return

    const metric: ChartRenderMetric = {
      chartType,
      renderTime,
      dataPoints,
      timestamp: Date.now()
    }

    this.chartMetrics.push(metric)
    
    // Keep only last 50 chart metrics
    if (this.chartMetrics.length > 50) {
      this.chartMetrics = this.chartMetrics.slice(-50)
    }

    // Log slow chart renders
    if (renderTime > 1000) { // > 1 second
      console.warn(`Slow chart render detected: ${chartType} took ${renderTime}ms for ${dataPoints} data points`)
    }
  }

  // Record user interactions
  recordInteraction(type: UserInteraction['type'], element: string, metadata?: Record<string, any>, userId?: string) {
    if (!this.isEnabled) return

    const interaction: UserInteraction = {
      type,
      element,
      timestamp: Date.now(),
      userId,
      metadata
    }

    this.interactions.push(interaction)
    
    // Keep only last 200 interactions
    if (this.interactions.length > 200) {
      this.interactions = this.interactions.slice(-200)
    }
  }

  // Get performance summary
  getPerformanceSummary() {
    const now = Date.now()
    const last5Minutes = now - (5 * 60 * 1000)

    const recentMetrics = this.metrics.filter(m => m.timestamp > last5Minutes)
    const recentChartMetrics = this.chartMetrics.filter(m => m.timestamp > last5Minutes)
    const recentInteractions = this.interactions.filter(i => i.timestamp > last5Minutes)

    return {
      webVitals: this.getWebVitalsSummary(recentMetrics),
      chartPerformance: this.getChartPerformanceSummary(recentChartMetrics),
      userActivity: this.getUserActivitySummary(recentInteractions),
      totalMetrics: this.metrics.length,
      totalChartRenders: this.chartMetrics.length,
      totalInteractions: this.interactions.length
    }
  }

  private getWebVitalsSummary(metrics: PerformanceMetric[]) {
    const vitals = ['LCP', 'FID', 'CLS', 'TTFB', 'DOMContentLoaded', 'LoadComplete']
    const summary: Record<string, { value: number; count: number; average: number }> = {}

    vitals.forEach(vital => {
      const vitalMetrics = metrics.filter(m => m.name === vital)
      if (vitalMetrics.length > 0) {
        const values = vitalMetrics.map(m => m.value)
        summary[vital] = {
          value: values[values.length - 1], // Latest value
          count: values.length,
          average: values.reduce((a, b) => a + b, 0) / values.length
        }
      }
    })

    return summary
  }

  private getChartPerformanceSummary(metrics: ChartRenderMetric[]) {
    const chartTypes = [...new Set(metrics.map(m => m.chartType))]
    const summary: Record<string, { averageRenderTime: number; totalRenders: number; slowRenders: number }> = {}

    chartTypes.forEach(type => {
      const typeMetrics = metrics.filter(m => m.chartType === type)
      const renderTimes = typeMetrics.map(m => m.renderTime)
      
      summary[type] = {
        averageRenderTime: renderTimes.reduce((a, b) => a + b, 0) / renderTimes.length,
        totalRenders: typeMetrics.length,
        slowRenders: typeMetrics.filter(m => m.renderTime > 1000).length
      }
    })

    return summary
  }

  private getUserActivitySummary(interactions: UserInteraction[]) {
    const interactionTypes = [...new Set(interactions.map(i => i.type))]
    const summary: Record<string, number> = {}

    interactionTypes.forEach(type => {
      summary[type] = interactions.filter(i => i.type === type).length
    })

    return summary
  }

  private sendMetricToAnalytics(metric: PerformanceMetric) {
    // In a real implementation, this would send to your analytics service
    // For now, we'll just log critical performance issues
    if (metric.name === 'LCP' && metric.value > 2500) {
      console.warn('Poor LCP detected:', metric.value, 'ms')
    }
    if (metric.name === 'FID' && metric.value > 100) {
      console.warn('Poor FID detected:', metric.value, 'ms')
    }
    if (metric.name === 'CLS' && metric.value > 0.1) {
      console.warn('Poor CLS detected:', metric.value)
    }
  }

  // Export metrics for external analysis
  exportMetrics() {
    return {
      metrics: this.metrics,
      chartMetrics: this.chartMetrics,
      interactions: this.interactions,
      summary: this.getPerformanceSummary()
    }
  }

  // Clear all stored metrics
  clearMetrics() {
    this.metrics = []
    this.chartMetrics = []
    this.interactions = []
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor()

// Utility functions for easy use in components
export const recordMetric = (name: string, value: number, url?: string, userId?: string) => {
  performanceMonitor.recordMetric(name, value, url, userId)
}

export const recordChartRender = (chartType: string, renderTime: number, dataPoints: number) => {
  performanceMonitor.recordChartRender(chartType, renderTime, dataPoints)
}

export const recordInteraction = (type: UserInteraction['type'], element: string, metadata?: Record<string, any>, userId?: string) => {
  performanceMonitor.recordInteraction(type, element, metadata, userId)
}

export const getPerformanceSummary = () => {
  return performanceMonitor.getPerformanceSummary()
}

// Hook for React components to measure render time
export const usePerformanceTimer = (componentName: string) => {
  const startTime = performance.now()
  
  return {
    end: () => {
      const endTime = performance.now()
      const renderTime = endTime - startTime
      recordMetric(`Component_${componentName}_Render`, renderTime)
      return renderTime
    }
  }
}
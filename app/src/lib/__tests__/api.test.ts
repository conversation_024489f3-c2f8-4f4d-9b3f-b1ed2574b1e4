// Basic API client tests
import { describe, it, expect, beforeEach } from 'vitest'
import { ApiClient } from '../api'

describe('ApiClient', () => {
  let apiClient: ApiClient

  beforeEach(() => {
    apiClient = new ApiClient('/api')
  })

  it('should create an instance', () => {
    expect(apiClient).toBeDefined()
    expect(apiClient.getAuthToken()).toBeNull()
  })

  it('should set and get auth token', () => {
    const token = 'test-token'
    apiClient.setAuthToken(token)
    expect(apiClient.getAuthToken()).toBe(token)
  })

  it('should clear auth token', () => {
    apiClient.setAuthToken('test-token')
    apiClient.setAuthToken(null)
    expect(apiClient.getAuthToken()).toBeNull()
  })

  it('should fetch users with mock data', async () => {
    const result = await apiClient.getUsers()
    expect(result).toBeDefined()
    expect(result.data).toBeInstanceOf(Array)
    expect(result.total).toBeGreaterThan(0)
  })

  it('should fetch user by id with mock data', async () => {
    const user = await apiClient.getUserById('1')
    expect(user).toBeDefined()
    expect(user.id).toBe('1')
    expect(user.email).toContain('<EMAIL>')
  })

  it('should fetch API keys with mock data', async () => {
    const keys = await apiClient.getApiKeys()
    expect(keys).toBeDefined()
    expect(keys).toBeInstanceOf(Array)
  })
})
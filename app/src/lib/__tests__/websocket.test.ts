// Basic WebSocket client tests
import { describe, it, expect, beforeEach } from 'vitest'
import { WebSocketClient } from '../websocket'

// Mock WebSocket for testing
global.WebSocket = class MockWebSocket {
  static CONNECTING = 0
  static OPEN = 1
  static CLOSING = 2
  static CLOSED = 3

  readyState = MockWebSocket.CLOSED
  onopen: ((event: Event) => void) | null = null
  onclose: ((event: CloseEvent) => void) | null = null
  onmessage: ((event: MessageEvent) => void) | null = null
  onerror: ((event: Event) => void) | null = null

  constructor(public url: string, public protocols?: string[]) {
    // Mock implementation
  }

  send(data: string) {
    // Mock implementation
  }

  close(code?: number, reason?: string) {
    this.readyState = MockWebSocket.CLOSED
  }
} as any

describe('WebSocketClient', () => {
  let wsClient: WebSocketClient

  beforeEach(() => {
    wsClient = new WebSocketClient({
      url: 'ws://localhost:3000/ws/test'
    })
  })

  it('should create an instance', () => {
    expect(wsClient).toBeDefined()
    expect(wsClient.getStatus()).toBe('closed')
  })

  it('should set and get auth token', () => {
    const token = 'test-token'
    wsClient.setAuthToken(token)
    // We can't directly test the private config, but we can verify the method exists
    expect(wsClient.setAuthToken).toBeDefined()
  })

  it('should check connection status', () => {
    expect(wsClient.isConnected()).toBe(false)
    expect(wsClient.getStatus()).toBe('closed')
  })

  it('should handle event handlers', () => {
    const handlers = {
      onConnect: () => {},
      onDisconnect: () => {},
      onMessage: () => {}
    }
    
    expect(() => wsClient.on(handlers)).not.toThrow()
  })
})
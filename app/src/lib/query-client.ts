// Query client configuration and cache management
import { QueryClient } from '@tanstack/react-query'
import { CACHE_CONFIG } from '../hooks/useApiQuery'
import { ApiError, NetworkError, TimeoutError } from './api-errors'

// Default query client configuration
export const queryClientConfig = {
  defaultOptions: {
    queries: {
      staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM,
      gcTime: CACHE_CONFIG.GC_TIME.MEDIUM,
      retry: (failureCount: number, error: Error) => {
        // Don't retry on authentication/authorization errors
        if (error instanceof ApiError) {
          if (error.isAuthenticationError() || error.isAuthorizationError()) {
            return false
          }
          // Don't retry on validation errors
          if (error.isValidationError()) {
            return false
          }
          // Don't retry on not found errors
          if (error.isNotFoundError()) {
            return false
          }
        }
        
        // Retry network errors and server errors up to 3 times
        if (error instanceof NetworkError || error instanceof TimeoutError) {
          return failureCount < 3
        }
        
        if (error instanceof ApiError && error.isServerError()) {
          return failureCount < 3
        }
        
        return false
      },
      retryDelay: (attemptIndex: number) => {
        // Exponential backoff: 1s, 2s, 4s
        return Math.min(1000 * Math.pow(2, attemptIndex), 30000)
      }
    },
    mutations: {
      retry: (failureCount: number, error: Error) => {
        // Generally don't retry mutations to avoid duplicate operations
        // Only retry on network errors
        if (error instanceof NetworkError || error instanceof TimeoutError) {
          return failureCount < 2
        }
        return false
      }
    }
  }
}

// Create query client instance
export const queryClient = new QueryClient(queryClientConfig)

// Cache management utilities
export class CacheManager {
  constructor(private client: QueryClient) {}

  // Invalidate all user-related queries
  invalidateUsers(): Promise<void> {
    return this.client.invalidateQueries({ queryKey: ['users'] })
  }

  // Invalidate specific user
  invalidateUser(userId: string): Promise<void> {
    return this.client.invalidateQueries({ 
      queryKey: ['users', 'detail', userId] 
    })
  }

  // Invalidate all API key queries
  invalidateApiKeys(): Promise<void> {
    return this.client.invalidateQueries({ queryKey: ['apiKeys'] })
  }

  // Invalidate specific API key
  invalidateApiKey(keyId: string): Promise<void> {
    return this.client.invalidateQueries({ 
      queryKey: ['apiKeys', 'detail', keyId] 
    })
  }

  // Invalidate user's API keys
  invalidateUserApiKeys(userId: string): Promise<void> {
    return this.client.invalidateQueries({ 
      queryKey: ['apiKeys', 'list'],
      predicate: (query) => {
        const queryKey = query.queryKey as any[]
        return queryKey.some(key => 
          typeof key === 'object' && key?.userId === userId
        )
      }
    })
  }

  // Invalidate analytics data
  invalidateAnalytics(): Promise<void> {
    return this.client.invalidateQueries({ queryKey: ['analytics'] })
  }

  // Invalidate analytics for specific time range
  invalidateAnalyticsForTimeRange(timeRange: { start: Date; end: Date }): Promise<void> {
    return this.client.invalidateQueries({ 
      queryKey: ['analytics'],
      predicate: (query) => {
        const queryKey = query.queryKey as any[]
        return queryKey.some(key => 
          typeof key === 'object' && 
          key?.timeRange &&
          this.timeRangesOverlap(key.timeRange, timeRange)
        )
      }
    })
  }

  // Remove all cached data
  clear(): void {
    this.client.clear()
  }

  // Remove stale data
  removeStaleQueries(): void {
    this.client.removeQueries({ 
      predicate: (query) => query.isStale() 
    })
  }

  // Get cache statistics
  getCacheStats() {
    const queryCache = this.client.getQueryCache()
    const queries = queryCache.getAll()
    
    const stats = {
      totalQueries: queries.length,
      staleQueries: queries.filter(q => q.isStale()).length,
      fetchingQueries: queries.filter(q => q.isFetching()).length,
      errorQueries: queries.filter(q => q.state.status === 'error').length,
      successQueries: queries.filter(q => q.state.status === 'success').length,
      cacheSize: this.estimateCacheSize(queries)
    }
    
    return stats
  }

  // Prefetch data
  async prefetchUsers(filters?: any, page = 1, limit = 10): Promise<void> {
    await this.client.prefetchQuery({
      queryKey: ['users', 'list', { filters, page, limit }],
      queryFn: () => {
        // This would use the API client
        return Promise.resolve({ data: [], total: 0, page, limit, totalPages: 0 })
      },
      staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM
    })
  }

  async prefetchApiKeys(filters?: any, userId?: string): Promise<void> {
    await this.client.prefetchQuery({
      queryKey: ['apiKeys', 'list', { filters, userId }],
      queryFn: () => {
        // This would use the API client
        return Promise.resolve([])
      },
      staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM
    })
  }

  // Background refetch for critical data
  async backgroundRefresh(): Promise<void> {
    const criticalQueries = [
      ['users', 'stats'],
      ['apiKeys', 'stats'],
      ['analytics', 'systemMetrics']
    ]

    await Promise.allSettled(
      criticalQueries.map(queryKey =>
        this.client.refetchQueries({ queryKey })
      )
    )
  }

  private timeRangesOverlap(
    range1: { start: Date; end: Date }, 
    range2: { start: Date; end: Date }
  ): boolean {
    return range1.start <= range2.end && range2.start <= range1.end
  }

  private estimateCacheSize(queries: any[]): string {
    // Rough estimation of cache size
    const totalEntries = queries.reduce((sum, query) => {
      return sum + (query.state.data ? 1 : 0)
    }, 0)
    
    // Assume average 1KB per entry (very rough estimate)
    const sizeKB = totalEntries * 1
    
    if (sizeKB < 1024) {
      return `${sizeKB} KB`
    } else {
      return `${(sizeKB / 1024).toFixed(1)} MB`
    }
  }
}

// Export cache manager instance
export const cacheManager = new CacheManager(queryClient)

// Cache warming utilities
export class CacheWarmer {
  constructor(private client: QueryClient) {}

  // Warm cache on app startup
  async warmCache(): Promise<void> {
    try {
      // Prefetch critical data that's likely to be needed
      await Promise.allSettled([
        cacheManager.prefetchUsers(),
        // Add other critical prefetch operations
      ])
    } catch (error) {
      console.warn('Cache warming failed:', error)
    }
  }

  // Warm cache for specific user session
  async warmUserCache(userId: string): Promise<void> {
    try {
      await Promise.allSettled([
        cacheManager.prefetchApiKeys(undefined, userId),
        // Add other user-specific prefetch operations
      ])
    } catch (error) {
      console.warn('User cache warming failed:', error)
    }
  }
}

export const cacheWarmer = new CacheWarmer(queryClient)

// Periodic cache maintenance
export function startCacheMaintenance(): () => void {
  const interval = setInterval(() => {
    // Remove stale queries every 5 minutes
    cacheManager.removeStaleQueries()
    
    // Log cache stats in development
    if (import.meta.env.DEV) {
      const stats = cacheManager.getCacheStats()
      console.log('Cache stats:', stats)
    }
  }, 5 * 60 * 1000) // 5 minutes

  // Return cleanup function
  return () => clearInterval(interval)
}
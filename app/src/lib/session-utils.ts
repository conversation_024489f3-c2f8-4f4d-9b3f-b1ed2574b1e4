import { authClient } from './auth'
import type { Session, User } from '../types/auth'

/**
 * Utility function to validate a session and return a properly typed Session object
 * This is useful when you need to validate sessions outside of the Jotai store
 */
export const validateSession = async (): Promise<Session | null> => {
    try {
        const response = await authClient.getSession()

        if (response.data?.session) {
            return {
                id: response.data.session.id,
                userId: response.data.user.id,
                user: {
                    ...response.data.user,
                    role: response.data.user.role as 'admin' | 'user' | 'whitelisted' || 'user'
                } as User,
                token: response.data.session.token,
                createdAt: new Date(response.data.session.createdAt),
                expiresAt: new Date(response.data.session.expiresAt),
            }
        }

        return null
    } catch (error) {
        console.warn('Session validation failed:', error)
        return null
    }
}

/**
 * Check if localStorage has been modified manually by comparing with a stored hash
 * This is useful for detecting when someone manually edits localStorage
 */
export const detectLocalStorageManipulation = (): boolean => {
    try {
        const storedSession = localStorage.getItem('auth-session')
        const lastKnownHash = localStorage.getItem('auth-session-hash')

        if (!storedSession) return false

        // Create a simple hash of the session data
        const currentHash = btoa(storedSession).slice(0, 20)

        if (lastKnownHash && lastKnownHash !== currentHash) {
            // Update the hash for next check
            localStorage.setItem('auth-session-hash', currentHash)
            return true
        }

        // Initialize hash if it doesn't exist
        if (!lastKnownHash) {
            localStorage.setItem('auth-session-hash', currentHash)
        }

        return false
    } catch (error) {
        console.warn('Error detecting localStorage manipulation:', error)
        return false
    }
}

/**
 * Clear all authentication data from localStorage
 */
export const clearAuthStorage = (): void => {
    localStorage.removeItem('auth-session')
    localStorage.removeItem('auth-session-hash')
}

/**
 * Initialize session hash tracking
 * Call this when the app starts to begin tracking localStorage changes
 */
export const initializeSessionTracking = (): void => {
    const storedSession = localStorage.getItem('auth-session')
    if (storedSession) {
        const hash = btoa(storedSession).slice(0, 20)
        localStorage.setItem('auth-session-hash', hash)
    }
}

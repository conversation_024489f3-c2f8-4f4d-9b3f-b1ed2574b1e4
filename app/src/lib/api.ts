// Enhanced API client with Axios, error handling, and retry logic
import axios from 'axios'
import type { 
  AxiosInstance, 
  AxiosRequestConfig, 
  AxiosResponse, 
  InternalAxiosRequestConfig 
} from 'axios'
import type { User } from '../types/auth'
import type { 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserActionRequest, 
  UserStats, 
  UserActivity,
  UserFilters 
} from '../types/user'
import type { 
  <PERSON><PERSON><PERSON><PERSON>, 
  CreateApiKeyRequest, 
  UpdateApiKeyRequest, 
  ApiKeyStats, 
  ApiKeyUsage,
  ApiKeyFilters 
} from '../types/apiKey'
import type { 
  ApiUsageLog, 
  UsageMetrics, 
  OllamaPerformanceMetrics, 
  SystemMetrics,
  AnalyticsQuery,
  AnalyticsFilters 
} from '../types/analytics'
import type { PaginatedResponse, TimeRange } from '../types/api'
import { 
  API_BASE_URL, 
  API_ENDPOINTS, 
  TIMEOUT_CONFIG, 
  RETRY_CONFIG 
} from './api-endpoints'
import { 
  ApiError, 
  NetworkError, 
  TimeoutError, 
  RetryExhaustedError,
  transformAxiosError, 
  logError 
} from './api-errors'

// Enhanced API client with Axios, error handling, and retry logic
export class ApiClient {
  private axiosInstance: AxiosInstance
  private authToken: string | null = null

  constructor(baseUrl: string = API_BASE_URL) {
    this.axiosInstance = axios.create({
      baseURL: baseUrl,
      timeout: TIMEOUT_CONFIG.DEFAULT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors(): void {
    // Request interceptor for authentication and logging
    this.axiosInstance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // Add authentication token if available
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`
        }

        // Add request ID for tracking
        config.headers['X-Request-ID'] = this.generateRequestId()

        // Log request in development
        if (import.meta.env.DEV) {
          console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`, {
            headers: config.headers,
            data: config.data
          })
        }

        return config
      },
      (error) => {
        logError(error, { context: 'request_interceptor' })
        return Promise.reject(error)
      }
    )

    // Response interceptor for error handling and logging
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        // Log successful response in development
        if (import.meta.env.DEV) {
          console.log(`API Response: ${response.status} ${response.config.url}`, {
            data: response.data,
            headers: response.headers
          })
        }

        return response
      },
      async (error) => {
        const transformedError = transformAxiosError(error)
        
        // Log error
        logError(transformedError, {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status
        })

        // Handle authentication errors
        if (transformedError instanceof ApiError && transformedError.isAuthenticationError()) {
          this.handleAuthenticationError()
        }

        return Promise.reject(transformedError)
      }
    )
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  }

  private handleAuthenticationError(): void {
    // Clear stored auth token
    this.authToken = null
    
    // Emit authentication error event for global handling
    window.dispatchEvent(new CustomEvent('auth:error', {
      detail: { message: 'Authentication failed' }
    }))
  }

  // Set authentication token
  public setAuthToken(token: string | null): void {
    this.authToken = token
  }

  // Get current auth token
  public getAuthToken(): string | null {
    return this.authToken
  }

  // Generic request method with retry logic
  private async request<T>(
    config: AxiosRequestConfig,
    retryCount = 0
  ): Promise<T> {
    try {
      const response = await this.axiosInstance.request<T>(config)
      return response.data
    } catch (error) {
      // Check if error is retryable and we haven't exceeded max retries
      if (
        retryCount < RETRY_CONFIG.MAX_RETRIES &&
        this.isRetryableError(error) &&
        this.isRetryableMethod(config.method)
      ) {
        const delay = this.calculateRetryDelay(retryCount)
        
        if (import.meta.env.DEV) {
          console.log(`Retrying request in ${delay}ms (attempt ${retryCount + 1}/${RETRY_CONFIG.MAX_RETRIES})`)
        }

        await this.sleep(delay)
        return this.request<T>(config, retryCount + 1)
      }

      // If we've exhausted retries, throw a RetryExhaustedError
      if (retryCount >= RETRY_CONFIG.MAX_RETRIES) {
        throw new RetryExhaustedError(RETRY_CONFIG.MAX_RETRIES, error as Error)
      }

      throw error
    }
  }

  private isRetryableError(error: any): boolean {
    if (error instanceof ApiError) {
      return error.isRetryable()
    }
    
    if (error instanceof NetworkError || error instanceof TimeoutError) {
      return true
    }

    return false
  }

  private isRetryableMethod(method?: string): boolean {
    if (!method) return false
    return RETRY_CONFIG.RETRYABLE_METHODS.includes(method.toUpperCase() as any)
  }

  private calculateRetryDelay(retryCount: number): number {
    return RETRY_CONFIG.RETRY_DELAY * Math.pow(RETRY_CONFIG.RETRY_MULTIPLIER, retryCount)
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  // HTTP method helpers
  private async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url })
  }

  private async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data })
  }

  private async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data })
  }

  private async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PATCH', url, data })
  }

  private async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url })
  }

  // User Management API
  async getUsers(filters?: UserFilters, page = 1, limit = 10): Promise<PaginatedResponse<User>> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<PaginatedResponse<User>>(API_ENDPOINTS.USERS.BASE, {
    //   params: { ...filters, page, limit }
    // })
    
    // Mock implementation for development
    const mockUsers: User[] = [
      {
        id: '1',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin',
        status: 'approved',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-15'),
        lastLoginAt: new Date('2024-01-15'),
        apiAccessLevel: 'premium'
      },
      {
        id: '2',
        email: '<EMAIL>',
        name: 'Regular User',
        role: 'user',
        status: 'approved',
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-12'),
        lastLoginAt: new Date('2024-01-14'),
        apiAccessLevel: 'basic'
      },
      {
        id: '3',
        email: '<EMAIL>',
        name: 'Pending User',
        role: 'user',
        status: 'pending',
        createdAt: new Date('2024-01-14'),
        updatedAt: new Date('2024-01-14'),
        apiAccessLevel: 'none'
      }
    ]

    // Apply filters
    let filteredUsers = mockUsers
    if (filters?.status && filters.status !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.status === filters.status)
    }
    if (filters?.role && filters.role !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.role === filters.role)
    }
    if (filters?.search) {
      const search = filters.search.toLowerCase()
      filteredUsers = filteredUsers.filter(user => 
        user.name.toLowerCase().includes(search) || 
        user.email.toLowerCase().includes(search)
      )
    }

    const total = filteredUsers.length
    const startIndex = (page - 1) * limit
    const paginatedUsers = filteredUsers.slice(startIndex, startIndex + limit)

    return {
      data: paginatedUsers,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }

  async getUserById(id: string): Promise<User> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<User>(API_ENDPOINTS.USERS.BY_ID(id))
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      id,
      email: `user${id}@example.com`,
      name: `User ${id}`,
      role: 'user',
      status: 'approved',
      createdAt: new Date(),
      updatedAt: new Date(),
      apiAccessLevel: 'basic'
    }
  }

  async createUser(userData: CreateUserRequest): Promise<User> {
    // TODO: Replace with real API call when backend is ready
    // return this.post<User>(API_ENDPOINTS.USERS.BASE, userData)
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 1000))
    return {
      id: Date.now().toString(),
      email: userData.email,
      name: userData.name,
      role: userData.role,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date(),
      apiAccessLevel: userData.apiAccessLevel
    }
  }

  async updateUser(userData: UpdateUserRequest): Promise<User> {
    // TODO: Replace with real API call when backend is ready
    // return this.put<User>(API_ENDPOINTS.USERS.BY_ID(userData.id), userData)
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 800))
    return {
      id: userData.id,
      email: `updated${userData.id}@example.com`,
      name: userData.name || `Updated User ${userData.id}`,
      role: userData.role || 'user',
      status: userData.status || 'approved',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
      apiAccessLevel: userData.apiAccessLevel || 'basic'
    }
  }

  async performUserAction(action: UserActionRequest): Promise<User> {
    // TODO: Replace with real API call when backend is ready
    // return this.post<User>(API_ENDPOINTS.USERS.BULK_ACTIONS, action)
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 600))
    return {
      id: action.userId,
      email: `user${action.userId}@example.com`,
      name: `User ${action.userId}`,
      role: action.action === 'promote' ? 'admin' : 'user',
      status: action.action === 'approve' ? 'approved' : 
              action.action === 'revoke' ? 'revoked' : 'pending',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
      apiAccessLevel: 'basic'
    }
  }

  async deleteUser(id: string): Promise<void> {
    // TODO: Replace with real API call when backend is ready
    // return this.delete<void>(API_ENDPOINTS.USERS.BY_ID(id))
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  async getUserStats(): Promise<UserStats> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<UserStats>(API_ENDPOINTS.USERS.STATS)
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 300))
    return {
      total: 150,
      pending: 12,
      approved: 130,
      revoked: 8,
      admins: 5,
      users: 145,
      recentSignups: 8,
      activeUsers: 95
    }
  }

  async getUserActivities(userId?: string, limit = 50): Promise<UserActivity[]> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<UserActivity[]>(API_ENDPOINTS.USERS.ACTIVITIES, {
    //   params: { userId, limit }
    // })
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 400))
    return [
      {
        userId: userId || '1',
        action: 'login',
        timestamp: new Date(),
        metadata: { ipAddress: '***********' }
      },
      {
        userId: userId || '1',
        action: 'api_key_created',
        timestamp: new Date(Date.now() - 3600000),
        metadata: { keyName: 'Production Key' }
      }
    ]
  }

  // API Key Management API
  async getApiKeys(filters?: ApiKeyFilters, userId?: string): Promise<ApiKey[]> {
    // TODO: Replace with real API call when backend is ready
    // const endpoint = userId ? API_ENDPOINTS.API_KEYS.BY_USER(userId) : API_ENDPOINTS.API_KEYS.BASE
    // return this.get<ApiKey[]>(endpoint, { params: filters })
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 400))
    
    const mockKeys: ApiKey[] = [
      {
        id: '1',
        userId: userId || '1',
        name: 'Production API Key',
        description: 'Main production key for API access',
        keyHash: 'hash_abc123',
        status: 'active',
        permissions: [
          { resource: 'ollama', actions: ['read', 'write'] },
          { resource: 'analytics', actions: ['read'] }
        ],
        rateLimit: {
          requestsPerMinute: 100,
          requestsPerHour: 1000,
          requestsPerDay: 10000
        },
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        lastUsedAt: new Date('2024-01-15'),
        expiresAt: new Date('2025-01-01')
      },
      {
        id: '2',
        userId: userId || '1',
        name: 'Development Key',
        description: 'Key for development and testing',
        keyHash: 'hash_def456',
        status: 'active',
        permissions: [
          { resource: 'ollama', actions: ['read'] }
        ],
        rateLimit: {
          requestsPerMinute: 50,
          requestsPerHour: 500,
          requestsPerDay: 2000
        },
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-10'),
        lastUsedAt: new Date('2024-01-14')
      },
      {
        id: '3',
        userId: userId || '2',
        name: 'Revoked Key',
        description: 'Previously active key that was revoked',
        keyHash: 'hash_ghi789',
        status: 'revoked',
        permissions: [
          { resource: 'ollama', actions: ['read'] }
        ],
        rateLimit: {
          requestsPerMinute: 30,
          requestsPerHour: 300,
          requestsPerDay: 1000
        },
        createdAt: new Date('2023-12-01'),
        updatedAt: new Date('2024-01-05')
      }
    ]

    // Apply filters
    let filteredKeys = mockKeys
    if (userId) {
      filteredKeys = filteredKeys.filter(key => key.userId === userId)
    }
    if (filters?.status && filters.status !== 'all') {
      filteredKeys = filteredKeys.filter(key => key.status === filters.status)
    }
    if (filters?.search) {
      const search = filters.search.toLowerCase()
      filteredKeys = filteredKeys.filter(key => 
        key.name.toLowerCase().includes(search) ||
        (key.description && key.description.toLowerCase().includes(search))
      )
    }

    return filteredKeys
  }

  async getApiKeyById(id: string): Promise<ApiKey> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<ApiKey>(API_ENDPOINTS.API_KEYS.BY_ID(id))
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 300))
    return {
      id,
      userId: '1',
      name: `API Key ${id}`,
      description: `Description for key ${id}`,
      keyHash: `hash_${id}`,
      status: 'active',
      permissions: [
        { resource: 'ollama', actions: ['read', 'write'] }
      ],
      rateLimit: {
        requestsPerMinute: 100,
        requestsPerHour: 1000,
        requestsPerDay: 10000
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      lastUsedAt: new Date()
    }
  }

  async createApiKey(keyData: CreateApiKeyRequest, userId: string): Promise<ApiKey> {
    // TODO: Replace with real API call when backend is ready
    // return this.post<ApiKey>(API_ENDPOINTS.API_KEYS.BY_USER(userId), keyData)
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const newKey: ApiKey = {
      id: Date.now().toString(),
      userId,
      name: keyData.name,
      description: keyData.description,
      key: `sk-${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`, // Only shown once
      keyHash: `hash_${Date.now()}`,
      status: 'active',
      permissions: keyData.permissions,
      rateLimit: {
        requestsPerMinute: keyData.rateLimit?.requestsPerMinute || 60,
        requestsPerHour: keyData.rateLimit?.requestsPerHour || 1000,
        requestsPerDay: keyData.rateLimit?.requestsPerDay || 10000
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      expiresAt: keyData.expiresAt
    }

    return newKey
  }

  async updateApiKey(keyData: UpdateApiKeyRequest): Promise<ApiKey> {
    // TODO: Replace with real API call when backend is ready
    // return this.put<ApiKey>(API_ENDPOINTS.API_KEYS.BY_ID(keyData.id), keyData)
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 600))
    return {
      id: keyData.id,
      userId: '1',
      name: keyData.name || `Updated Key ${keyData.id}`,
      description: keyData.description,
      keyHash: `hash_${keyData.id}`,
      status: 'active',
      permissions: keyData.permissions || [
        { resource: 'ollama', actions: ['read'] }
      ],
      rateLimit: {
        requestsPerMinute: keyData.rateLimit?.requestsPerMinute || 60,
        requestsPerHour: keyData.rateLimit?.requestsPerHour || 1000,
        requestsPerDay: keyData.rateLimit?.requestsPerDay || 10000
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
      expiresAt: keyData.expiresAt
    }
  }

  async regenerateApiKey(keyId: string): Promise<ApiKey> {
    // TODO: Replace with real API call when backend is ready
    // return this.post<ApiKey>(API_ENDPOINTS.API_KEYS.REGENERATE(keyId))
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 700))
    return {
      id: keyId,
      userId: '1',
      name: `Regenerated Key ${keyId}`,
      description: 'Regenerated API key',
      key: `sk-${Math.random().toString(36).substring(2, 15)}${Math.random().toString(36).substring(2, 15)}`, // Only shown once
      keyHash: `hash_new_${Date.now()}`,
      status: 'active',
      permissions: [
        { resource: 'ollama', actions: ['read', 'write'] }
      ],
      rateLimit: {
        requestsPerMinute: 100,
        requestsPerHour: 1000,
        requestsPerDay: 10000
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date(),
      lastUsedAt: new Date()
    }
  }

  async revokeApiKey(keyId: string): Promise<ApiKey> {
    // TODO: Replace with real API call when backend is ready
    // return this.post<ApiKey>(API_ENDPOINTS.API_KEYS.REVOKE(keyId))
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 400))
    return {
      id: keyId,
      userId: '1',
      name: `Revoked Key ${keyId}`,
      description: 'This key has been revoked',
      keyHash: `hash_${keyId}`,
      status: 'revoked',
      permissions: [],
      rateLimit: {
        requestsPerMinute: 0,
        requestsPerHour: 0,
        requestsPerDay: 0
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date()
    }
  }

  async deleteApiKey(keyId: string): Promise<void> {
    // TODO: Replace with real API call when backend is ready
    // return this.delete<void>(API_ENDPOINTS.API_KEYS.BY_ID(keyId))
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  async getApiKeyStats(userId?: string): Promise<ApiKeyStats> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<ApiKeyStats>(API_ENDPOINTS.API_KEYS.STATS, {
    //   params: { userId }
    // })
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 300))
    return {
      total: userId ? 5 : 25,
      active: userId ? 3 : 18,
      revoked: userId ? 2 : 7,
      expiringSoon: userId ? 1 : 3,
      neverUsed: userId ? 0 : 2,
      totalUsage: userId ? 15420 : 89650,
      averageUsage: userId ? 3084 : 3586
    }
  }

  async getApiKeyUsage(keyId: string, timeRange: TimeRange): Promise<ApiKeyUsage> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<ApiKeyUsage>(API_ENDPOINTS.API_KEYS.USAGE(keyId), {
    //   params: { startDate: timeRange.start.toISOString(), endDate: timeRange.end.toISOString() }
    // })
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 400))
    return {
      keyId,
      period: timeRange,
      totalRequests: 1250,
      successfulRequests: 1180,
      failedRequests: 70,
      averageResponseTime: 245,
      dataTransferred: 5242880, // bytes
      topEndpoints: [
        { endpoint: '/api/ollama/generate', count: 800, averageResponseTime: 320 },
        { endpoint: '/api/ollama/chat', count: 350, averageResponseTime: 180 },
        { endpoint: '/api/ollama/models', count: 100, averageResponseTime: 45 }
      ]
    }
  }

  // Analytics API
  async getUsageLogs(filters: AnalyticsFilters, limit = 100): Promise<ApiUsageLog[]> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<ApiUsageLog[]>(API_ENDPOINTS.ANALYTICS.USAGE_LOGS, {
    //   params: { ...filters, limit }
    // })
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 600))
    
    const mockLogs: ApiUsageLog[] = []
    const endpoints = ['/api/ollama/generate', '/api/ollama/chat', '/api/ollama/models', '/api/analytics/usage']
    const methods: Array<'GET' | 'POST' | 'PUT' | 'DELETE'> = ['GET', 'POST', 'PUT', 'DELETE']
    const statusCodes = [200, 201, 400, 401, 403, 404, 500]
    
    // Generate mock logs
    for (let i = 0; i < limit; i++) {
      const timestamp = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000) // Last 7 days
      const endpoint = endpoints[Math.floor(Math.random() * endpoints.length)]
      const method = methods[Math.floor(Math.random() * methods.length)]
      const statusCode = statusCodes[Math.floor(Math.random() * statusCodes.length)]
      const isSuccess = statusCode < 400
      
      mockLogs.push({
        id: `log-${i + 1}`,
        userId: filters.userId || `user-${Math.floor(Math.random() * 5) + 1}`,
        apiKeyId: filters.apiKeyId || `key-${Math.floor(Math.random() * 3) + 1}`,
        endpoint,
        method,
        statusCode,
        responseTime: Math.floor(Math.random() * 2000) + 50, // 50-2050ms
        requestSize: Math.floor(Math.random() * 10000) + 100, // 100-10100 bytes
        responseSize: Math.floor(Math.random() * 50000) + 500, // 500-50500 bytes
        timestamp,
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        userAgent: 'API Client/1.0',
        errorMessage: isSuccess ? undefined : `Error ${statusCode}: ${endpoint} failed`
      })
    }

    // Apply filters
    let filteredLogs = mockLogs
    
    if (filters.userId) {
      filteredLogs = filteredLogs.filter(log => log.userId === filters.userId)
    }
    
    if (filters.apiKeyId) {
      filteredLogs = filteredLogs.filter(log => log.apiKeyId === filters.apiKeyId)
    }
    
    if (filters.endpoint) {
      filteredLogs = filteredLogs.filter(log => log.endpoint.includes(filters.endpoint!))
    }
    
    if (filters.method) {
      filteredLogs = filteredLogs.filter(log => log.method === filters.method)
    }
    
    if (filters.statusCode) {
      filteredLogs = filteredLogs.filter(log => log.statusCode === filters.statusCode)
    }
    
    if (filters.timeRange) {
      filteredLogs = filteredLogs.filter(log => 
        log.timestamp >= filters.timeRange!.start && 
        log.timestamp <= filters.timeRange!.end
      )
    }

    return filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  async getUsageMetrics(query: AnalyticsQuery): Promise<UsageMetrics> {
    // TODO: Replace with real API call when backend is ready
    // return this.post<UsageMetrics>(API_ENDPOINTS.ANALYTICS.USAGE_METRICS, query)
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 800))
    
    const totalRequests = Math.floor(Math.random() * 10000) + 1000
    const successfulRequests = Math.floor(totalRequests * (0.85 + Math.random() * 0.1)) // 85-95% success rate
    const failedRequests = totalRequests - successfulRequests
    
    return {
      userId: query.filters?.userId,
      apiKeyId: query.filters?.apiKeyId,
      period: query.timeRange,
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime: Math.floor(Math.random() * 500) + 100, // 100-600ms
      totalDataTransferred: Math.floor(Math.random() * 1000000000) + 10000000, // 10MB-1GB
      requestsPerHour: Array.from({ length: 24 }, () => Math.floor(Math.random() * 100)),
      topEndpoints: [
        {
          endpoint: '/api/ollama/generate',
          count: Math.floor(totalRequests * 0.4),
          averageResponseTime: 320,
          errorRate: 0.05
        },
        {
          endpoint: '/api/ollama/chat',
          count: Math.floor(totalRequests * 0.3),
          averageResponseTime: 180,
          errorRate: 0.03
        },
        {
          endpoint: '/api/ollama/models',
          count: Math.floor(totalRequests * 0.2),
          averageResponseTime: 45,
          errorRate: 0.01
        },
        {
          endpoint: '/api/analytics/usage',
          count: Math.floor(totalRequests * 0.1),
          averageResponseTime: 120,
          errorRate: 0.02
        }
      ],
      errorBreakdown: [
        { statusCode: 400, count: Math.floor(failedRequests * 0.3), percentage: 30 },
        { statusCode: 401, count: Math.floor(failedRequests * 0.2), percentage: 20 },
        { statusCode: 403, count: Math.floor(failedRequests * 0.15), percentage: 15 },
        { statusCode: 404, count: Math.floor(failedRequests * 0.2), percentage: 20 },
        { statusCode: 500, count: Math.floor(failedRequests * 0.15), percentage: 15 }
      ],
      responseTimeDistribution: [
        { range: '0-100ms', count: Math.floor(totalRequests * 0.2), percentage: 20 },
        { range: '100-300ms', count: Math.floor(totalRequests * 0.4), percentage: 40 },
        { range: '300-500ms', count: Math.floor(totalRequests * 0.25), percentage: 25 },
        { range: '500-1000ms', count: Math.floor(totalRequests * 0.1), percentage: 10 },
        { range: '1000ms+', count: Math.floor(totalRequests * 0.05), percentage: 5 }
      ]
    }
  }

  async getOllamaMetrics(timeRange: TimeRange): Promise<OllamaPerformanceMetrics[]> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<OllamaPerformanceMetrics[]>(API_ENDPOINTS.ANALYTICS.OLLAMA_METRICS, {
    //   params: { startDate: timeRange.start.toISOString(), endDate: timeRange.end.toISOString() }
    // })
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const models = ['llama2', 'codellama', 'mistral', 'neural-chat']
    
    return models.map(modelName => ({
      modelName,
      totalRequests: Math.floor(Math.random() * 5000) + 500,
      averageResponseTime: Math.floor(Math.random() * 1000) + 200,
      tokensPerSecond: Math.floor(Math.random() * 100) + 20,
      successRate: 0.9 + Math.random() * 0.09, // 90-99%
      errorRate: Math.random() * 0.1, // 0-10%
      peakUsageTime: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
      resourceUtilization: {
        cpu: Math.floor(Math.random() * 60) + 20, // 20-80%
        memory: Math.floor(Math.random() * 40) + 30, // 30-70%
        gpu: Math.floor(Math.random() * 80) + 10 // 10-90%
      },
      concurrentRequests: Math.floor(Math.random() * 20) + 1,
      queueLength: Math.floor(Math.random() * 10)
    }))
  }

  async getSystemMetrics(timeRange: TimeRange): Promise<SystemMetrics[]> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<SystemMetrics[]>(API_ENDPOINTS.ANALYTICS.SYSTEM_METRICS, {
    //   params: { startDate: timeRange.start.toISOString(), endDate: timeRange.end.toISOString() }
    // })
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 400))
    
    const hours = Math.ceil((timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60))
    const metrics: SystemMetrics[] = []
    
    for (let i = 0; i < Math.min(hours, 168); i++) { // Max 1 week of hourly data
      const timestamp = new Date(timeRange.start.getTime() + i * 60 * 60 * 1000)
      
      metrics.push({
        timestamp,
        activeUsers: Math.floor(Math.random() * 50) + 10,
        totalApiCalls: Math.floor(Math.random() * 1000) + 100,
        averageResponseTime: Math.floor(Math.random() * 300) + 100,
        errorRate: Math.random() * 0.1,
        systemLoad: {
          cpu: Math.floor(Math.random() * 60) + 20,
          memory: Math.floor(Math.random() * 40) + 40,
          disk: Math.floor(Math.random() * 30) + 10
        },
        ollamaMetrics: await this.getOllamaMetrics(timeRange)
      })
    }
    
    return metrics
  }

  async getTrendData(metric: string, timeRange: TimeRange, granularity: 'hour' | 'day' | 'week' = 'day'): Promise<{ timestamp: Date; value: number }[]> {
    // TODO: Replace with real API call when backend is ready
    // return this.get<{ timestamp: Date; value: number }[]>(API_ENDPOINTS.ANALYTICS.TREND_DATA, {
    //   params: { 
    //     metric, 
    //     granularity,
    //     startDate: timeRange.start.toISOString(), 
    //     endDate: timeRange.end.toISOString() 
    //   }
    // })
    
    // Mock implementation for development
    await new Promise(resolve => setTimeout(resolve, 300))
    
    const intervals = granularity === 'hour' ? 24 : granularity === 'day' ? 30 : 12
    const data = []
    
    for (let i = 0; i < intervals; i++) {
      const multiplier = granularity === 'hour' ? 60 * 60 * 1000 : 
                       granularity === 'day' ? 24 * 60 * 60 * 1000 : 
                       7 * 24 * 60 * 60 * 1000
      
      const timestamp = new Date(timeRange.start.getTime() + i * multiplier)
      let value = 0
      
      switch (metric) {
        case 'requests':
          value = Math.floor(Math.random() * 1000) + 100
          break
        case 'responseTime':
          value = Math.floor(Math.random() * 300) + 100
          break
        case 'errors':
          value = Math.floor(Math.random() * 50) + 5
          break
        case 'users':
          value = Math.floor(Math.random() * 100) + 20
          break
        default:
          value = Math.floor(Math.random() * 100)
      }
      
      data.push({ timestamp, value })
    }
    
    return data
  }
}

// Export singleton instance
export const apiClient = new ApiClient()
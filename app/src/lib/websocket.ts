// WebSocket client for real-time data updates
import { API_ENDPOINTS, TIMEOUT_CONFIG } from './api-endpoints'
import { logError } from './api-errors'

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: string
  id?: string
}

export interface UsageUpdateMessage extends WebSocketMessage {
  type: 'usage_update'
  data: {
    userId: string
    apiKeyId: string
    endpoint: string
    method: string
    statusCode: number
    responseTime: number
    timestamp: string
  }
}

export interface SystemStatusMessage extends WebSocketMessage {
  type: 'system_status'
  data: {
    activeUsers: number
    totalApiCalls: number
    averageResponseTime: number
    errorRate: number
    systemLoad: {
      cpu: number
      memory: number
      disk: number
    }
  }
}

export interface NotificationMessage extends WebSocketMessage {
  type: 'notification'
  data: {
    title: string
    message: string
    level: 'info' | 'warning' | 'error' | 'success'
    userId?: string
    actions?: Array<{
      label: string
      action: string
    }>
  }
}

export type WebSocketMessageType = 
  | UsageUpdateMessage 
  | SystemStatusMessage 
  | NotificationMessage

export interface WebSocketConfig {
  url: string
  protocols?: string[]
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  authToken?: string
}

export interface WebSocketEventHandlers {
  onMessage?: (message: WebSocketMessageType) => void
  onUsageUpdate?: (data: UsageUpdateMessage['data']) => void
  onSystemStatus?: (data: SystemStatusMessage['data']) => void
  onNotification?: (data: NotificationMessage['data']) => void
  onConnect?: () => void
  onDisconnect?: () => void
  onError?: (error: Event) => void
  onReconnect?: (attempt: number) => void
}

export class WebSocketClient {
  private ws: WebSocket | null = null
  private config: Required<WebSocketConfig>
  private handlers: WebSocketEventHandlers = {}
  private reconnectAttempts = 0
  private reconnectTimer: NodeJS.Timeout | null = null
  private heartbeatTimer: NodeJS.Timeout | null = null
  private isConnecting = false
  private isManuallyDisconnected = false

  constructor(config: WebSocketConfig) {
    this.config = {
      protocols: [],
      reconnectInterval: 5000, // 5 seconds
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000, // 30 seconds
      authToken: undefined,
      ...config
    }
  }

  // Set event handlers
  on(handlers: WebSocketEventHandlers): void {
    this.handlers = { ...this.handlers, ...handlers }
  }

  // Connect to WebSocket server
  async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return
    }

    this.isConnecting = true
    this.isManuallyDisconnected = false

    try {
      const wsUrl = this.buildWebSocketUrl()
      this.ws = new WebSocket(wsUrl, this.config.protocols)

      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
      this.ws.onerror = this.handleError.bind(this)

      // Set connection timeout
      setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
          this.ws.close()
          this.handleConnectionTimeout()
        }
      }, TIMEOUT_CONFIG.WEBSOCKET)

    } catch (error) {
      this.isConnecting = false
      logError(error as Error, { context: 'websocket_connect' })
      throw error
    }
  }

  // Disconnect from WebSocket server
  disconnect(): void {
    this.isManuallyDisconnected = true
    this.clearTimers()
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect')
      this.ws = null
    }
  }

  // Send message to server
  send(message: any): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('WebSocket is not connected')
    }

    const messageWithId = {
      ...message,
      id: this.generateMessageId(),
      timestamp: new Date().toISOString()
    }

    this.ws.send(JSON.stringify(messageWithId))
  }

  // Get connection status
  getStatus(): 'connecting' | 'open' | 'closing' | 'closed' {
    if (!this.ws) return 'closed'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting'
      case WebSocket.OPEN: return 'open'
      case WebSocket.CLOSING: return 'closing'
      case WebSocket.CLOSED: return 'closed'
      default: return 'closed'
    }
  }

  // Check if connected
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN
  }

  // Set authentication token
  setAuthToken(token: string | null): void {
    this.config.authToken = token || undefined
  }

  private buildWebSocketUrl(): string {
    const baseUrl = this.config.url.replace(/^http/, 'ws')
    const url = new URL(baseUrl)
    
    // Add auth token as query parameter if available
    if (this.config.authToken) {
      url.searchParams.set('token', this.config.authToken)
    }
    
    return url.toString()
  }

  private handleOpen(): void {
    this.isConnecting = false
    this.reconnectAttempts = 0
    
    // Start heartbeat
    this.startHeartbeat()
    
    // Notify handlers
    this.handlers.onConnect?.()
    
    if (import.meta.env.DEV) {
      console.log('WebSocket connected')
    }
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessageType = JSON.parse(event.data)
      
      // Handle different message types
      switch (message.type) {
        case 'usage_update':
          this.handlers.onUsageUpdate?.(message.data)
          break
        case 'system_status':
          this.handlers.onSystemStatus?.(message.data)
          break
        case 'notification':
          this.handlers.onNotification?.(message.data)
          break
        case 'pong':
          // Heartbeat response - no action needed
          break
        default:
          if (import.meta.env.DEV) {
            console.warn('Unknown WebSocket message type:', message.type)
          }
      }
      
      // Call general message handler
      this.handlers.onMessage?.(message)
      
    } catch (error) {
      logError(error as Error, { 
        context: 'websocket_message_parse',
        rawMessage: event.data 
      })
    }
  }

  private handleClose(event: CloseEvent): void {
    this.isConnecting = false
    this.clearTimers()
    
    // Notify handlers
    this.handlers.onDisconnect?.()
    
    if (import.meta.env.DEV) {
      console.log('WebSocket disconnected:', event.code, event.reason)
    }
    
    // Attempt reconnection if not manually disconnected
    if (!this.isManuallyDisconnected && this.shouldReconnect(event.code)) {
      this.scheduleReconnect()
    }
  }

  private handleError(event: Event): void {
    this.isConnecting = false
    
    logError(new Error('WebSocket error'), { 
      context: 'websocket_error',
      event 
    })
    
    // Notify handlers
    this.handlers.onError?.(event)
  }

  private handleConnectionTimeout(): void {
    this.isConnecting = false
    
    logError(new Error('WebSocket connection timeout'), { 
      context: 'websocket_timeout' 
    })
    
    if (!this.isManuallyDisconnected) {
      this.scheduleReconnect()
    }
  }

  private shouldReconnect(closeCode: number): boolean {
    // Don't reconnect on certain close codes
    const noReconnectCodes = [1000, 1001, 1005, 4000, 4001, 4002, 4003]
    return !noReconnectCodes.includes(closeCode)
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      if (import.meta.env.DEV) {
        console.log('Max reconnection attempts reached')
      }
      return
    }

    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts),
      30000 // Max 30 seconds
    )

    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++
      
      if (import.meta.env.DEV) {
        console.log(`Attempting WebSocket reconnection (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`)
      }
      
      this.handlers.onReconnect?.(this.reconnectAttempts)
      this.connect().catch(error => {
        logError(error, { context: 'websocket_reconnect' })
      })
    }, delay)
  }

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.send({ type: 'ping' })
      }
    }, this.config.heartbeatInterval)
  }

  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  }
}

// WebSocket manager for handling multiple connections
export class WebSocketManager {
  private connections = new Map<string, WebSocketClient>()
  private baseUrl: string

  constructor(baseUrl: string = API_ENDPOINTS.WEBSOCKET.USAGE_UPDATES) {
    this.baseUrl = baseUrl.replace(/^http/, 'ws')
  }

  // Create or get WebSocket connection
  getConnection(endpoint: string, config?: Partial<WebSocketConfig>): WebSocketClient {
    if (!this.connections.has(endpoint)) {
      const wsConfig: WebSocketConfig = {
        url: `${this.baseUrl}${endpoint}`,
        ...config
      }
      
      const client = new WebSocketClient(wsConfig)
      this.connections.set(endpoint, client)
    }
    
    return this.connections.get(endpoint)!
  }

  // Connect to usage updates WebSocket
  connectUsageUpdates(handlers: WebSocketEventHandlers): WebSocketClient {
    const client = this.getConnection('/usage-updates')
    client.on(handlers)
    client.connect()
    return client
  }

  // Connect to system status WebSocket
  connectSystemStatus(handlers: WebSocketEventHandlers): WebSocketClient {
    const client = this.getConnection('/system-status')
    client.on(handlers)
    client.connect()
    return client
  }

  // Connect to notifications WebSocket
  connectNotifications(handlers: WebSocketEventHandlers): WebSocketClient {
    const client = this.getConnection('/notifications')
    client.on(handlers)
    client.connect()
    return client
  }

  // Set auth token for all connections
  setAuthToken(token: string | null): void {
    this.connections.forEach(client => {
      client.setAuthToken(token)
    })
  }

  // Disconnect all connections
  disconnectAll(): void {
    this.connections.forEach(client => {
      client.disconnect()
    })
    this.connections.clear()
  }

  // Get connection status for all connections
  getConnectionStatuses(): Record<string, string> {
    const statuses: Record<string, string> = {}
    this.connections.forEach((client, endpoint) => {
      statuses[endpoint] = client.getStatus()
    })
    return statuses
  }
}

// Export singleton instance
export const wsManager = new WebSocketManager()
import { loadChartExportUtils } from './dynamic-imports'

export interface ExportOptions {
  filename?: string
  quality?: number
  backgroundColor?: string
  scale?: number
}

export class ChartExporter {
  /**
   * Export chart as PNG image
   */
  static async exportAsPNG(
    element: HTMLElement,
    options: ExportOptions = {}
  ): Promise<void> {
    const {
      filename = 'chart',
      quality = 1,
      backgroundColor = '#ffffff',
      scale = 2,
    } = options

    try {
      const { html2canvas } = await loadChartExportUtils()
      const canvas = await html2canvas(element, {
        backgroundColor,
        scale,
        useCORS: true,
        allowTaint: true,
        logging: false,
      })

      // Create download link
      const link = document.createElement('a')
      link.download = `${filename}.png`
      link.href = canvas.toDataURL('image/png', quality)
      
      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } catch (error) {
      console.error('Error exporting chart as PNG:', error)
      throw new Error('Failed to export chart as PNG')
    }
  }

  /**
   * Export chart as SVG
   */
  static async exportAsSVG(
    element: HTMLElement,
    options: ExportOptions = {}
  ): Promise<void> {
    const { filename = 'chart' } = options

    try {
      // Find SVG element within the chart container
      const svgElement = element.querySelector('svg')
      if (!svgElement) {
        throw new Error('No SVG element found in chart')
      }

      // Clone the SVG to avoid modifying the original
      const clonedSvg = svgElement.cloneNode(true) as SVGElement
      
      // Add XML namespace if not present
      if (!clonedSvg.getAttribute('xmlns')) {
        clonedSvg.setAttribute('xmlns', 'http://www.w3.org/2000/svg')
      }

      // Serialize SVG to string
      const serializer = new XMLSerializer()
      const svgString = serializer.serializeToString(clonedSvg)
      
      // Create blob and download
      const blob = new Blob([svgString], { type: 'image/svg+xml' })
      const url = URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.download = `${filename}.svg`
      link.href = url
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error exporting chart as SVG:', error)
      throw new Error('Failed to export chart as SVG')
    }
  }

  /**
   * Export chart as PDF
   */
  static async exportAsPDF(
    element: HTMLElement,
    options: ExportOptions = {}
  ): Promise<void> {
    const {
      filename = 'chart',
      quality = 1,
      backgroundColor = '#ffffff',
      scale = 2,
    } = options

    try {
      const { html2canvas, jsPDF } = await loadChartExportUtils()
      const canvas = await html2canvas(element, {
        backgroundColor,
        scale,
        useCORS: true,
        allowTaint: true,
        logging: false,
      })

      const imgData = canvas.toDataURL('image/png', quality)
      const imgWidth = canvas.width
      const imgHeight = canvas.height

      // Calculate PDF dimensions (A4 size with margins)
      const pdfWidth = 210 // A4 width in mm
      const pdfHeight = 297 // A4 height in mm
      const margin = 20

      // Calculate scaling to fit image in PDF
      const maxWidth = pdfWidth - (margin * 2)
      const maxHeight = pdfHeight - (margin * 2)
      
      const widthRatio = maxWidth / (imgWidth * 0.264583) // Convert px to mm
      const heightRatio = maxHeight / (imgHeight * 0.264583)
      const ratio = Math.min(widthRatio, heightRatio)

      const scaledWidth = (imgWidth * 0.264583) * ratio
      const scaledHeight = (imgHeight * 0.264583) * ratio

      // Center the image
      const x = (pdfWidth - scaledWidth) / 2
      const y = (pdfHeight - scaledHeight) / 2

      // Create PDF
      const pdf = new jsPDF('p', 'mm', 'a4')
      pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight)
      
      // Save PDF
      pdf.save(`${filename}.pdf`)
    } catch (error) {
      console.error('Error exporting chart as PDF:', error)
      throw new Error('Failed to export chart as PDF')
    }
  }

  /**
   * Export chart in specified format
   */
  static async exportChart(
    element: HTMLElement,
    format: 'png' | 'svg' | 'pdf',
    options: ExportOptions = {}
  ): Promise<void> {
    switch (format) {
      case 'png':
        return this.exportAsPNG(element, options)
      case 'svg':
        return this.exportAsSVG(element, options)
      case 'pdf':
        return this.exportAsPDF(element, options)
      default:
        throw new Error(`Unsupported export format: ${format}`)
    }
  }
}
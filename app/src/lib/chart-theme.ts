// Chart theme configuration matching the design system
export const chartTheme = {
  colors: {
    primary: 'hsl(var(--primary))',
    secondary: 'hsl(var(--secondary))',
    accent: 'hsl(var(--accent))',
    muted: 'hsl(var(--muted))',
    success: 'hsl(142, 76%, 36%)',
    warning: 'hsl(38, 92%, 50%)',
    error: 'hsl(0, 84%, 60%)',
    info: 'hsl(217, 91%, 60%)',
  },
  palette: [
    'hsl(var(--primary))',
    'hsl(var(--secondary))',
    'hsl(var(--accent))',
    'hsl(142, 76%, 36%)', // success
    'hsl(38, 92%, 50%)',  // warning
    'hsl(0, 84%, 60%)',   // error
    'hsl(217, 91%, 60%)', // info
    'hsl(280, 100%, 70%)', // purple
    'hsl(200, 100%, 70%)', // cyan
    'hsl(160, 100%, 40%)', // teal
  ],
  grid: {
    stroke: 'hsl(var(--border))',
    strokeDasharray: '3 3',
    strokeOpacity: 0.3,
  },
  axis: {
    stroke: 'hsl(var(--border))',
    fontSize: 12,
    fontFamily: 'var(--font-sans)',
    fill: 'hsl(var(--muted-foreground))',
  },
  tooltip: {
    backgroundColor: 'hsl(var(--popover))',
    border: '1px solid hsl(var(--border))',
    borderRadius: 'var(--radius)',
    boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    color: 'hsl(var(--popover-foreground))',
    fontSize: 12,
    fontFamily: 'var(--font-sans)',
  },
  legend: {
    fontSize: 12,
    fontFamily: 'var(--font-sans)',
    fill: 'hsl(var(--foreground))',
  },
}

export const getChartColor = (index: number): string => {
  return chartTheme.palette[index % chartTheme.palette.length] || chartTheme.colors.primary
}

export const getStatusColor = (status: 'success' | 'warning' | 'error' | 'info'): string => {
  return chartTheme.colors[status]
}
// API error types and utilities
export interface ApiErrorResponse {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: string
  path?: string
  statusCode: number
}

export interface ValidationErrorDetail {
  field: string
  message: string
  value?: any
}

export interface ValidationErrorResponse extends ApiErrorResponse {
  code: 'VALIDATION_ERROR'
  details: {
    errors: ValidationErrorDetail[]
  }
}

export class ApiError extends Error {
  public readonly statusCode: number
  public readonly code: string
  public readonly details?: Record<string, any>
  public readonly timestamp: string
  public readonly path?: string

  constructor(response: ApiErrorResponse) {
    super(response.message)
    this.name = 'ApiError'
    this.statusCode = response.statusCode
    this.code = response.code
    this.details = response.details || undefined
    this.timestamp = response.timestamp
    this.path = response.path || undefined
  }

  public isValidationError(): this is ApiError & { details: { errors: ValidationErrorDetail[] } } {
    return this.code === 'VALIDATION_ERROR'
  }

  public isAuthenticationError(): boolean {
    return this.statusCode === 401 || this.code === 'AUTHENTICATION_ERROR'
  }

  public isAuthorizationError(): boolean {
    return this.statusCode === 403 || this.code === 'AUTHORIZATION_ERROR'
  }

  public isNotFoundError(): boolean {
    return this.statusCode === 404 || this.code === 'NOT_FOUND'
  }

  public isRateLimitError(): boolean {
    return this.statusCode === 429 || this.code === 'RATE_LIMIT_EXCEEDED'
  }

  public isServerError(): boolean {
    return this.statusCode >= 500
  }

  public isRetryable(): boolean {
    return this.isServerError() || this.isRateLimitError() || this.statusCode === 408
  }
}

export class NetworkError extends Error {
  constructor(message: string, public readonly originalError?: Error) {
    super(message)
    this.name = 'NetworkError'
  }
}

export class TimeoutError extends Error {
  constructor(timeout: number) {
    super(`Request timed out after ${timeout}ms`)
    this.name = 'TimeoutError'
  }
}

export class RetryExhaustedError extends Error {
  constructor(
    public readonly maxRetries: number,
    public readonly lastError: Error
  ) {
    super(`Request failed after ${maxRetries} retries. Last error: ${lastError.message}`)
    this.name = 'RetryExhaustedError'
  }
}

// Error transformation utilities
export function transformAxiosError(error: any): ApiError | NetworkError | TimeoutError {
  if (error.code === 'ECONNABORTED') {
    return new TimeoutError(error.config?.timeout || 0)
  }

  if (!error.response) {
    return new NetworkError(
      error.message || 'Network error occurred',
      error
    )
  }

  const response = error.response
  const errorData: ApiErrorResponse = {
    code: response.data?.code || 'UNKNOWN_ERROR',
    message: response.data?.message || response.statusText || 'An error occurred',
    details: response.data?.details,
    timestamp: response.data?.timestamp || new Date().toISOString(),
    path: response.config?.url,
    statusCode: response.status
  }

  return new ApiError(errorData)
}

// Error logging utility
export function logError(error: Error, context?: Record<string, any>): void {
  const errorInfo = {
    name: error.name,
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    context
  }

  if (error instanceof ApiError) {
    Object.assign(errorInfo, {
      statusCode: error.statusCode,
      code: error.code,
      details: error.details,
      path: error.path
    })
  }

  // In development, log to console
  if (import.meta.env.DEV) {
    console.error('API Error:', errorInfo)
  }

  // In production, you might want to send to an error tracking service
  // Example: Sentry, LogRocket, etc.
  if (import.meta.env.PROD) {
    // sendToErrorTrackingService(errorInfo)
  }
}
// Dynamic imports for heavy dependencies to reduce initial bundle size

// Chart export utilities (html2canvas, jsPDF)
export const loadChartExportUtils = async () => {
  const [html2canvas, jsPDF] = await Promise.all([
    import('html2canvas'),
    import('jspdf')
  ])
  
  return {
    html2canvas: html2canvas.default,
    jsPDF: jsPDF.jsPDF
  }
}

// Recharts library
export const loadRechartsComponents = async () => {
  const recharts = await import('recharts')
  return recharts
}

// Date utilities (only load when needed for complex date operations)
export const loadDateUtils = async () => {
  const dateFns = await import('date-fns')
  return dateFns
}

// React Query DevTools (only in development)
export const loadReactQueryDevtools = async () => {
  if (process.env['NODE_ENV'] === 'development') {
    const { ReactQueryDevtools } = await import('@tanstack/react-query-devtools')
    return ReactQueryDevtools
  }
  return null
}

// Performance monitoring utilities
export const loadPerformanceUtils = async () => {
  // Only load in production for performance monitoring
  if (process.env['NODE_ENV'] === 'production') {
    // These would be actual performance monitoring libraries
    // For now, we'll return a mock implementation
    return {
      getCLS: () => {},
      getFID: () => {},
      getFCP: () => {},
      getLCP: () => {},
      getTTFB: () => {},
    }
  }
  return null
}

// Utility to preload critical chunks
export const preloadCriticalChunks = () => {
  // Preload chunks that are likely to be needed soon
  const preloadPromises = [
    // Preload auth components (likely needed after login)
    import('../pages/user/UserDashboard'),
    import('../components/ui/loading-spinner'),
  ]

  // Don't await these - just start the loading process
  Promise.all(preloadPromises).catch(() => {
    // Silently fail - preloading is an optimization, not critical
  })
}

// Utility to check if a module is already loaded
export const isModuleLoaded = (moduleName: string): boolean => {
  // This is a simplified check - in a real app you might want more sophisticated tracking
  return Boolean((window as any).__loadedModules?.[moduleName])
}

// Mark a module as loaded
export const markModuleAsLoaded = (moduleName: string): void => {
  if (!(window as any).__loadedModules) {
    (window as any).__loadedModules = {}
  }
  (window as any).__loadedModules[moduleName] = true
}
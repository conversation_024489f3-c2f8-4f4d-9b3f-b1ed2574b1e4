import React from 'react'
import { useAtomValue } from 'jotai'
import { userAtom, isAuthenticatedAtom } from '../stores/auth'
import { Navigate, useLocation } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Shield, AlertTriangle, ArrowLeft } from 'lucide-react'
import { Link } from 'react-router-dom'

interface AdminGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function AdminGuard({ children, fallback }: AdminGuardProps) {
  const user = useAtomValue(userAtom)
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  const location = useLocation()

  // Not authenticated - redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // Not an admin user - show access denied
  if (user?.role !== 'admin') {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-red-100 rounded-full w-fit">
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
            <CardTitle className="text-xl">Access Denied</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              You don't have permission to access the admin panel. 
              Administrator privileges are required.
            </p>
            
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-center justify-between">
                <span>Your Role:</span>
                <span className="font-medium">{user?.role || 'Unknown'}</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Required Role:</span>
                <span className="font-medium flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  Admin
                </span>
              </div>
            </div>

            <div className="pt-4 space-y-2">
              <Link to="/dashboard">
                <Button className="w-full">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Go to User Dashboard
                </Button>
              </Link>
              
              <p className="text-xs text-muted-foreground">
                Contact your administrator if you believe this is an error.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // User is authenticated and is an admin
  return <>{children}</>
}

// Higher-order component version
export function withAdminGuard<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) {
  return function AdminGuardedComponent(props: P) {
    return (
      <AdminGuard fallback={fallback}>
        <Component {...props} />
      </AdminGuard>
    )
  }
}

// Hook for checking admin access
export function useAdminAccess() {
  const user = useAtomValue(userAtom)
  const isAuthenticated = useAtomValue(isAuthenticatedAtom)
  
  return {
    isAuthenticated,
    isAdmin: user?.role === 'admin',
    hasAdminAccess: isAuthenticated && user?.role === 'admin',
    user
  }
}
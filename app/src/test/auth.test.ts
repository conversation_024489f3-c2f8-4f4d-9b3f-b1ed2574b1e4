import { describe, it, expect, beforeEach } from 'vitest'
import { createStore } from 'jotai'
import { 
  session<PERSON>tom, 
  isAuthenticatedAtom, 
  user<PERSON>tom, 
  userRole<PERSON>tom,
  isAdmin<PERSON>tom,
  userStatusAtom,
  canAccessApi<PERSON>tom
} from '../stores/auth'
import type { Session, User } from '../types/auth'

describe('Auth Atoms', () => {
  let store: ReturnType<typeof createStore>

  beforeEach(() => {
    store = createStore()
  })

  it('should initialize with null session', () => {
    const session = store.get(sessionAtom)
    expect(session).toBeNull()
  })

  it('should not be authenticated initially', () => {
    const isAuthenticated = store.get(isAuthenticatedAtom)
    expect(isAuthenticated).toBe(false)
  })

  it('should return null user initially', () => {
    const user = store.get(userAtom)
    expect(user).toBeNull()
  })

  it('should return default user role', () => {
    const role = store.get(user<PERSON><PERSON><PERSON><PERSON>)
    expect(role).toBe('user')
  })

  it('should not be admin initially', () => {
    const isAdmin = store.get(isAdminAtom)
    expect(isAdmin).toBe(false)
  })

  it('should have pending status initially', () => {
    const status = store.get(userStatusAtom)
    expect(status).toBe('pending')
  })

  it('should not have API access initially', () => {
    const canAccess = store.get(canAccessApiAtom)
    expect(canAccess).toBe(false)
  })

  it('should be authenticated with valid session', () => {
    const mockUser: User = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      status: 'approved',
      createdAt: new Date(),
      updatedAt: new Date(),
      apiAccessLevel: 'basic'
    }

    const mockSession: Session = {
      id: 'session-1',
      userId: '1',
      user: mockUser,
      token: 'mock-token',
      expiresAt: new Date(Date.now() + 3600000), // 1 hour from now
      createdAt: new Date()
    }

    store.set(sessionAtom, mockSession)

    expect(store.get(isAuthenticatedAtom)).toBe(true)
    expect(store.get(userAtom)).toEqual(mockUser)
    expect(store.get(userRoleAtom)).toBe('user')
    expect(store.get(isAdminAtom)).toBe(false)
    expect(store.get(userStatusAtom)).toBe('approved')
    expect(store.get(canAccessApiAtom)).toBe(true)
  })

  it('should handle admin user correctly', () => {
    const mockAdminUser: User = {
      id: '2',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      status: 'approved',
      createdAt: new Date(),
      updatedAt: new Date(),
      apiAccessLevel: 'premium'
    }

    const mockSession: Session = {
      id: 'session-2',
      userId: '2',
      user: mockAdminUser,
      token: 'mock-admin-token',
      expiresAt: new Date(Date.now() + 3600000),
      createdAt: new Date()
    }

    store.set(sessionAtom, mockSession)

    expect(store.get(userRoleAtom)).toBe('admin')
    expect(store.get(isAdminAtom)).toBe(true)
  })

  it('should not be authenticated with expired session', () => {
    const mockUser: User = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'user',
      status: 'approved',
      createdAt: new Date(),
      updatedAt: new Date(),
      apiAccessLevel: 'basic'
    }

    const expiredSession: Session = {
      id: 'session-1',
      userId: '1',
      user: mockUser,
      token: 'mock-token',
      expiresAt: new Date(Date.now() - 3600000), // 1 hour ago
      createdAt: new Date()
    }

    store.set(sessionAtom, expiredSession)

    expect(store.get(isAuthenticatedAtom)).toBe(false)
  })
})
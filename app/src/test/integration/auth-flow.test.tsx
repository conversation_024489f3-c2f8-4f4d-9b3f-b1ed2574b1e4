import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'jotai'
import { createStore } from 'jotai'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { LoginForm } from '../../components/auth/LoginForm'
import { UserDashboard } from '../../pages/user/UserDashboard'
import { AdminDashboard } from '../../pages/admin/AdminDashboard'
import { sessionAtom, userAtom } from '../../stores/auth'
import type { User, Session } from '../../types/auth'

// Mock the auth client
const mockAuthClient = {
  signIn: {
    email: vi.fn()
  },
  signUp: {
    email: vi.fn()
  },
  signOut: vi.fn(),
  getSession: vi.fn()
}

vi.mock('../../lib/auth', () => ({
  authClient: mockAuthClient
}))

// Mock API client
const mockApiClient = {
  getApiKeys: vi.fn(),
  getUsageMetrics: vi.fn(),
  getUsers: vi.fn(),
  getUserStats: vi.fn()
}

vi.mock('../../lib/api', () => ({
  apiClient: mockApiClient
}))

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const store = createStore()
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <Provider store={store}>
          {children}
        </Provider>
      </QueryClientProvider>
    </BrowserRouter>
  )
}

describe('Authentication Flow Integration', () => {
  let store: any

  const mockUser: User = {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user',
    status: 'approved',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    apiAccessLevel: 'basic'
  }

  const mockAdminUser: User = {
    ...mockUser,
    id: 'admin-1',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin'
  }

  const mockSession: Session = {
    id: 'session-1',
    userId: 'user-1',
    user: mockUser,
    token: 'mock-token',
    expiresAt: new Date(Date.now() + 60 * 60 * 1000),
    createdAt: new Date()
  }

  const mockAdminSession: Session = {
    ...mockSession,
    id: 'session-admin',
    userId: 'admin-1',
    user: mockAdminUser
  }

  beforeEach(() => {
    store = createStore()
    vi.clearAllMocks()
    
    // Setup default API responses
    mockApiClient.getApiKeys.mockResolvedValue([])
    mockApiClient.getUsageMetrics.mockResolvedValue({
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0
    })
    mockApiClient.getUsers.mockResolvedValue([])
    mockApiClient.getUserStats.mockResolvedValue({
      total: 0,
      pending: 0,
      approved: 0,
      revoked: 0
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('User Login Flow', () => {
    it('should complete full user login and dashboard access flow', async () => {
      const user = userEvent.setup()
      
      // Mock successful login
      mockAuthClient.signIn.email.mockResolvedValue({
        data: {
          user: mockUser,
          token: 'mock-token',
          expiresAt: mockSession.expiresAt.toISOString()
        }
      })

      // Start with login form
      const { rerender } = render(
        <TestWrapper>
          <LoginForm onSuccess={() => {}} />
        </TestWrapper>
      )

      // Fill in login form
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      // Wait for login to complete
      await waitFor(() => {
        expect(mockAuthClient.signIn.email).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123'
        })
      })

      // Set session in store to simulate successful login
      store.set(sessionAtom, mockSession)
      store.set(userAtom, mockUser)

      // Render user dashboard
      rerender(
        <TestWrapper>
          <UserDashboard />
        </TestWrapper>
      )

      // Verify user dashboard is rendered
      await waitFor(() => {
        expect(screen.getByText(/welcome.*test user/i)).toBeInTheDocument()
      })

      // Verify user-specific content is shown
      expect(screen.getByText(/api keys/i)).toBeInTheDocument()
      expect(screen.getByText(/usage metrics/i)).toBeInTheDocument()
      expect(screen.queryByText(/user management/i)).not.toBeInTheDocument() // Admin only
    })

    it('should handle login failure gracefully', async () => {
      const user = userEvent.setup()
      
      // Mock login failure
      mockAuthClient.signIn.email.mockResolvedValue({
        error: { message: 'Invalid credentials' }
      })

      render(
        <TestWrapper>
          <LoginForm onSuccess={() => {}} />
        </TestWrapper>
      )

      // Fill in login form with invalid credentials
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'wrongpassword')
      await user.click(submitButton)

      // Wait for error to appear
      await waitFor(() => {
        expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument()
      })

      // Verify user stays on login form
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
    })

    it('should handle pending user status', async () => {
      const user = userEvent.setup()
      const pendingUser = { ...mockUser, status: 'pending' as const }
      const pendingSession = { ...mockSession, user: pendingUser }
      
      // Mock successful login but with pending user
      mockAuthClient.signIn.email.mockResolvedValue({
        data: {
          user: pendingUser,
          token: 'mock-token',
          expiresAt: pendingSession.expiresAt.toISOString()
        }
      })

      const { rerender } = render(
        <TestWrapper>
          <LoginForm onSuccess={() => {}} />
        </TestWrapper>
      )

      // Complete login
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      // Set pending session
      store.set(sessionAtom, pendingSession)
      store.set(userAtom, pendingUser)

      // Render dashboard
      rerender(
        <TestWrapper>
          <UserDashboard />
        </TestWrapper>
      )

      // Verify pending status message is shown
      await waitFor(() => {
        expect(screen.getByText(/account pending approval/i)).toBeInTheDocument()
      })

      // Verify limited functionality
      expect(screen.queryByText(/create api key/i)).not.toBeInTheDocument()
    })
  })

  describe('Admin Login Flow', () => {
    it('should complete full admin login and dashboard access flow', async () => {
      const user = userEvent.setup()
      
      // Mock successful admin login
      mockAuthClient.signIn.email.mockResolvedValue({
        data: {
          user: mockAdminUser,
          token: 'mock-token',
          expiresAt: mockAdminSession.expiresAt.toISOString()
        }
      })

      const { rerender } = render(
        <TestWrapper>
          <LoginForm onSuccess={() => {}} />
        </TestWrapper>
      )

      // Complete login
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      // Set admin session
      store.set(sessionAtom, mockAdminSession)
      store.set(userAtom, mockAdminUser)

      // Render admin dashboard
      rerender(
        <TestWrapper>
          <AdminDashboard />
        </TestWrapper>
      )

      // Verify admin dashboard is rendered
      await waitFor(() => {
        expect(screen.getByText(/admin dashboard/i)).toBeInTheDocument()
      })

      // Verify admin-specific content is shown
      expect(screen.getByText(/user management/i)).toBeInTheDocument()
      expect(screen.getByText(/system metrics/i)).toBeInTheDocument()
      expect(screen.getByText(/api usage overview/i)).toBeInTheDocument()
    })

    it('should show admin navigation options', async () => {
      store.set(sessionAtom, mockAdminSession)
      store.set(userAtom, mockAdminUser)

      render(
        <TestWrapper>
          <AdminDashboard />
        </TestWrapper>
      )

      // Verify admin navigation items
      await waitFor(() => {
        expect(screen.getByRole('link', { name: /users/i })).toBeInTheDocument()
        expect(screen.getByRole('link', { name: /analytics/i })).toBeInTheDocument()
        expect(screen.getByRole('link', { name: /settings/i })).toBeInTheDocument()
      })
    })
  })

  describe('Session Management', () => {
    it('should handle session expiry', async () => {
      const expiredSession = {
        ...mockSession,
        expiresAt: new Date(Date.now() - 60 * 60 * 1000) // 1 hour ago
      }

      store.set(sessionAtom, expiredSession)

      render(
        <TestWrapper>
          <UserDashboard />
        </TestWrapper>
      )

      // Should redirect to login or show session expired message
      await waitFor(() => {
        expect(
          screen.getByText(/session expired/i) || 
          screen.getByRole('button', { name: /sign in/i })
        ).toBeInTheDocument()
      })
    })

    it('should handle logout flow', async () => {
      const user = userEvent.setup()
      
      store.set(sessionAtom, mockSession)
      store.set(userAtom, mockUser)

      mockAuthClient.signOut.mockResolvedValue({})

      render(
        <TestWrapper>
          <UserDashboard />
        </TestWrapper>
      )

      // Find and click logout button
      const logoutButton = screen.getByRole('button', { name: /logout/i })
      await user.click(logoutButton)

      // Wait for logout to complete
      await waitFor(() => {
        expect(mockAuthClient.signOut).toHaveBeenCalled()
      })

      // Session should be cleared
      expect(store.get(sessionAtom)).toBeNull()
    })
  })

  describe('Role-based Access Control', () => {
    it('should prevent regular users from accessing admin features', async () => {
      store.set(sessionAtom, mockSession)
      store.set(userAtom, mockUser)

      render(
        <TestWrapper>
          <AdminDashboard />
        </TestWrapper>
      )

      // Should show access denied or redirect
      await waitFor(() => {
        expect(
          screen.getByText(/access denied/i) ||
          screen.getByText(/unauthorized/i)
        ).toBeInTheDocument()
      })
    })

    it('should allow admins to access all features', async () => {
      store.set(sessionAtom, mockAdminSession)
      store.set(userAtom, mockAdminUser)

      render(
        <TestWrapper>
          <AdminDashboard />
        </TestWrapper>
      )

      // Should show admin dashboard without restrictions
      await waitFor(() => {
        expect(screen.getByText(/admin dashboard/i)).toBeInTheDocument()
      })

      expect(screen.getByText(/user management/i)).toBeInTheDocument()
      expect(screen.getByText(/system metrics/i)).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors during login', async () => {
      const user = userEvent.setup()
      
      // Mock network error
      mockAuthClient.signIn.email.mockRejectedValue(new Error('Network error'))

      render(
        <TestWrapper>
          <LoginForm onSuccess={() => {}} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      // Should show network error message
      await waitFor(() => {
        expect(screen.getByText(/network error/i)).toBeInTheDocument()
      })
    })

    it('should handle API errors in dashboard', async () => {
      store.set(sessionAtom, mockSession)
      store.set(userAtom, mockUser)

      // Mock API error
      mockApiClient.getApiKeys.mockRejectedValue(new Error('API Error'))

      render(
        <TestWrapper>
          <UserDashboard />
        </TestWrapper>
      )

      // Should show error state in dashboard
      await waitFor(() => {
        expect(screen.getByText(/failed to load/i)).toBeInTheDocument()
      })
    })
  })

  describe('Loading States', () => {
    it('should show loading states during authentication', async () => {
      const user = userEvent.setup()
      
      // Mock delayed login response
      let resolveLogin: (value: any) => void
      mockAuthClient.signIn.email.mockImplementation(() => 
        new Promise(resolve => { resolveLogin = resolve })
      )

      render(
        <TestWrapper>
          <LoginForm onSuccess={() => {}} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      // Should show loading state
      expect(screen.getByText(/signing in/i)).toBeInTheDocument()
      expect(submitButton).toBeDisabled()

      // Resolve login
      resolveLogin!({
        data: {
          user: mockUser,
          token: 'mock-token',
          expiresAt: mockSession.expiresAt.toISOString()
        }
      })

      await waitFor(() => {
        expect(screen.queryByText(/signing in/i)).not.toBeInTheDocument()
      })
    })
  })
})
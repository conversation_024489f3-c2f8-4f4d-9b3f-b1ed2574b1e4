import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'jotai'
import { createStore } from 'jotai'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ApiKeysPage } from '../../pages/user/ApiKeysPage'
import { CreateApiKeyDialog } from '../../components/user/CreateApiKeyDialog'
import { ApiKeyDetailsModal } from '../../components/user/ApiKeyDetailsModal'
import { sessionAtom, userAtom } from '../../stores/auth'
import { apiKeysAtom } from '../../stores/apiKeys'
import type { User, Session } from '../../types/auth'
import type { <PERSON><PERSON><PERSON><PERSON>, CreateApiKeyRequest } from '../../types/apiKey'

// Mock API client
const mockApiClient = {
  getApiKeys: vi.fn(),
  createApiKey: vi.fn(),
  updateApiKey: vi.fn(),
  regenerateApiKey: vi.fn(),
  revokeApiKey: vi.fn(),
  deleteApiKey: vi.fn(),
  getApiKeyStats: vi.fn(),
  getApiKeyUsage: vi.fn()
}

vi.mock('../../lib/api', () => ({
  apiClient: mockApiClient
}))

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const store = createStore()
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <Provider store={store}>
          {children}
        </Provider>
      </QueryClientProvider>
    </BrowserRouter>
  )
}

describe('API Key Management Integration', () => {
  let store: any

  const mockUser: User = {
    id: 'user-1',
    email: '<EMAIL>',
    name: 'Test User',
    role: 'user',
    status: 'approved',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    apiAccessLevel: 'basic'
  }

  const mockSession: Session = {
    id: 'session-1',
    userId: 'user-1',
    user: mockUser,
    token: 'mock-token',
    expiresAt: new Date(Date.now() + 60 * 60 * 1000),
    createdAt: new Date()
  }

  const mockApiKeys: ApiKey[] = [
    {
      id: 'key-1',
      userId: 'user-1',
      name: 'Production Key',
      description: 'Key for production use',
      keyHash: 'hash-1',
      status: 'active',
      permissions: [{ resource: 'ollama', actions: ['read', 'write'] }],
      rateLimit: {
        requestsPerMinute: 60,
        requestsPerHour: 1000,
        requestsPerDay: 10000
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      lastUsedAt: new Date('2024-01-02')
    },
    {
      id: 'key-2',
      userId: 'user-1',
      name: 'Development Key',
      description: 'Key for development',
      keyHash: 'hash-2',
      status: 'active',
      permissions: [{ resource: 'ollama', actions: ['read'] }],
      rateLimit: {
        requestsPerMinute: 30,
        requestsPerHour: 500,
        requestsPerDay: 5000
      },
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02')
    }
  ]

  beforeEach(() => {
    store = createStore()
    vi.clearAllMocks()
    
    // Set up authenticated user
    store.set(sessionAtom, mockSession)
    store.set(userAtom, mockUser)
    
    // Setup default API responses
    mockApiClient.getApiKeys.mockResolvedValue(mockApiKeys)
    mockApiClient.getApiKeyStats.mockResolvedValue({
      total: 2,
      active: 2,
      revoked: 0,
      expiringSoon: 0,
      neverUsed: 1,
      totalUsage: 1500,
      averageUsage: 750
    })
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('API Key List and Management', () => {
    it('should load and display API keys', async () => {
      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Wait for API keys to load
      await waitFor(() => {
        expect(mockApiClient.getApiKeys).toHaveBeenCalledWith(undefined, 'user-1')
      })

      // Verify API keys are displayed
      expect(screen.getByText('Production Key')).toBeInTheDocument()
      expect(screen.getByText('Development Key')).toBeInTheDocument()
      expect(screen.getByText('Key for production use')).toBeInTheDocument()
      expect(screen.getByText('Key for development')).toBeInTheDocument()
    })

    it('should show API key statistics', async () => {
      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('2')).toBeInTheDocument() // Total keys
        expect(screen.getByText('Active Keys')).toBeInTheDocument()
      })
    })

    it('should handle empty API key list', async () => {
      mockApiClient.getApiKeys.mockResolvedValue([])
      mockApiClient.getApiKeyStats.mockResolvedValue({
        total: 0,
        active: 0,
        revoked: 0,
        expiringSoon: 0,
        neverUsed: 0,
        totalUsage: 0,
        averageUsage: 0
      })

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText(/no api keys found/i)).toBeInTheDocument()
        expect(screen.getByText(/create your first api key/i)).toBeInTheDocument()
      })
    })
  })

  describe('API Key Creation Flow', () => {
    it('should complete full API key creation workflow', async () => {
      const user = userEvent.setup()
      
      const newApiKey: ApiKey = {
        id: 'key-3',
        userId: 'user-1',
        name: 'New Test Key',
        description: 'Test key description',
        key: 'sk-test-123456789', // Only shown once
        keyHash: 'hash-3',
        status: 'active',
        permissions: [{ resource: 'ollama', actions: ['read'] }],
        rateLimit: {
          requestsPerMinute: 60,
          requestsPerHour: 1000,
          requestsPerDay: 10000
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockApiClient.createApiKey.mockResolvedValue(newApiKey)

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Wait for page to load
      await waitFor(() => {
        expect(screen.getByText('Production Key')).toBeInTheDocument()
      })

      // Click create API key button
      const createButton = screen.getByRole('button', { name: /create api key/i })
      await user.click(createButton)

      // Fill in the creation form
      await waitFor(() => {
        expect(screen.getByText(/create new api key/i)).toBeInTheDocument()
      })

      const nameInput = screen.getByLabelText(/name/i)
      const descriptionInput = screen.getByLabelText(/description/i)
      
      await user.type(nameInput, 'New Test Key')
      await user.type(descriptionInput, 'Test key description')

      // Select permissions
      const readPermission = screen.getByRole('checkbox', { name: /read/i })
      await user.click(readPermission)

      // Submit form
      const submitButton = screen.getByRole('button', { name: /create key/i })
      await user.click(submitButton)

      // Wait for API call
      await waitFor(() => {
        expect(mockApiClient.createApiKey).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'New Test Key',
            description: 'Test key description',
            permissions: expect.arrayContaining([
              expect.objectContaining({ resource: 'ollama', actions: ['read'] })
            ])
          }),
          'user-1'
        )
      })

      // Verify success message and key display
      expect(screen.getByText(/api key created successfully/i)).toBeInTheDocument()
      expect(screen.getByText('sk-test-123456789')).toBeInTheDocument()
      expect(screen.getByText(/copy this key now/i)).toBeInTheDocument()
    })

    it('should handle API key creation validation errors', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Click create API key button
      const createButton = screen.getByRole('button', { name: /create api key/i })
      await user.click(createButton)

      // Try to submit without filling required fields
      const submitButton = screen.getByRole('button', { name: /create key/i })
      await user.click(submitButton)

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument()
      })
    })

    it('should handle API key creation failure', async () => {
      const user = userEvent.setup()
      
      mockApiClient.createApiKey.mockRejectedValue(new Error('Key creation failed'))

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Complete the creation flow
      const createButton = screen.getByRole('button', { name: /create api key/i })
      await user.click(createButton)

      const nameInput = screen.getByLabelText(/name/i)
      await user.type(nameInput, 'Test Key')

      const readPermission = screen.getByRole('checkbox', { name: /read/i })
      await user.click(readPermission)

      const submitButton = screen.getByRole('button', { name: /create key/i })
      await user.click(submitButton)

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/key creation failed/i)).toBeInTheDocument()
      })
    })
  })

  describe('API Key Actions', () => {
    beforeEach(() => {
      store.set(apiKeysAtom, mockApiKeys)
    })

    it('should regenerate API key', async () => {
      const user = userEvent.setup()
      
      const regeneratedKey = { ...mockApiKeys[0], updatedAt: new Date() }
      mockApiClient.regenerateApiKey.mockResolvedValue(regeneratedKey)

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Find and click regenerate button for first key
      const regenerateButtons = screen.getAllByRole('button', { name: /regenerate/i })
      await user.click(regenerateButtons[0])

      // Confirm regeneration
      const confirmButton = screen.getByRole('button', { name: /confirm/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockApiClient.regenerateApiKey).toHaveBeenCalledWith('key-1')
      })

      expect(screen.getByText(/api key regenerated successfully/i)).toBeInTheDocument()
    })

    it('should revoke API key', async () => {
      const user = userEvent.setup()
      
      const revokedKey = { ...mockApiKeys[0], status: 'revoked' as const }
      mockApiClient.revokeApiKey.mockResolvedValue(revokedKey)

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Find and click revoke button
      const revokeButtons = screen.getAllByRole('button', { name: /revoke/i })
      await user.click(revokeButtons[0])

      // Confirm revocation
      const confirmButton = screen.getByRole('button', { name: /confirm/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockApiClient.revokeApiKey).toHaveBeenCalledWith('key-1')
      })

      expect(screen.getByText(/api key revoked successfully/i)).toBeInTheDocument()
    })

    it('should delete API key', async () => {
      const user = userEvent.setup()
      
      mockApiClient.deleteApiKey.mockResolvedValue(undefined)

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Find and click delete button
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i })
      await user.click(deleteButtons[0])

      // Confirm deletion
      const confirmButton = screen.getByRole('button', { name: /delete/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockApiClient.deleteApiKey).toHaveBeenCalledWith('key-1')
      })

      expect(screen.getByText(/api key deleted successfully/i)).toBeInTheDocument()
    })

    it('should view API key details', async () => {
      const user = userEvent.setup()
      
      const mockUsage = {
        keyId: 'key-1',
        period: { start: new Date(), end: new Date() },
        totalRequests: 1000,
        successfulRequests: 950,
        failedRequests: 50,
        averageResponseTime: 250,
        dataTransferred: 1024000,
        topEndpoints: []
      }
      
      mockApiClient.getApiKeyUsage.mockResolvedValue(mockUsage)

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Click on API key card to view details
      const keyCard = screen.getByText('Production Key').closest('button')
      await user.click(keyCard!)

      // Should open details modal
      await waitFor(() => {
        expect(screen.getByText(/api key details/i)).toBeInTheDocument()
        expect(screen.getByText('Production Key')).toBeInTheDocument()
      })

      // Should load usage data
      expect(mockApiClient.getApiKeyUsage).toHaveBeenCalledWith(
        'key-1',
        expect.any(Object)
      )

      // Should show usage statistics
      expect(screen.getByText('1,000')).toBeInTheDocument() // Total requests
      expect(screen.getByText('950')).toBeInTheDocument() // Successful requests
    })
  })

  describe('API Key Filtering and Search', () => {
    beforeEach(() => {
      store.set(apiKeysAtom, [
        ...mockApiKeys,
        {
          ...mockApiKeys[0],
          id: 'key-3',
          name: 'Revoked Key',
          status: 'revoked' as const
        }
      ])
    })

    it('should filter API keys by status', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Open filter dropdown
      const filterButton = screen.getByRole('button', { name: /filter/i })
      await user.click(filterButton)

      // Select "Active" filter
      const activeFilter = screen.getByRole('option', { name: /active/i })
      await user.click(activeFilter)

      // Should only show active keys
      expect(screen.getByText('Production Key')).toBeInTheDocument()
      expect(screen.getByText('Development Key')).toBeInTheDocument()
      expect(screen.queryByText('Revoked Key')).not.toBeInTheDocument()
    })

    it('should search API keys by name', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Type in search box
      const searchInput = screen.getByPlaceholderText(/search api keys/i)
      await user.type(searchInput, 'Production')

      // Should only show matching keys
      await waitFor(() => {
        expect(screen.getByText('Production Key')).toBeInTheDocument()
        expect(screen.queryByText('Development Key')).not.toBeInTheDocument()
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle API key loading failure', async () => {
      mockApiClient.getApiKeys.mockRejectedValue(new Error('Failed to load API keys'))

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText(/failed to load api keys/i)).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
      })
    })

    it('should retry loading API keys', async () => {
      const user = userEvent.setup()
      
      mockApiClient.getApiKeys
        .mockRejectedValueOnce(new Error('Failed to load'))
        .mockResolvedValueOnce(mockApiKeys)

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Wait for error state
      await waitFor(() => {
        expect(screen.getByText(/failed to load api keys/i)).toBeInTheDocument()
      })

      // Click retry
      const retryButton = screen.getByRole('button', { name: /retry/i })
      await user.click(retryButton)

      // Should load successfully
      await waitFor(() => {
        expect(screen.getByText('Production Key')).toBeInTheDocument()
      })
    })
  })

  describe('Permissions and Access Control', () => {
    it('should prevent API key creation for pending users', async () => {
      const pendingUser = { ...mockUser, status: 'pending' as const }
      const pendingSession = { ...mockSession, user: pendingUser }
      
      store.set(sessionAtom, pendingSession)
      store.set(userAtom, pendingUser)

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Create button should be disabled or not shown
      const createButton = screen.queryByRole('button', { name: /create api key/i })
      expect(createButton).toBeDisabled()
      
      // Should show pending approval message
      expect(screen.getByText(/account pending approval/i)).toBeInTheDocument()
    })

    it('should prevent API key creation for revoked users', async () => {
      const revokedUser = { ...mockUser, status: 'revoked' as const }
      const revokedSession = { ...mockSession, user: revokedUser }
      
      store.set(sessionAtom, revokedSession)
      store.set(userAtom, revokedUser)

      render(
        <TestWrapper>
          <ApiKeysPage />
        </TestWrapper>
      )

      // Should show access revoked message
      expect(screen.getByText(/access has been revoked/i)).toBeInTheDocument()
      
      // Create button should not be available
      expect(screen.queryByRole('button', { name: /create api key/i })).not.toBeInTheDocument()
    })
  })
})
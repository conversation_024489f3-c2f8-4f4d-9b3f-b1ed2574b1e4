import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'jotai'
import { createStore } from 'jotai'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { UserManagementPage } from '../../pages/admin/UserManagementPage'
import { UserDetailModal } from '../../components/admin/UserDetailModal'
import { sessionAtom, userAtom } from '../../stores/auth'
import { usersAtom } from '../../stores/users'
import type { User, Session } from '../../types/auth'
import type { UserActionRequest } from '../../types/user'

// Mock API client
const mockApiClient = {
  getUsers: vi.fn(),
  createUser: vi.fn(),
  updateUser: vi.fn(),
  performUserAction: vi.fn(),
  deleteUser: vi.fn(),
  getUserStats: vi.fn(),
  getUserActivities: vi.fn(),
  getUserById: vi.fn()
}

vi.mock('../../lib/api', () => ({
  apiClient: mockApiClient
}))

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const store = createStore()
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <Provider store={store}>
          {children}
        </Provider>
      </QueryClientProvider>
    </BrowserRouter>
  )
}

describe('Admin User Management Integration', () => {
  let store: any

  const mockAdminUser: User = {
    id: 'admin-1',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin',
    status: 'approved',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    apiAccessLevel: 'premium'
  }

  const mockAdminSession: Session = {
    id: 'session-admin',
    userId: 'admin-1',
    user: mockAdminUser,
    token: 'mock-token',
    expiresAt: new Date(Date.now() + 60 * 60 * 1000),
    createdAt: new Date()
  }

  const mockUsers: User[] = [
    {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'John Doe',
      role: 'user',
      status: 'approved',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      apiAccessLevel: 'basic',
      lastLoginAt: new Date('2024-01-02')
    },
    {
      id: 'user-2',
      email: '<EMAIL>',
      name: 'Jane Smith',
      role: 'user',
      status: 'pending',
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      apiAccessLevel: 'basic'
    },
    {
      id: 'user-3',
      email: '<EMAIL>',
      name: 'Bob Johnson',
      role: 'user',
      status: 'revoked',
      createdAt: new Date('2024-01-03'),
      updatedAt: new Date('2024-01-03'),
      apiAccessLevel: 'none',
      lastLoginAt: new Date('2024-01-03')
    }
  ]

  const mockUserStats = {
    total: 3,
    pending: 1,
    approved: 1,
    revoked: 1,
    admins: 1,
    users: 3,
    recentSignups: 2,
    activeUsers: 1
  }

  const mockUserActivities = [
    {
      userId: 'user-1',
      action: 'login' as const,
      timestamp: new Date('2024-01-02T10:00:00Z'),
      metadata: { ip: '***********' }
    },
    {
      userId: 'user-1',
      action: 'api_key_created' as const,
      timestamp: new Date('2024-01-02T11:00:00Z'),
      metadata: { keyName: 'Test Key' }
    }
  ]

  beforeEach(() => {
    store = createStore()
    vi.clearAllMocks()
    
    // Set up authenticated admin user
    store.set(sessionAtom, mockAdminSession)
    store.set(userAtom, mockAdminUser)
    
    // Setup default API responses
    mockApiClient.getUsers.mockResolvedValue({
      data: mockUsers,
      page: 1,
      limit: 10,
      total: 3
    })
    mockApiClient.getUserStats.mockResolvedValue(mockUserStats)
    mockApiClient.getUserActivities.mockResolvedValue(mockUserActivities)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('User List and Statistics', () => {
    it('should load and display users with statistics', async () => {
      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      // Wait for users to load
      await waitFor(() => {
        expect(mockApiClient.getUsers).toHaveBeenCalled()
        expect(mockApiClient.getUserStats).toHaveBeenCalled()
      })

      // Verify users are displayed
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.getByText('Bob Johnson')).toBeInTheDocument()

      // Verify statistics are displayed
      expect(screen.getByText('3')).toBeInTheDocument() // Total users
      expect(screen.getByText('1')).toBeInTheDocument() // Pending users
    })

    it('should show user status badges correctly', async () => {
      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Check status badges
      expect(screen.getByText('Approved')).toBeInTheDocument()
      expect(screen.getByText('Pending')).toBeInTheDocument()
      expect(screen.getByText('Revoked')).toBeInTheDocument()
    })

    it('should handle empty user list', async () => {
      mockApiClient.getUsers.mockResolvedValue({
        data: [],
        page: 1,
        limit: 10,
        total: 0
      })
      mockApiClient.getUserStats.mockResolvedValue({
        total: 0,
        pending: 0,
        approved: 0,
        revoked: 0,
        admins: 0,
        users: 0,
        recentSignups: 0,
        activeUsers: 0
      })

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText(/no users found/i)).toBeInTheDocument()
      })
    })
  })

  describe('User Actions', () => {
    beforeEach(() => {
      store.set(usersAtom, mockUsers)
    })

    it('should approve pending user', async () => {
      const user = userEvent.setup()
      
      const approvedUser = { ...mockUsers[1], status: 'approved' as const }
      mockApiClient.performUserAction.mockResolvedValue(approvedUser)

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      // Find and click approve button for pending user
      const approveButtons = screen.getAllByRole('button', { name: /approve/i })
      const pendingUserApproveButton = approveButtons.find(button => 
        button.closest('tr')?.textContent?.includes('Jane Smith')
      )
      
      await user.click(pendingUserApproveButton!)

      // Confirm approval
      const confirmButton = screen.getByRole('button', { name: /confirm/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockApiClient.performUserAction).toHaveBeenCalledWith({
          userId: 'user-2',
          action: 'approve'
        })
      })

      expect(screen.getByText(/user approved successfully/i)).toBeInTheDocument()
    })

    it('should revoke user access', async () => {
      const user = userEvent.setup()
      
      const revokedUser = { ...mockUsers[0], status: 'revoked' as const }
      mockApiClient.performUserAction.mockResolvedValue(revokedUser)

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Find and click revoke button for approved user
      const revokeButtons = screen.getAllByRole('button', { name: /revoke/i })
      const approvedUserRevokeButton = revokeButtons.find(button => 
        button.closest('tr')?.textContent?.includes('John Doe')
      )
      
      await user.click(approvedUserRevokeButton!)

      // Confirm revocation
      const confirmButton = screen.getByRole('button', { name: /confirm/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockApiClient.performUserAction).toHaveBeenCalledWith({
          userId: 'user-1',
          action: 'revoke'
        })
      })

      expect(screen.getByText(/user access revoked successfully/i)).toBeInTheDocument()
    })

    it('should delete user', async () => {
      const user = userEvent.setup()
      
      mockApiClient.deleteUser.mockResolvedValue(undefined)

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Bob Johnson')).toBeInTheDocument()
      })

      // Find and click delete button
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i })
      const userDeleteButton = deleteButtons.find(button => 
        button.closest('tr')?.textContent?.includes('Bob Johnson')
      )
      
      await user.click(userDeleteButton!)

      // Confirm deletion
      const confirmButton = screen.getByRole('button', { name: /delete/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockApiClient.deleteUser).toHaveBeenCalledWith('user-3')
      })

      expect(screen.getByText(/user deleted successfully/i)).toBeInTheDocument()
    })

    it('should promote user to admin', async () => {
      const user = userEvent.setup()
      
      const promotedUser = { ...mockUsers[0], role: 'admin' as const }
      mockApiClient.performUserAction.mockResolvedValue(promotedUser)

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Open user actions menu
      const moreButtons = screen.getAllByRole('button', { name: /more actions/i })
      const userMoreButton = moreButtons.find(button => 
        button.closest('tr')?.textContent?.includes('John Doe')
      )
      
      await user.click(userMoreButton!)

      // Click promote option
      const promoteButton = screen.getByRole('menuitem', { name: /promote to admin/i })
      await user.click(promoteButton)

      // Confirm promotion
      const confirmButton = screen.getByRole('button', { name: /confirm/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockApiClient.performUserAction).toHaveBeenCalledWith({
          userId: 'user-1',
          action: 'promote'
        })
      })

      expect(screen.getByText(/user promoted to admin successfully/i)).toBeInTheDocument()
    })
  })

  describe('Bulk Actions', () => {
    beforeEach(() => {
      store.set(usersAtom, mockUsers)
    })

    it('should perform bulk approval', async () => {
      const user = userEvent.setup()
      
      mockApiClient.performUserAction.mockResolvedValue({ success: true })

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      // Select users for bulk action
      const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i })
      await user.click(selectAllCheckbox)

      // Bulk actions should appear
      expect(screen.getByText(/3 users selected/i)).toBeInTheDocument()

      // Click bulk approve
      const bulkApproveButton = screen.getByRole('button', { name: /bulk approve/i })
      await user.click(bulkApproveButton)

      // Confirm bulk action
      const confirmButton = screen.getByRole('button', { name: /approve selected/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(mockApiClient.performUserAction).toHaveBeenCalledTimes(3)
      })

      expect(screen.getByText(/bulk action completed successfully/i)).toBeInTheDocument()
    })

    it('should handle partial bulk action failures', async () => {
      const user = userEvent.setup()
      
      // Mock partial failure
      mockApiClient.performUserAction
        .mockResolvedValueOnce({ success: true })
        .mockRejectedValueOnce(new Error('Action failed'))
        .mockResolvedValueOnce({ success: true })

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      // Select all users
      const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i })
      await user.click(selectAllCheckbox)

      // Perform bulk revoke
      const bulkRevokeButton = screen.getByRole('button', { name: /bulk revoke/i })
      await user.click(bulkRevokeButton)

      const confirmButton = screen.getByRole('button', { name: /revoke selected/i })
      await user.click(confirmButton)

      await waitFor(() => {
        expect(screen.getByText(/2 of 3 actions completed/i)).toBeInTheDocument()
        expect(screen.getByText(/1 action failed/i)).toBeInTheDocument()
      })
    })
  })

  describe('User Details and Activities', () => {
    it('should view user details with activities', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Click view details button
      const viewButtons = screen.getAllByRole('button', { name: /view details/i })
      const userViewButton = viewButtons.find(button => 
        button.closest('tr')?.textContent?.includes('John Doe')
      )
      
      await user.click(userViewButton!)

      // Should open user details modal
      await waitFor(() => {
        expect(screen.getByText(/user details/i)).toBeInTheDocument()
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Should load user activities
      expect(mockApiClient.getUserActivities).toHaveBeenCalledWith('user-1')

      // Should show activities
      expect(screen.getByText(/login/i)).toBeInTheDocument()
      expect(screen.getByText(/api key created/i)).toBeInTheDocument()
    })

    it('should handle user details loading failure', async () => {
      const user = userEvent.setup()
      
      mockApiClient.getUserActivities.mockRejectedValue(new Error('Failed to load activities'))

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Click view details
      const viewButtons = screen.getAllByRole('button', { name: /view details/i })
      await user.click(viewButtons[0])

      // Should show error in activities section
      await waitFor(() => {
        expect(screen.getByText(/failed to load activities/i)).toBeInTheDocument()
      })
    })
  })

  describe('Filtering and Search', () => {
    beforeEach(() => {
      store.set(usersAtom, mockUsers)
    })

    it('should filter users by status', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Open status filter
      const statusFilter = screen.getByRole('button', { name: /status filter/i })
      await user.click(statusFilter)

      // Select pending filter
      const pendingOption = screen.getByRole('option', { name: /pending/i })
      await user.click(pendingOption)

      // Should only show pending users
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
      expect(screen.queryByText('Bob Johnson')).not.toBeInTheDocument()
    })

    it('should search users by name', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Type in search box
      const searchInput = screen.getByPlaceholderText(/search users/i)
      await user.type(searchInput, 'Jane')

      // Should only show matching users
      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
        expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
      })
    })

    it('should filter by role', async () => {
      const user = userEvent.setup()
      
      // Add admin user to the list
      const usersWithAdmin = [...mockUsers, mockAdminUser]
      store.set(usersAtom, usersWithAdmin)

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Admin User')).toBeInTheDocument()
      })

      // Filter by admin role
      const roleFilter = screen.getByRole('button', { name: /role filter/i })
      await user.click(roleFilter)

      const adminOption = screen.getByRole('option', { name: /admin/i })
      await user.click(adminOption)

      // Should only show admin users
      expect(screen.getByText('Admin User')).toBeInTheDocument()
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
    })
  })

  describe('Pagination and Sorting', () => {
    it('should handle pagination', async () => {
      const user = userEvent.setup()
      
      // Mock paginated response
      mockApiClient.getUsers.mockResolvedValue({
        data: mockUsers.slice(0, 2),
        page: 1,
        limit: 2,
        total: 3
      })

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      // Should show pagination controls
      expect(screen.getByText(/page 1 of 2/i)).toBeInTheDocument()
      
      // Click next page
      const nextButton = screen.getByRole('button', { name: /next page/i })
      await user.click(nextButton)

      // Should load next page
      await waitFor(() => {
        expect(mockApiClient.getUsers).toHaveBeenCalledWith(
          expect.any(Object),
          2,
          2
        )
      })
    })

    it('should sort users by column', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })

      // Click name column header to sort
      const nameHeader = screen.getByRole('button', { name: /sort by name/i })
      await user.click(nameHeader)

      // Should show sort indicator
      expect(screen.getByTestId('sort-asc-icon')).toBeInTheDocument()

      // Click again to reverse sort
      await user.click(nameHeader)
      expect(screen.getByTestId('sort-desc-icon')).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should handle user loading failure', async () => {
      mockApiClient.getUsers.mockRejectedValue(new Error('Failed to load users'))

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText(/failed to load users/i)).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
      })
    })

    it('should retry loading users', async () => {
      const user = userEvent.setup()
      
      mockApiClient.getUsers
        .mockRejectedValueOnce(new Error('Failed to load'))
        .mockResolvedValueOnce({
          data: mockUsers,
          page: 1,
          limit: 10,
          total: 3
        })

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      // Wait for error state
      await waitFor(() => {
        expect(screen.getByText(/failed to load users/i)).toBeInTheDocument()
      })

      // Click retry
      const retryButton = screen.getByRole('button', { name: /retry/i })
      await user.click(retryButton)

      // Should load successfully
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument()
      })
    })

    it('should handle user action failures', async () => {
      const user = userEvent.setup()
      
      mockApiClient.performUserAction.mockRejectedValue(new Error('Action failed'))

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      })

      // Try to approve user
      const approveButtons = screen.getAllByRole('button', { name: /approve/i })
      await user.click(approveButtons[0])

      const confirmButton = screen.getByRole('button', { name: /confirm/i })
      await user.click(confirmButton)

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/action failed/i)).toBeInTheDocument()
      })
    })
  })

  describe('Access Control', () => {
    it('should prevent non-admin users from accessing user management', async () => {
      const regularUser = { ...mockUsers[0] }
      const regularSession = { ...mockAdminSession, user: regularUser, userId: regularUser.id }
      
      store.set(sessionAtom, regularSession)
      store.set(userAtom, regularUser)

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      // Should show access denied
      await waitFor(() => {
        expect(screen.getByText(/access denied/i)).toBeInTheDocument()
      })
    })

    it('should prevent admin from deleting themselves', async () => {
      const usersWithCurrentAdmin = [...mockUsers, mockAdminUser]
      store.set(usersAtom, usersWithCurrentAdmin)

      render(
        <TestWrapper>
          <UserManagementPage />
        </TestWrapper>
      )

      await waitFor(() => {
        expect(screen.getByText('Admin User')).toBeInTheDocument()
      })

      // Find admin user row and check that delete button is disabled
      const adminRow = screen.getByText('Admin User').closest('tr')
      const deleteButton = adminRow?.querySelector('button[aria-label*="Delete"]')
      expect(deleteButton).toBeDisabled()
    })
  })
})
import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { <PERSON>Line<PERSON>hart, BaseBar<PERSON>hart, BasePie<PERSON>hart, BaseAreaChart } from '@/components/charts'
import type { ChartConfig, PieChartConfig, PieChartData } from '@/types/charts'

const mockLineData = [
  { date: '2024-01-01', requests: 100, errors: 5 },
  { date: '2024-01-02', requests: 150, errors: 3 },
  { date: '2024-01-03', requests: 120, errors: 8 },
]

const mockLineConfig: ChartConfig = {
  title: 'API Usage Over Time',
  xAxisKey: 'date',
  dataKeys: [
    { key: 'requests', name: 'Requests', color: '#8884d8' },
    { key: 'errors', name: 'Errors', color: '#82ca9d' },
  ],
}

const mockPieData: PieChartData[] = [
  { name: 'Success', value: 400 },
  { name: 'Error', value: 300 },
  { name: 'Timeout', value: 100 },
]

const mockPieConfig: PieChartConfig = {
  title: 'Response Status Distribution',
}

describe('Chart Components', () => {
  it('renders BaseLineChart with title', () => {
    render(<BaseLineChart data={mockLineData} config={mockLineConfig} />)
    expect(screen.getByText('API Usage Over Time')).toBeInTheDocument()
  })

  it('renders BaseBarChart with title', () => {
    render(<BaseBarChart data={mockLineData} config={mockLineConfig} />)
    expect(screen.getByText('API Usage Over Time')).toBeInTheDocument()
  })

  it('renders BasePieChart with title', () => {
    render(<BasePieChart data={mockPieData} config={mockPieConfig} />)
    expect(screen.getByText('Response Status Distribution')).toBeInTheDocument()
  })

  it('renders BaseAreaChart with title', () => {
    render(<BaseAreaChart data={mockLineData} config={mockLineConfig} />)
    expect(screen.getByText('API Usage Over Time')).toBeInTheDocument()
  })

  it('shows loading state', () => {
    render(<BaseLineChart data={[]} config={mockLineConfig} loading={true} />)
    expect(screen.getByText('API Usage Over Time')).toBeInTheDocument()
  })

  it('shows error state', () => {
    render(<BaseLineChart data={[]} config={mockLineConfig} error="Failed to load data" />)
    expect(screen.getByText('Failed to load data')).toBeInTheDocument()
  })
})
import { describe, it, expect, beforeEach } from 'vitest'
import { createStore } from 'jotai'
import { 
  users<PERSON>tom, 
  userFiltersAtom, 
  filteredUsersAtom,
  apiKeysAtom,
  apiKeyFiltersAtom,
  filteredApiKeysAtom,
  usageLogsAtom,
  analyticsFiltersAtom,
  filteredUsageLogsAtom
} from '../stores'
import type { User } from '../types/auth'
import type { ApiKey } from '../types/apiKey'
import type { ApiUsageLog } from '../types/analytics'

describe('State Management', () => {
  let store: ReturnType<typeof createStore>

  beforeEach(() => {
    store = createStore()
  })

  describe('User Management State', () => {
    const mockUsers: User[] = [
      {
        id: '1',
        email: '<EMAIL>',
        name: 'Admin User',
        role: 'admin',
        status: 'approved',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01'),
        apiAccessLevel: 'premium'
      },
      {
        id: '2',
        email: '<EMAIL>',
        name: 'Regular User',
        role: 'user',
        status: 'pending',
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02'),
        apiAccessLevel: 'basic'
      }
    ]

    it('should store and retrieve users', () => {
      store.set(usersAtom, mockUsers)
      const users = store.get(usersAtom)
      expect(users).toEqual(mockUsers)
    })

    it('should filter users by status', () => {
      store.set(usersAtom, mockUsers)
      store.set(userFiltersAtom, { status: 'approved', search: '' })
      
      const filteredUsers = store.get(filteredUsersAtom)
      expect(filteredUsers).toHaveLength(1)
      expect(filteredUsers[0].status).toBe('approved')
    })

    it('should filter users by search term', () => {
      store.set(usersAtom, mockUsers)
      store.set(userFiltersAtom, { status: 'all', search: 'admin' })
      
      const filteredUsers = store.get(filteredUsersAtom)
      expect(filteredUsers).toHaveLength(1)
      expect(filteredUsers[0].name.toLowerCase()).toContain('admin')
    })
  })

  describe('API Key Management State', () => {
    const mockApiKeys: ApiKey[] = [
      {
        id: '1',
        userId: 'user1',
        name: 'Production Key',
        keyHash: 'hash1',
        status: 'active',
        permissions: [{ resource: 'ollama', actions: ['read', 'write'] }],
        rateLimit: { requestsPerMinute: 100, requestsPerHour: 1000, requestsPerDay: 10000 },
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-01')
      },
      {
        id: '2',
        userId: 'user1',
        name: 'Development Key',
        keyHash: 'hash2',
        status: 'revoked',
        permissions: [{ resource: 'ollama', actions: ['read'] }],
        rateLimit: { requestsPerMinute: 50, requestsPerHour: 500, requestsPerDay: 2000 },
        createdAt: new Date('2024-01-02'),
        updatedAt: new Date('2024-01-02')
      }
    ]

    it('should store and retrieve API keys', () => {
      store.set(apiKeysAtom, mockApiKeys)
      const keys = store.get(apiKeysAtom)
      expect(keys).toEqual(mockApiKeys)
    })

    it('should filter API keys by status', () => {
      store.set(apiKeysAtom, mockApiKeys)
      store.set(apiKeyFiltersAtom, { status: 'active', search: '' })
      
      const filteredKeys = store.get(filteredApiKeysAtom)
      expect(filteredKeys).toHaveLength(1)
      expect(filteredKeys[0].status).toBe('active')
    })

    it('should filter API keys by search term', () => {
      store.set(apiKeysAtom, mockApiKeys)
      store.set(apiKeyFiltersAtom, { status: 'all', search: 'production' })
      
      const filteredKeys = store.get(filteredApiKeysAtom)
      expect(filteredKeys).toHaveLength(1)
      expect(filteredKeys[0].name.toLowerCase()).toContain('production')
    })
  })

  describe('Analytics State', () => {
    const mockLogs: ApiUsageLog[] = [
      {
        id: '1',
        userId: 'user1',
        apiKeyId: 'key1',
        endpoint: '/api/ollama/generate',
        method: 'POST',
        statusCode: 200,
        responseTime: 250,
        requestSize: 1000,
        responseSize: 5000,
        timestamp: new Date('2024-01-01T10:00:00Z')
      },
      {
        id: '2',
        userId: 'user2',
        apiKeyId: 'key2',
        endpoint: '/api/ollama/chat',
        method: 'POST',
        statusCode: 400,
        responseTime: 150,
        requestSize: 800,
        responseSize: 200,
        timestamp: new Date('2024-01-01T11:00:00Z'),
        errorMessage: 'Bad request'
      }
    ]

    it('should store and retrieve usage logs', () => {
      store.set(usageLogsAtom, mockLogs)
      const logs = store.get(usageLogsAtom)
      expect(logs).toEqual(mockLogs)
    })

    it('should filter logs by user ID', () => {
      store.set(usageLogsAtom, mockLogs)
      store.set(analyticsFiltersAtom, { 
        userId: 'user1',
        timeRange: { start: new Date('2024-01-01'), end: new Date('2024-01-02') },
        search: ''
      })
      
      const filteredLogs = store.get(filteredUsageLogsAtom)
      expect(filteredLogs).toHaveLength(1)
      expect(filteredLogs[0].userId).toBe('user1')
    })

    it('should filter logs by endpoint', () => {
      store.set(usageLogsAtom, mockLogs)
      store.set(analyticsFiltersAtom, { 
        endpoint: 'generate',
        timeRange: { start: new Date('2024-01-01'), end: new Date('2024-01-02') },
        search: ''
      })
      
      const filteredLogs = store.get(filteredUsageLogsAtom)
      expect(filteredLogs).toHaveLength(1)
      expect(filteredLogs[0].endpoint).toContain('generate')
    })

    it('should filter logs by status code', () => {
      store.set(usageLogsAtom, mockLogs)
      store.set(analyticsFiltersAtom, { 
        statusCode: 400,
        timeRange: { start: new Date('2024-01-01'), end: new Date('2024-01-02') },
        search: ''
      })
      
      const filteredLogs = store.get(filteredUsageLogsAtom)
      expect(filteredLogs).toHaveLength(1)
      expect(filteredLogs[0].statusCode).toBe(400)
    })
  })
})
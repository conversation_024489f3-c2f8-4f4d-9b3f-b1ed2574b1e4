import React, { useEffect, useState } from 'react'
import { use<PERSON>tom, useAtomValue, useSet<PERSON>tom } from 'jotai'
import {
  analyticsFiltersAtom,
  analyticsStatsSummaryAtom,
  loadAnalyticsDashboardAtom,
  loadTrendDataAtom,
  refreshAnalyticsDataAtom,
  updateAnalyticsFiltersAtom,
  setTimeRangePresetAtom,
  requestTrendsAtom,
  responseTimeTrendsAtom,
  errorTrendsAtom,
  userTrendsAtom,
  sortedUsageLogsAtom,
  performanceMetricsAtom,
  recentErrorsAtom
} from '../../stores/analytics'
import { ApiUsageStatsCards } from '../../components/admin/ApiUsageStatsCards'
import { ApiUsageFilters } from '../../components/admin/ApiUsageFilters'
import { ApiUsageCharts } from '../../components/admin/ApiUsageCharts'
import { ApiUsageTable } from '../../components/admin/ApiUsageTable'
import { PerformanceMetricsCard } from '../../components/admin/PerformanceMetricsCard'
import { RecentErrorsCard } from '../../components/admin/RecentErrorsCard'
import { ExportReportsDialog } from '../../components/admin/ExportReportsDialog'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { RefreshCw, Download, TrendingUp, Activity, AlertTriangle } from 'lucide-react'
import type { AnalyticsFilters } from '../../types/analytics'

export function ApiUsageDashboard() {
  const [isLoading, setIsLoading] = useState(false)
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false)
  
  // Atoms
  const filters = useAtomValue(analyticsFiltersAtom)
  const stats = useAtomValue(analyticsStatsSummaryAtom)
  const requestTrends = useAtomValue(requestTrendsAtom)
  const responseTimeTrends = useAtomValue(responseTimeTrendsAtom)
  const errorTrends = useAtomValue(errorTrendsAtom)
  const userTrends = useAtomValue(userTrendsAtom)
  const usageLogs = useAtomValue(sortedUsageLogsAtom)
  const performanceMetrics = useAtomValue(performanceMetricsAtom)
  const recentErrors = useAtomValue(recentErrorsAtom)
  
  // Actions
  const loadAnalyticsDashboard = useSetAtom(loadAnalyticsDashboardAtom)
  const loadTrendData = useSetAtom(loadTrendDataAtom)
  const refreshAnalyticsData = useSetAtom(refreshAnalyticsDataAtom)
  const updateFilters = useSetAtom(updateAnalyticsFiltersAtom)
  const setTimeRangePreset = useSetAtom(setTimeRangePresetAtom)

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        await Promise.all([
          loadAnalyticsDashboard(filters),
          loadTrendData(filters.timeRange)
        ])
      } catch (error) {
        console.error('Failed to load analytics data:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    loadData()
  }, [filters.timeRange, loadAnalyticsDashboard, loadTrendData])

  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      await refreshAnalyticsData()
    } catch (error) {
      console.error('Failed to refresh analytics data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleFiltersChange = (newFilters: Partial<AnalyticsFilters>) => {
    updateFilters(newFilters)
  }

  const handleTimeRangePreset = (preset: 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year') => {
    setTimeRangePreset(preset)
  }

  const handleExportReports = () => {
    setIsExportDialogOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">API Usage Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor API usage, performance metrics, and system analytics
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={handleExportReports}>
            <Download className="h-4 w-4" />
            Export Reports
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters & Time Range</CardTitle>
        </CardHeader>
        <CardContent>
          <ApiUsageFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onTimeRangePreset={handleTimeRangePreset}
          />
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <ApiUsageStatsCards stats={stats || {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
        totalDataTransferred: 0,
        uniqueUsers: 0,
        uniqueEndpoints: 0,
        errorRate: 0,
        successRate: 0
      }} isLoading={isLoading} />

      {/* Main Dashboard Content */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="logs" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Request Logs
          </TabsTrigger>
          <TabsTrigger value="errors" className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4" />
            Errors
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Usage Charts */}
          <ApiUsageCharts
            requestTrends={requestTrends}
            responseTimeTrends={responseTimeTrends}
            errorTrends={errorTrends}
            userTrends={userTrends}
            isLoading={isLoading}
          />
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Performance Metrics */}
            <PerformanceMetricsCard 
              metrics={performanceMetrics || {
                p50ResponseTime: 0,
                p95ResponseTime: 0,
                p99ResponseTime: 0,
                slowestEndpoints: [],
                fastestEndpoints: []
              }}
              isLoading={isLoading}
            />
            
            {/* System Health */}
            <Card>
              <CardHeader>
                <CardTitle>System Health</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">API Availability</span>
                    <span className="text-sm text-green-600">99.9%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Average Response Time</span>
                    <span className="text-sm">{stats?.averageResponseTime || 0}ms</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Error Rate</span>
                    <span className="text-sm text-red-600">
                      {((stats?.errorRate || 0) * 100).toFixed(2)}%
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Active Users</span>
                    <span className="text-sm">{stats?.uniqueUsers || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="logs" className="space-y-6">
          {/* Usage Logs Table */}
          <Card>
            <CardHeader>
              <CardTitle>Recent API Requests ({usageLogs.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <ApiUsageTable
                logs={usageLogs.slice(0, 100)} // Show first 100 logs
                isLoading={isLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-6">
          {/* Recent Errors */}
          <RecentErrorsCard 
            errors={recentErrors}
            isLoading={isLoading}
          />
        </TabsContent>
      </Tabs>

      {/* Export Reports Dialog */}
      <ExportReportsDialog
        open={isExportDialogOpen}
        onClose={() => setIsExportDialogOpen(false)}
        filters={filters}
        stats={stats || {
          totalRequests: 0,
          successfulRequests: 0,
          failedRequests: 0,
          averageResponseTime: 0,
          totalDataTransferred: 0,
          uniqueUsers: 0,
          uniqueEndpoints: 0,
          errorRate: 0,
          successRate: 0
        }}
      />
    </div>
  )
}
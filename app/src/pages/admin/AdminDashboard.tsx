import React, { useEffect, useState } from 'react'
import { useAtomValue, useSetAtom } from 'jotai'
import { 
  userStatsSummaryAtom,
  loadUsersWithStatsAtom
} from '../../stores/users'
import {
  analyticsStatsSummaryAtom,
  loadAnalyticsDashboardAtom,
  analyticsFiltersAtom
} from '../../stores/analytics'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { 
  Users, 
  Activity, 
  AlertTriangle, 
  TrendingUp,
  Clock,
  Shield,
  Database,
  Zap,
  RefreshCw,
  ArrowRight
} from 'lucide-react'
import { Link } from 'react-router-dom'

export function AdminDashboard() {
  const [isLoading, setIsLoading] = useState(false)
  
  // User stats
  const userStats = useAtomValue(userStatsSummaryAtom)
  const loadUsersWithStats = useSetAtom(loadUsersWithStatsAtom)
  
  // Analytics stats
  const analyticsStats = useAtomValue(analyticsStatsSummaryAtom)
  const analyticsFilters = useAtomValue(analyticsFiltersAtom)
  const loadAnalyticsDashboard = useSetAtom(loadAnalyticsDashboardAtom)

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        await Promise.all([
          loadUsersWithStats(),
          loadAnalyticsDashboard(analyticsFilters)
        ])
      } catch (error) {
        console.error('Failed to load dashboard data:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    loadData()
  }, [loadUsersWithStats, loadAnalyticsDashboard, analyticsFilters])

  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      await Promise.all([
        loadUsersWithStats(),
        loadAnalyticsDashboard(analyticsFilters)
      ])
    } catch (error) {
      console.error('Failed to refresh dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const quickStats = [
    {
      title: 'Total Users',
      value: userStats?.totalUsers || 0,
      icon: Users,
      color: 'text-blue-600',
      href: '/admin/users'
    },
    {
      title: 'Pending Approvals',
      value: userStats?.pendingApprovals || 0,
      icon: Clock,
      color: 'text-orange-600',
      href: '/admin/users?status=pending',
      badge: userStats?.pendingApprovals ? 'warning' : undefined
    },
    {
      title: 'API Requests (24h)',
      value: analyticsStats?.totalRequests || 0,
      icon: Activity,
      color: 'text-green-600',
      href: '/admin/usage'
    },
    {
      title: 'Error Rate',
      value: `${((analyticsStats?.errorRate || 0) * 100).toFixed(1)}%`,
      icon: AlertTriangle,
      color: (analyticsStats?.errorRate || 0) > 0.05 ? 'text-red-600' : 'text-green-600',
      href: '/admin/errors'
    }
  ]

  const systemHealth = {
    status: (analyticsStats?.successRate || 0) > 0.95 ? 'excellent' : 
            (analyticsStats?.successRate || 0) > 0.9 ? 'good' : 'poor',
    uptime: '99.9%',
    responseTime: analyticsStats?.averageResponseTime || 0,
    activeUsers: analyticsStats?.uniqueUsers || 0
  }

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600'
      case 'good': return 'text-yellow-600'
      case 'poor': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getHealthBadge = (status: string) => {
    switch (status) {
      case 'excellent': return 'success'
      case 'good': return 'warning'
      case 'poor': return 'destructive'
      default: return 'outline-solid'
    }
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            System overview and key metrics
          </p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <div className="flex items-center gap-2">
                  {stat.badge && (
                    <Badge variant={stat.badge as any} className="text-xs">
                      {stat.value}
                    </Badge>
                  )}
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <Link 
                  to={stat.href}
                  className="text-xs text-muted-foreground hover:text-foreground flex items-center gap-1 mt-1"
                >
                  View details
                  <ArrowRight className="h-3 w-3" />
                </Link>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* System Health & Quick Actions */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* System Health */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Status</span>
              <Badge variant={getHealthBadge(systemHealth.status) as any}>
                {systemHealth.status.charAt(0).toUpperCase() + systemHealth.status.slice(1)}
              </Badge>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span>Uptime</span>
                <span className="font-medium text-green-600">{systemHealth.uptime}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span>Avg Response Time</span>
                <span className={`font-medium ${
                  systemHealth.responseTime < 200 ? 'text-green-600' : 
                  systemHealth.responseTime < 500 ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {systemHealth.responseTime}ms
                </span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span>Active Users</span>
                <span className="font-medium">{systemHealth.activeUsers}</span>
              </div>
              
              <div className="flex items-center justify-between text-sm">
                <span>Success Rate</span>
                <span className="font-medium text-green-600">
                  {((analyticsStats?.successRate || 0) * 100).toFixed(1)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link to="/admin/users?status=pending">
              <Button variant="outline" className="w-full justify-start">
                <Clock className="h-4 w-4 mr-2" />
                Review Pending Users
                {(userStats?.pendingApprovals || 0) > 0 && (
                  <Badge variant="warning" className="ml-auto">
                    {userStats?.pendingApprovals || 0}
                  </Badge>
                )}
              </Button>
            </Link>
            
            <Link to="/admin/usage">
              <Button variant="outline" className="w-full justify-start">
                <TrendingUp className="h-4 w-4 mr-2" />
                View Usage Analytics
              </Button>
            </Link>
            
            <Link to="/admin/errors">
              <Button variant="outline" className="w-full justify-start">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Check Error Logs
              </Button>
            </Link>
            
            <Link to="/admin/settings">
              <Button variant="outline" className="w-full justify-start">
                <Shield className="h-4 w-4 mr-2" />
                System Settings
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity Summary */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">User Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>New Signups (7d)</span>
                <span className="font-medium">{userStats?.recentSignups || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Active Users</span>
                <span className="font-medium">{userStats?.activeUsers || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Admin Users</span>
                <span className="font-medium">{userStats?.adminUsers || 0}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">API Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Total Requests</span>
                <span className="font-medium">{analyticsStats?.totalRequests.toLocaleString() || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Unique Endpoints</span>
                <span className="font-medium">{analyticsStats?.uniqueEndpoints || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Data Transferred</span>
                <span className="font-medium">
                  {analyticsStats?.totalDataTransferred ? 
                    `${(analyticsStats.totalDataTransferred / 1024 / 1024).toFixed(1)} MB` : 
                    '0 MB'
                  }
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">System Resources</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>CPU Usage</span>
                <span className="font-medium text-green-600">12%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Memory Usage</span>
                <span className="font-medium text-yellow-600">68%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Disk Usage</span>
                <span className="font-medium text-green-600">45%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
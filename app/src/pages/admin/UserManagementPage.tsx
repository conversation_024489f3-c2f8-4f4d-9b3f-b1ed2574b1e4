import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON>, useAtomValue, useSetAtom } from 'jotai'
import { 
  usersAtom,
  userFiltersAtom,
  userPaginationAtom,
  paginatedUsersAtom,
  userStatsSummaryAtom,
  loadUsersWithStatsAtom,
  updateUserFiltersAtom,
  updateUserPagination<PERSON>tom,
  performUserActionAtom,
  selectedUserAtom,
  selectUserWithActivitiesAtom
} from '../../stores/users'
import { UserManagementTable } from '../../components/admin/UserManagementTable'
import { UserFilters } from '../../components/admin/UserFilters'
import { UserStatsCards } from '../../components/admin/UserStatsCards'
import { UserDetailModal } from '../../components/admin/UserDetailModal'
import { BulkActionsBar } from '../../components/admin/BulkActionsBar'
import { Card, CardContent, CardHeader, CardTit<PERSON> } from '../../components/ui/card'
import { But<PERSON> } from '../../components/ui/button'
import { RefreshCw, UserPlus } from 'lucide-react'
import type { User } from '../../types/auth'
import type { UserActionRequest } from '../../types/user'

export function UserManagementPage() {
  const [selectedUsers, setSelectedUsers] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  // Atoms
  const users = useAtomValue(paginatedUsersAtom)
  const filters = useAtomValue(userFiltersAtom)
  const pagination = useAtomValue(userPaginationAtom)
  const stats = useAtomValue(userStatsSummaryAtom)
  const selectedUser = useAtomValue(selectedUserAtom)
  
  // Actions
  const loadUsersWithStats = useSetAtom(loadUsersWithStatsAtom)
  const updateFilters = useSetAtom(updateUserFiltersAtom)
  const updatePagination = useSetAtom(updateUserPaginationAtom)
  const performUserAction = useSetAtom(performUserActionAtom.optimisticActionAtom)
  const selectUserWithActivities = useSetAtom(selectUserWithActivitiesAtom)
  const [, clearSelectedUser] = useAtom(selectedUserAtom)

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        await loadUsersWithStats(filters, pagination.page, pagination.limit)
      } catch (error) {
        console.error('Failed to load users:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    loadData()
  }, [filters, pagination.page, pagination.limit, loadUsersWithStats])

  const handleRefresh = async () => {
    setIsLoading(true)
    try {
      await loadUsersWithStats(filters, pagination.page, pagination.limit)
    } catch (error) {
      console.error('Failed to refresh users:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleUserAction = async (userId: string, action: UserActionRequest['action'], reason?: string) => {
    try {
      await performUserAction({ userId, action, reason })
      // Remove from selected users if action was performed
      setSelectedUsers(prev => prev.filter(id => id !== userId))
    } catch (error) {
      console.error(`Failed to ${action} user:`, error)
    }
  }

  const handleBulkAction = async (action: UserActionRequest['action'], reason?: string) => {
    try {
      await Promise.all(
        selectedUsers.map(userId => performUserAction({ userId, action, reason }))
      )
      setSelectedUsers([])
    } catch (error) {
      console.error(`Failed to perform bulk ${action}:`, error)
    }
  }

  const handleUserSelect = (userId: string, selected: boolean) => {
    setSelectedUsers(prev => 
      selected 
        ? [...prev, userId]
        : prev.filter(id => id !== userId)
    )
  }

  const handleSelectAll = (selected: boolean) => {
    setSelectedUsers(selected ? users.map(user => user.id) : [])
  }

  const handleViewUserDetails = (user: User) => {
    selectUserWithActivities(user)
  }

  const handleCloseUserDetails = () => {
    clearSelectedUser(null)
  }

  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    updateFilters(newFilters)
  }

  const handlePaginationChange = (newPagination: Partial<typeof pagination>) => {
    updatePagination(newPagination)
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Manage user accounts, approvals, and access permissions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button>
            <UserPlus className="h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <UserStatsCards stats={stats} />

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <UserFilters
            filters={filters}
            onFiltersChange={handleFilterChange}
          />
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <BulkActionsBar
          selectedCount={selectedUsers.length}
          onBulkAction={handleBulkAction}
          onClearSelection={() => setSelectedUsers([])}
        />
      )}

      {/* User Table */}
      <Card>
        <CardHeader>
          <CardTitle>Users ({users.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <UserManagementTable
            users={users}
            selectedUsers={selectedUsers}
            pagination={pagination}
            isLoading={isLoading}
            onUserSelect={handleUserSelect}
            onSelectAll={handleSelectAll}
            onUserAction={handleUserAction}
            onViewDetails={handleViewUserDetails}
            onPaginationChange={handlePaginationChange}
          />
        </CardContent>
      </Card>

      {/* User Detail Modal */}
      {selectedUser && (
        <UserDetailModal
          user={selectedUser}
          open={!!selectedUser}
          onClose={handleCloseUserDetails}
          onUserAction={handleUserAction}
        />
      )}
    </div>
  )
}
import React, { useEffect, useState } from 'react'
import { useAtom, useAtomValue } from 'jotai'
import { 
  Key, 
  BarChart3, 
  User, 
  Activity, 
  TrendingUp, 
  Shield,
  Plus,
  ArrowRight,
  Loader2
} from 'lucide-react'
import { <PERSON> } from 'react-router-dom'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { toast } from '../../hooks/useToast'
import { 
  userApiKeysAtom,
  apiKeyStatsSummaryAtom,
  loadUserApiKeysWithStatsAtom,
  apiKeyUsageAtom
} from '../../stores/apiKeys'
import { userAtom } from '../../stores/auth'
import UsageMetricsCard, { createRequestMetrics } from '../../components/user/UsageMetricsCard'
import CreateApiKeyDialog from '../../components/user/CreateApiKeyDialog'

const UserDashboard: React.FC = () => {
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const user = useAtomValue(userAtom)
  const apiKeys = useAtomValue(userApiKeysAtom)
  const stats = useAtomValue(apiKeyStatsSummaryAtom)
  const usageData = useAtomValue(apiKeyUsageAtom)
  const [, loadApiKeys] = useAtom(loadUserApiKeysWithStatsAtom)

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        const result = await loadApiKeys()
        if (result.errors.length > 0) {
          toast({
            title: 'Warning',
            description: 'Some data could not be loaded',
            variant: 'destructive',
          })
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to load dashboard data',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }

    if (user) {
      loadData()
    } else {
      setIsLoading(false)
    }
  }, [user, loadApiKeys])

  // Aggregate usage data for overview
  const aggregatedUsage = React.useMemo(() => {
    return apiKeys.reduce((acc, key) => {
      const usage = usageData[key.id]
      if (!usage) return acc

      return {
        totalRequests: acc.totalRequests + usage.totalRequests,
        successfulRequests: acc.successfulRequests + usage.successfulRequests,
        failedRequests: acc.failedRequests + usage.failedRequests,
        averageResponseTime: (acc.averageResponseTime + usage.averageResponseTime) / 2,
        dataTransferred: acc.dataTransferred + usage.dataTransferred
      }
    }, {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      dataTransferred: 0
    })
  }, [apiKeys, usageData])

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center gap-4">
              <Loader2 className="h-6 w-6 animate-spin" />
              <p className="text-muted-foreground">Loading your dashboard...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">Please log in to view your dashboard</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  const requestMetrics = createRequestMetrics({
    totalRequests: aggregatedUsage.totalRequests,
    successfulRequests: aggregatedUsage.successfulRequests,
    failedRequests: aggregatedUsage.failedRequests,
    averageResponseTime: aggregatedUsage.averageResponseTime
  })

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Welcome back, {user.name}!</h1>
          <p className="text-muted-foreground">
            Here's an overview of your API usage and account status
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create API Key
        </Button>
      </div>

      {/* Account Status */}
      {user.status !== 'approved' && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Shield className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">Account Pending Approval</p>
                <p className="text-sm text-yellow-700">
                  Your account is currently pending admin approval. You'll be able to create API keys once approved.
                </p>
              </div>
              <Badge variant="outline" className="border-yellow-500 text-yellow-700">
                {user.status}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">API Keys</p>
                <p className="text-2xl font-bold">{stats?.totalKeys || 0}</p>
              </div>
              <Key className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2 flex items-center gap-2">
              <Badge variant="outline" className="text-green-700 border-green-500">
                {stats?.activeKeys || 0} active
              </Badge>
              {(stats?.expiringSoon || 0) > 0 && (
                <Badge variant="outline" className="text-yellow-700 border-yellow-500">
                  {stats?.expiringSoon || 0} expiring
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Requests</p>
                <p className="text-2xl font-bold">{aggregatedUsage.totalRequests.toLocaleString()}</p>
              </div>
              <Activity className="h-8 w-8 text-green-500" />
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Last 30 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold text-green-600">
                  {aggregatedUsage.totalRequests > 0 
                    ? ((aggregatedUsage.successfulRequests / aggregatedUsage.totalRequests) * 100).toFixed(1)
                    : 0
                  }%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              API reliability
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Response</p>
                <p className="text-2xl font-bold">
                  {aggregatedUsage.averageResponseTime.toFixed(0)}ms
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Response time
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Usage Overview */}
      {aggregatedUsage.totalRequests > 0 && (
        <UsageMetricsCard
          title="Usage Overview (Last 30 Days)"
          metrics={requestMetrics}
        />
      )}

      {/* Recent API Keys */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-base">Recent API Keys</CardTitle>
          <Button variant="outline" size="sm" asChild>
            <Link to="/dashboard/api-keys">
              View All
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </Button>
        </CardHeader>
        <CardContent>
          {apiKeys.length === 0 ? (
            <div className="text-center py-8">
              <Key className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No API Keys</h3>
              <p className="text-muted-foreground mb-4">
                Create your first API key to start using Ollama services
              </p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create API Key
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {apiKeys.slice(0, 3).map((key) => (
                <div key={key.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Key className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{key.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Created {formatDate(key.createdAt)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={key.status === 'active' ? 'default' : 'destructive'}
                      className={key.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                    >
                      {key.status}
                    </Badge>
                    {key.lastUsedAt && (
                      <p className="text-sm text-muted-foreground">
                        Used {formatDate(key.lastUsedAt)}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Link to="/dashboard/api-keys">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <Key className="h-8 w-8 text-blue-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Manage API Keys</h3>
              <p className="text-sm text-muted-foreground">
                Create, view, and manage your API keys
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link to="/dashboard/analytics">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <BarChart3 className="h-8 w-8 text-green-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Usage Analytics</h3>
              <p className="text-sm text-muted-foreground">
                Monitor your API usage and performance
              </p>
            </CardContent>
          </Card>
        </Link>

        <Link to="/dashboard/profile">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <User className="h-8 w-8 text-purple-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Account Settings</h3>
              <p className="text-sm text-muted-foreground">
                Update your profile and security settings
              </p>
            </CardContent>
          </Card>
        </Link>
      </div>

      {/* Create API Key Dialog */}
      <CreateApiKeyDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </div>
  )
}

export default UserDashboard
import React, { useEffect } from 'react'
import { useAtom } from 'jotai'
import { Loader2 } from 'lucide-react'
import { Card, CardContent } from '../../components/ui/card'
import { toast } from '../../hooks/useToast'
import { loadUserApiKeysWithStatsAtom } from '../../stores/apiKeys'
import ApiKeyList from '../../components/user/ApiKeyList'

const ApiKeysPage: React.FC = () => {
  const [, loadApiKeys] = useAtom(loadUserApiKeysWithStatsAtom)
  const [isLoading, setIsLoading] = React.useState(true)

  useEffect(() => {
    const loadData = async () => {
      try {
        const result = await loadApiKeys()
        if (result.errors.length > 0) {
          toast({
            title: 'Warning',
            description: 'Some data could not be loaded',
            variant: 'destructive',
          })
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to load API keys',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [loadApiKeys])

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center gap-4">
              <Loader2 className="h-6 w-6 animate-spin" />
              <p className="text-muted-foreground">Loading your API keys...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <ApiKeyList />
    </div>
  )
}

export default ApiKeysPage
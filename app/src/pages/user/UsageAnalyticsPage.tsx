import React, { useState, useEffect } from 'react'
import { use<PERSON>tom, useAtomValue } from 'jotai'
import { 
  Calendar, 
  Download, 
  Filter, 
  BarChart3, 
  TrendingUp,
  Activity,
  Clock,
  Database
} from 'lucide-react'
import { Button } from '../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'
import { toast } from '../../hooks/useToast'
import { 
  userApiKeysAtom,
  apiKeyUsageAtom,
  fetchApiKeyUsageAtom
} from '../../stores/apiKeys'
import { userAtom } from '../../stores/auth'
import UsageMetricsCard, { createRequestMetrics, createDataTransferMetrics } from '../../components/user/UsageMetricsCard'
import UsageChart from '../../components/user/UsageChart'
import type { TimeRange } from '../../types/api'

interface TimeRangeOption {
  label: string
  value: string
  range: TimeRange
}

const TIME_RANGES: TimeRangeOption[] = [
  {
    label: 'Last 7 days',
    value: '7d',
    range: {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
  },
  {
    label: 'Last 30 days',
    value: '30d',
    range: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
  },
  {
    label: 'Last 90 days',
    value: '90d',
    range: {
      start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
  },
  {
    label: 'This month',
    value: 'month',
    range: {
      start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      end: new Date()
    }
  }
]

const UsageAnalyticsPage: React.FC = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRangeOption>(TIME_RANGES[1] || TIME_RANGES[0])
  const [selectedApiKey, setSelectedApiKey] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')

  const user = useAtomValue(userAtom)
  const apiKeys = useAtomValue(userApiKeysAtom)
  const usageData = useAtomValue(apiKeyUsageAtom)
  const [, fetchUsage] = useAtom(fetchApiKeyUsageAtom.actionAtom)

  // Load usage data when filters change
  useEffect(() => {
    const loadUsageData = async () => {
      if (!user || apiKeys.length === 0) return

      setIsLoading(true)
      try {
        if (selectedApiKey === 'all') {
          // Load usage for all API keys
          await Promise.all(
            apiKeys.map(key => 
              fetchUsage(key.id, selectedTimeRange.range)
            )
          )
        } else {
          // Load usage for selected API key
          await fetchUsage(selectedApiKey, selectedTimeRange.range)
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to load usage data',
          variant: 'destructive',
        })
      } finally {
        setIsLoading(false)
      }
    }

    loadUsageData()
  }, [selectedTimeRange, selectedApiKey, user, apiKeys, fetchUsage])

  // Aggregate usage data
  const aggregatedUsage = React.useMemo(() => {
    const relevantKeys = selectedApiKey === 'all' 
      ? apiKeys 
      : apiKeys.filter(key => key.id === selectedApiKey)

    const totalUsage = relevantKeys.reduce((acc, key) => {
      const usage = usageData[key.id]
      if (!usage) return acc

      return {
        totalRequests: acc.totalRequests + usage.totalRequests,
        successfulRequests: acc.successfulRequests + usage.successfulRequests,
        failedRequests: acc.failedRequests + usage.failedRequests,
        averageResponseTime: (acc.averageResponseTime + usage.averageResponseTime) / 2,
        dataTransferred: acc.dataTransferred + usage.dataTransferred,
        topEndpoints: [...acc.topEndpoints, ...usage.topEndpoints]
      }
    }, {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      dataTransferred: 0,
      topEndpoints: [] as Array<{endpoint: string, count: number, averageResponseTime: number}>
    })

    // Aggregate top endpoints
    const endpointMap = new Map()
    totalUsage.topEndpoints.forEach(endpoint => {
      if (endpointMap.has(endpoint.endpoint)) {
        const existing = endpointMap.get(endpoint.endpoint)
        endpointMap.set(endpoint.endpoint, {
          endpoint: endpoint.endpoint,
          count: existing.count + endpoint.count,
          averageResponseTime: (existing.averageResponseTime + endpoint.averageResponseTime) / 2
        })
      } else {
        endpointMap.set(endpoint.endpoint, endpoint)
      }
    })

    totalUsage.topEndpoints = Array.from(endpointMap.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    return totalUsage
  }, [apiKeys, usageData, selectedApiKey])

  // Generate chart data
  const chartData = React.useMemo(() => {
    // Generate mock time series data based on aggregated usage
    const days = Math.ceil((selectedTimeRange.range.end.getTime() - selectedTimeRange.range.start.getTime()) / (24 * 60 * 60 * 1000))
    const dailyAverage = aggregatedUsage.totalRequests / days

    return Array.from({ length: days }, (_, i) => {
      const date = new Date(selectedTimeRange.range.start.getTime() + i * 24 * 60 * 60 * 1000)
      const variance = 0.3 // 30% variance
      const requests = Math.max(0, Math.round(dailyAverage * (1 + (Math.random() - 0.5) * variance)))
      const successRate = 0.85 + Math.random() * 0.1 // 85-95% success rate
      
      return {
        timestamp: date.toISOString(),
        date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        requests,
        successfulRequests: Math.round(requests * successRate),
        failedRequests: Math.round(requests * (1 - successRate)),
        responseTime: aggregatedUsage.averageResponseTime * (0.8 + Math.random() * 0.4),
        dataTransferred: Math.round(requests * 1024 * (1 + Math.random()))
      }
    })
  }, [aggregatedUsage, selectedTimeRange])

  const endpointChartData = React.useMemo(() => {
    const total = aggregatedUsage.topEndpoints.reduce((sum, ep) => sum + ep.count, 0)
    return aggregatedUsage.topEndpoints.map(endpoint => ({
      ...endpoint,
      percentage: total > 0 ? (endpoint.count / total) * 100 : 0
    }))
  }, [aggregatedUsage.topEndpoints])

  const handleExportData = () => {
    const exportData = {
      timeRange: selectedTimeRange.label,
      apiKey: selectedApiKey === 'all' ? 'All API Keys' : apiKeys.find(k => k.id === selectedApiKey)?.name,
      summary: aggregatedUsage,
      chartData,
      generatedAt: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `usage-analytics-${selectedTimeRange.value}-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: 'Export Complete',
      description: 'Usage data has been exported successfully',
    })
  }

  const requestMetrics = createRequestMetrics({
    totalRequests: aggregatedUsage.totalRequests,
    successfulRequests: aggregatedUsage.successfulRequests,
    failedRequests: aggregatedUsage.failedRequests,
    averageResponseTime: aggregatedUsage.averageResponseTime
  })

  const dataTransferMetrics = createDataTransferMetrics({
    dataTransferred: aggregatedUsage.dataTransferred,
    requestsThisMonth: aggregatedUsage.totalRequests,
    monthlyLimit: 100000, // Example limit
    dailyAverage: aggregatedUsage.totalRequests / 30
  })

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Usage Analytics</h1>
          <p className="text-muted-foreground">
            Monitor your API usage patterns and performance metrics
          </p>
        </div>
        <Button onClick={handleExportData} variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export Data
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <Select
                value={selectedTimeRange.value}
                onValueChange={(value) => {
                  const range = TIME_RANGES.find(r => r.value === value)
                  if (range) setSelectedTimeRange(range)
                }}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TIME_RANGES.map((range) => (
                    <SelectItem key={range.value} value={range.value}>
                      {range.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <Select
                value={selectedApiKey}
                onValueChange={setSelectedApiKey}
              >
                <SelectTrigger className="w-[200px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All API Keys</SelectItem>
                  {apiKeys.map((key) => (
                    <SelectItem key={key.id} value={key.id}>
                      {key.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {isLoading && (
              <Badge variant="outline">
                <Activity className="h-3 w-3 mr-1 animate-spin" />
                Loading...
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
          <TabsTrigger value="comparison">Comparison</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Request Metrics */}
          <UsageMetricsCard
            title="Request Statistics"
            metrics={requestMetrics}
          />

          {/* Data Transfer Metrics */}
          <UsageMetricsCard
            title="Data Transfer & Limits"
            metrics={dataTransferMetrics}
          />

          {/* Request Trends Chart */}
          <UsageChart
            data={chartData}
            type="line"
            metric="requests"
            title="Request Trends"
            height={350}
          />
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Response Time Chart */}
          <UsageChart
            data={chartData}
            type="area"
            metric="responseTime"
            title="Response Time Trends"
            height={350}
          />

          {/* Data Transfer Chart */}
          <UsageChart
            data={chartData}
            type="bar"
            metric="dataTransfer"
            title="Daily Data Transfer"
            height={350}
          />
        </TabsContent>

        <TabsContent value="endpoints" className="space-y-6">
          {/* Endpoint Usage Distribution */}
          <UsageChart
            data={[]}
            endpointData={endpointChartData}
            type="pie"
            metric="endpoints"
            title="Endpoint Usage Distribution"
            height={400}
          />

          {/* Top Endpoints Table */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Top Endpoints</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {aggregatedUsage.topEndpoints.map((endpoint, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <p className="font-medium">{endpoint.endpoint}</p>
                        <p className="text-sm text-muted-foreground">
                          Avg response: {endpoint.averageResponseTime.toFixed(0)}ms
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">{endpoint.count.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">requests</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-6">
          {/* API Key Comparison */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">API Key Performance Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {apiKeys.map((key) => {
                  const usage = usageData[key.id]
                  const successRate = usage ? (usage.successfulRequests / usage.totalRequests) * 100 : 0
                  
                  return (
                    <div key={key.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium">{key.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            Created {new Date(key.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        <Badge 
                          variant={key.status === 'active' ? 'default' : 'destructive'}
                          className={key.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                        >
                          {key.status}
                        </Badge>
                      </div>
                      
                      {usage ? (
                        <div className="grid grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Requests</p>
                            <p className="font-medium">{usage.totalRequests.toLocaleString()}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Success Rate</p>
                            <p className="font-medium text-green-600">{successRate.toFixed(1)}%</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Avg Response</p>
                            <p className="font-medium">{usage.averageResponseTime.toFixed(0)}ms</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Data Transfer</p>
                            <p className="font-medium">
                              {usage.dataTransferred >= 1024 * 1024 
                                ? `${(usage.dataTransferred / (1024 * 1024)).toFixed(1)}MB`
                                : `${(usage.dataTransferred / 1024).toFixed(1)}KB`
                              }
                            </p>
                          </div>
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">No usage data available</p>
                      )}
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default UsageAnalyticsPage
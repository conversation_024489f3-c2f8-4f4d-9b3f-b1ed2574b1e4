@import 'tailwindcss';

@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';

@theme {
  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-admin: hsl(var(--admin));
  --color-admin-foreground: hsl(var(--admin-foreground));
  --color-admin-hover: hsl(var(--admin-hover));

  --color-success: hsl(var(--success));
  --color-success-foreground: hsl(var(--success-foreground));

  --color-warning: hsl(var(--warning));
  --color-warning-foreground: hsl(var(--warning-foreground));

  --color-info: hsl(var(--info));
  --color-info-foreground: hsl(var(--info-foreground));

  --color-sidebar: hsl(var(--sidebar));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));

  --color-header: hsl(var(--header));
  --color-header-foreground: hsl(var(--header-foreground));
  --color-header-border: hsl(var(--header-border));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --spacing-sidebar: var(--sidebar-width);
  --spacing-header: var(--header-height);
  --spacing-content: var(--content-padding);

  --font-sans:
    Inter, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto,
    sans-serif;
  --font-mono: JetBrains Mono, Fira Code, Monaco, Consolas, monospace;

  --text-xs: 0.75rem;
  --text-xs--line-height: 1rem;
  --text-sm: 0.875rem;
  --text-sm--line-height: 1.25rem;
  --text-base: 1rem;
  --text-base--line-height: 1.5rem;
  --text-lg: 1.125rem;
  --text-lg--line-height: 1.75rem;
  --text-xl: 1.25rem;
  --text-xl--line-height: 1.75rem;
  --text-2xl: 1.5rem;
  --text-2xl--line-height: 2rem;
  --text-3xl: 1.875rem;
  --text-3xl--line-height: 2.25rem;
  --text-4xl: 2.25rem;
  --text-4xl--line-height: 2.5rem;

  --shadow-admin: var(--admin-shadow);
  --shadow-card-hover: var(--card-hover-shadow);
  --shadow-sidebar: var(--sidebar-shadow);

  --animate-fade-in: fadeIn 0.2s ease-in-out;
  --animate-slide-in: slideIn 0.3s ease-out;
  --animate-slide-out: slideOut 0.3s ease-in;

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes slideIn {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(0);
    }
  }
  @keyframes slideOut {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
  }
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

:root {
  /* Base shadcn/ui CSS variables */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;

  /* Admin panel specific variables */
  --admin: 221.2 83.2% 53.3%;
  --admin-foreground: 210 40% 98%;
  --admin-hover: 221.2 83.2% 48%;
  --success: 142.1 76.2% 36.3%;
  --success-foreground: 355.7 100% 97.3%;
  --warning: 32.5 94.6% 43.7%;
  --warning-foreground: 355.7 100% 97.3%;
  --info: 204.4 94% 53.9%;
  --info-foreground: 355.7 100% 97.3%;

  /* Layout variables */
  --sidebar-width: 16rem;
  --header-height: 4rem;
  --content-padding: 1.5rem;
  --sidebar: 0 0% 98%;
  --sidebar-foreground: 222.2 84% 4.9%;
  --sidebar-border: 214.3 31.8% 91.4%;
  --header: 0 0% 100%;
  --header-foreground: 222.2 84% 4.9%;
  --header-border: 214.3 31.8% 91.4%;

  /* Shadows */
  --admin-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --card-hover-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --sidebar-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;

  /* Admin panel dark mode */
  --admin: 217.2 91.2% 59.8%;
  --admin-foreground: 222.2 84% 4.9%;
  --admin-hover: 217.2 91.2% 54%;
  --sidebar: 222.2 84% 4.9%;
  --sidebar-foreground: 210 40% 98%;
  --sidebar-border: 217.2 32.6% 17.5%;
  --header: 222.2 84% 4.9%;
  --header-foreground: 210 40% 98%;
  --header-border: 217.2 32.6% 17.5%;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Navigation item styles */
.nav-item {
  @apply flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors;
}

.nav-item-active {
  @apply bg-primary text-primary-foreground;
}

.nav-item-inactive {
  @apply text-muted-foreground hover:text-foreground hover:bg-muted;
}
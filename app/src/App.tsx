import { RouterProvider } from 'react-router-dom'
import { Provider } from 'jotai'
import { router } from './router'
import ErrorBoundary from './components/ErrorBoundary'
import { useSessionMonitoring } from './hooks/useSessionMonitoring'
import { ThemeProvider } from './contexts/ThemeContext'

// Component to handle session monitoring
function AppWithSessionMonitoring() {
  useSessionMonitoring() // This will monitor localStorage changes and validate sessions
  
  return <RouterProvider router={router} />
}

function App() {
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <Provider>
          <AppWithSessionMonitoring />
        </Provider>
      </ThemeProvider>
    </ErrorBoundary>
  )
}

export default App

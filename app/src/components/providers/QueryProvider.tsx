// Query client provider component
import React, { useEffect } from 'react'
import { QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { queryClient, cacheWarmer, startCacheMaintenance } from '../../lib/query-client'

interface QueryProviderProps {
  children: React.ReactNode
}

export function QueryProvider({ children }: QueryProviderProps) {
  useEffect(() => {
    // Start cache maintenance
    const cleanup = startCacheMaintenance()
    
    // Warm cache on mount
    cacheWarmer.warmCache()
    
    return cleanup
  }, [])

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {import.meta.env.DEV && (
        <ReactQueryDevtools 
          initialIsOpen={false}
          position="bottom-right"
        />
      )}
    </QueryClientProvider>
  )
}
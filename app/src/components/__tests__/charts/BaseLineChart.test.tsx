import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { BaseLineChart } from '../../charts/BaseLineChart'
import type { ChartData, ChartConfig } from '../../../types/charts'

// Mock Recharts components
vi.mock('recharts', () => ({
  LineChart: ({ children, ...props }: any) => (
    <div data-testid="line-chart" {...props}>
      {children}
    </div>
  ),
  Line: ({ dataKey, stroke, ...props }: any) => (
    <div data-testid={`line-${dataKey}`} data-stroke={stroke} {...props} />
  ),
  XAxis: ({ dataKey, ...props }: any) => (
    <div data-testid="x-axis" data-key={dataKey} {...props} />
  ),
  YAxis: ({ ...props }: any) => (
    <div data-testid="y-axis" {...props} />
  ),
  CartesianGrid: ({ ...props }: any) => (
    <div data-testid="cartesian-grid" {...props} />
  ),
  Tooltip: ({ content, ...props }: any) => (
    <div data-testid="tooltip" {...props}>
      {content && typeof content === 'function' ? content({}) : content}
    </div>
  ),
  Legend: ({ ...props }: any) => (
    <div data-testid="legend" {...props} />
  ),
  ResponsiveContainer: ({ children, ...props }: any) => (
    <div data-testid="responsive-container" {...props}>
      {children}
    </div>
  )
}))

const mockData: ChartData[] = [
  { timestamp: '2024-01-01', value: 100, label: 'Jan 1' },
  { timestamp: '2024-01-02', value: 150, label: 'Jan 2' },
  { timestamp: '2024-01-03', value: 120, label: 'Jan 3' },
  { timestamp: '2024-01-04', value: 180, label: 'Jan 4' },
  { timestamp: '2024-01-05', value: 200, label: 'Jan 5' }
]

const mockConfig: ChartConfig = {
  title: 'API Usage Over Time',
  xAxisKey: 'timestamp',
  yAxisKey: 'value',
  lines: [
    {
      dataKey: 'value',
      stroke: '#3b82f6',
      name: 'Requests',
      strokeWidth: 2
    }
  ],
  showGrid: true,
  showTooltip: true,
  showLegend: true
}

describe('BaseLineChart', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render chart with data', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
      />
    )

    expect(screen.getByTestId('responsive-container')).toBeInTheDocument()
    expect(screen.getByTestId('line-chart')).toBeInTheDocument()
    expect(screen.getByTestId('line-value')).toBeInTheDocument()
    expect(screen.getByTestId('x-axis')).toBeInTheDocument()
    expect(screen.getByTestId('y-axis')).toBeInTheDocument()
  })

  it('should render chart title', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
      />
    )

    expect(screen.getByText('API Usage Over Time')).toBeInTheDocument()
  })

  it('should render grid when showGrid is true', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
      />
    )

    expect(screen.getByTestId('cartesian-grid')).toBeInTheDocument()
  })

  it('should not render grid when showGrid is false', () => {
    const configWithoutGrid = { ...mockConfig, showGrid: false }
    
    render(
      <BaseLineChart
        data={mockData}
        config={configWithoutGrid}
      />
    )

    expect(screen.queryByTestId('cartesian-grid')).not.toBeInTheDocument()
  })

  it('should render tooltip when showTooltip is true', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
      />
    )

    expect(screen.getByTestId('tooltip')).toBeInTheDocument()
  })

  it('should not render tooltip when showTooltip is false', () => {
    const configWithoutTooltip = { ...mockConfig, showTooltip: false }
    
    render(
      <BaseLineChart
        data={mockData}
        config={configWithoutTooltip}
      />
    )

    expect(screen.queryByTestId('tooltip')).not.toBeInTheDocument()
  })

  it('should render legend when showLegend is true', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
      />
    )

    expect(screen.getByTestId('legend')).toBeInTheDocument()
  })

  it('should not render legend when showLegend is false', () => {
    const configWithoutLegend = { ...mockConfig, showLegend: false }
    
    render(
      <BaseLineChart
        data={mockData}
        config={configWithoutLegend}
      />
    )

    expect(screen.queryByTestId('legend')).not.toBeInTheDocument()
  })

  it('should render multiple lines', () => {
    const multiLineConfig: ChartConfig = {
      ...mockConfig,
      lines: [
        {
          dataKey: 'value',
          stroke: '#3b82f6',
          name: 'Requests',
          strokeWidth: 2
        },
        {
          dataKey: 'errors',
          stroke: '#ef4444',
          name: 'Errors',
          strokeWidth: 2
        }
      ]
    }

    const dataWithErrors = mockData.map(item => ({ ...item, errors: Math.floor(item.value * 0.1) }))
    
    render(
      <BaseLineChart
        data={dataWithErrors}
        config={multiLineConfig}
      />
    )

    expect(screen.getByTestId('line-value')).toBeInTheDocument()
    expect(screen.getByTestId('line-errors')).toBeInTheDocument()
  })

  it('should apply correct stroke colors to lines', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
      />
    )

    const line = screen.getByTestId('line-value')
    expect(line).toHaveAttribute('data-stroke', '#3b82f6')
  })

  it('should handle empty data gracefully', () => {
    render(
      <BaseLineChart
        data={[]}
        config={mockConfig}
      />
    )

    expect(screen.getByTestId('line-chart')).toBeInTheDocument()
    expect(screen.getByText(/no data available/i)).toBeInTheDocument()
  })

  it('should show loading state', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
        isLoading={true}
      />
    )

    expect(screen.getByTestId('chart-skeleton')).toBeInTheDocument()
  })

  it('should show error state', () => {
    const error = new Error('Failed to load chart data')
    
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
        error={error}
      />
    )

    expect(screen.getByText(/failed to load chart data/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument()
  })

  it('should call onRetry when retry button is clicked', async () => {
    const user = userEvent.setup()
    const mockOnRetry = vi.fn()
    const error = new Error('Failed to load chart data')
    
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
        error={error}
        onRetry={mockOnRetry}
      />
    )

    const retryButton = screen.getByRole('button', { name: /retry/i })
    await user.click(retryButton)

    expect(mockOnRetry).toHaveBeenCalled()
  })

  it('should handle custom tooltip content', () => {
    const customTooltipConfig = {
      ...mockConfig,
      customTooltip: ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
          return (
            <div data-testid="custom-tooltip">
              <p>{`Date: ${label}`}</p>
              <p>{`Value: ${payload[0].value}`}</p>
            </div>
          )
        }
        return null
      }
    }
    
    render(
      <BaseLineChart
        data={mockData}
        config={customTooltipConfig}
      />
    )

    // The custom tooltip should be rendered within the Tooltip component
    expect(screen.getByTestId('tooltip')).toBeInTheDocument()
  })

  it('should handle click events on data points', async () => {
    const user = userEvent.setup()
    const mockOnDataPointClick = vi.fn()
    
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
        onDataPointClick={mockOnDataPointClick}
      />
    )

    // Simulate clicking on a data point
    const chart = screen.getByTestId('line-chart')
    await user.click(chart)

    // Note: In a real implementation, this would be handled by Recharts
    // Here we're just testing that the prop is passed correctly
    expect(chart).toBeInTheDocument()
  })

  it('should be responsive', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
      />
    )

    const responsiveContainer = screen.getByTestId('responsive-container')
    expect(responsiveContainer).toBeInTheDocument()
  })

  it('should apply custom className', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
        className="custom-chart-class"
      />
    )

    const chartContainer = screen.getByTestId('line-chart').parentElement
    expect(chartContainer).toHaveClass('custom-chart-class')
  })

  it('should handle data formatting', () => {
    const dataWithDifferentFormat = [
      { date: '2024-01-01', count: 100 },
      { date: '2024-01-02', count: 150 }
    ]

    const customConfig: ChartConfig = {
      ...mockConfig,
      xAxisKey: 'date',
      yAxisKey: 'count',
      lines: [
        {
          dataKey: 'count',
          stroke: '#3b82f6',
          name: 'Count',
          strokeWidth: 2
        }
      ]
    }
    
    render(
      <BaseLineChart
        data={dataWithDifferentFormat}
        config={customConfig}
      />
    )

    expect(screen.getByTestId('x-axis')).toHaveAttribute('data-key', 'date')
    expect(screen.getByTestId('line-count')).toBeInTheDocument()
  })

  it('should handle accessibility features', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
      />
    )

    const chart = screen.getByTestId('line-chart')
    expect(chart).toHaveAttribute('role', 'img')
    expect(chart).toHaveAttribute('aria-label', expect.stringContaining('API Usage Over Time'))
  })

  it('should support different chart heights', () => {
    render(
      <BaseLineChart
        data={mockData}
        config={mockConfig}
        height={400}
      />
    )

    const responsiveContainer = screen.getByTestId('responsive-container')
    expect(responsiveContainer).toHaveAttribute('height', '400')
  })

  it('should handle animation settings', () => {
    const animatedConfig = {
      ...mockConfig,
      animationDuration: 1000,
      animationEasing: 'ease-in-out'
    }
    
    render(
      <BaseLineChart
        data={mockData}
        config={animatedConfig}
      />
    )

    // Animation props would be passed to the Line components
    const line = screen.getByTestId('line-value')
    expect(line).toBeInTheDocument()
  })
})
import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Button } from '../../ui/button'

describe('Button', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render button with text', () => {
    render(<Button>Click me</Button>)
    
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument()
  })

  it('should handle click events', async () => {
    const user = userEvent.setup()
    const mockOnClick = vi.fn()
    
    render(<Button onClick={mockOnClick}>Click me</Button>)
    
    const button = screen.getByRole('button', { name: 'Click me' })
    await user.click(button)
    
    expect(mockOnClick).toHaveBeenCalledTimes(1)
  })

  it('should render different variants', () => {
    const { rerender } = render(<Button variant="default">Default</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-primary')

    rerender(<Button variant="destructive">Destructive</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-destructive')

    rerender(<Button variant="outline">Outline</Button>)
    expect(screen.getByRole('button')).toHaveClass('border-input')

    rerender(<Button variant="secondary">Secondary</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-secondary')

    rerender(<Button variant="ghost">Ghost</Button>)
    expect(screen.getByRole('button')).toHaveClass('hover:bg-accent')

    rerender(<Button variant="link">Link</Button>)
    expect(screen.getByRole('button')).toHaveClass('text-primary')
  })

  it('should render different sizes', () => {
    const { rerender } = render(<Button size="default">Default</Button>)
    expect(screen.getByRole('button')).toHaveClass('h-10', 'px-4', 'py-2')

    rerender(<Button size="sm">Small</Button>)
    expect(screen.getByRole('button')).toHaveClass('h-9', 'px-3')

    rerender(<Button size="lg">Large</Button>)
    expect(screen.getByRole('button')).toHaveClass('h-11', 'px-8')

    rerender(<Button size="icon">Icon</Button>)
    expect(screen.getByRole('button')).toHaveClass('h-10', 'w-10')
  })

  it('should be disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled</Button>)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('disabled:pointer-events-none', 'disabled:opacity-50')
  })

  it('should not call onClick when disabled', async () => {
    const user = userEvent.setup()
    const mockOnClick = vi.fn()
    
    render(<Button disabled onClick={mockOnClick}>Disabled</Button>)
    
    const button = screen.getByRole('button')
    await user.click(button)
    
    expect(mockOnClick).not.toHaveBeenCalled()
  })

  it('should show loading state', () => {
    render(<Button loading>Loading</Button>)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('should hide text when loading and hideTextOnLoading is true', () => {
    render(<Button loading hideTextOnLoading>Loading</Button>)
    
    expect(screen.queryByText('Loading')).not.toBeInTheDocument()
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
  })

  it('should render with custom className', () => {
    render(<Button className="custom-class">Custom</Button>)
    
    const button = screen.getByRole('button')
    expect(button).toHaveClass('custom-class')
  })

  it('should render as different HTML elements', () => {
    render(<Button asChild><a href="/test">Link</a></Button>)
    
    expect(screen.getByRole('link')).toBeInTheDocument()
    expect(screen.getByRole('link')).toHaveAttribute('href', '/test')
  })

  it('should forward refs correctly', () => {
    const ref = vi.fn()
    
    render(<Button ref={ref}>Button</Button>)
    
    expect(ref).toHaveBeenCalledWith(expect.any(HTMLButtonElement))
  })

  it('should handle keyboard events', async () => {
    const user = userEvent.setup()
    const mockOnClick = vi.fn()
    
    render(<Button onClick={mockOnClick}>Button</Button>)
    
    const button = screen.getByRole('button')
    button.focus()
    
    await user.keyboard('{Enter}')
    expect(mockOnClick).toHaveBeenCalledTimes(1)
    
    await user.keyboard(' ')
    expect(mockOnClick).toHaveBeenCalledTimes(2)
  })

  it('should have proper focus styles', () => {
    render(<Button>Button</Button>)
    
    const button = screen.getByRole('button')
    expect(button).toHaveClass('focus-visible:outline-hidden', 'focus-visible:ring-2')
  })

  it('should render with icons', () => {
    const Icon = () => <span data-testid="icon">Icon</span>
    
    render(
      <Button>
        <Icon />
        Button with icon
      </Button>
    )
    
    expect(screen.getByTestId('icon')).toBeInTheDocument()
    expect(screen.getByText('Button with icon')).toBeInTheDocument()
  })

  it('should handle form submission', () => {
    const mockOnSubmit = vi.fn(e => e.preventDefault())
    
    render(
      <form onSubmit={mockOnSubmit}>
        <Button type="submit">Submit</Button>
      </form>
    )
    
    const button = screen.getByRole('button', { name: 'Submit' })
    fireEvent.click(button)
    
    expect(mockOnSubmit).toHaveBeenCalled()
  })

  it('should be accessible', () => {
    render(<Button aria-label="Custom label">Button</Button>)
    
    const button = screen.getByRole('button', { name: 'Custom label' })
    expect(button).toBeInTheDocument()
  })

  it('should support custom attributes', () => {
    render(<Button data-testid="custom-button" id="my-button">Button</Button>)
    
    const button = screen.getByTestId('custom-button')
    expect(button).toHaveAttribute('id', 'my-button')
  })

  it('should handle long text gracefully', () => {
    const longText = 'This is a very long button text that should be handled gracefully'
    
    render(<Button>{longText}</Button>)
    
    expect(screen.getByText(longText)).toBeInTheDocument()
  })

  it('should maintain aspect ratio for icon buttons', () => {
    render(<Button size="icon" aria-label="Icon button">🔍</Button>)
    
    const button = screen.getByRole('button', { name: 'Icon button' })
    expect(button).toHaveClass('h-10', 'w-10')
  })

  it('should handle rapid clicks', async () => {
    const user = userEvent.setup()
    const mockOnClick = vi.fn()
    
    render(<Button onClick={mockOnClick}>Button</Button>)
    
    const button = screen.getByRole('button')
    
    // Rapid clicks
    await user.click(button)
    await user.click(button)
    await user.click(button)
    
    expect(mockOnClick).toHaveBeenCalledTimes(3)
  })

  it('should work with React.forwardRef', () => {
    const CustomButton = React.forwardRef<HTMLButtonElement, React.ComponentProps<typeof Button>>(
      (props, ref) => <Button ref={ref} {...props} />
    )
    
    const ref = React.createRef<HTMLButtonElement>()
    
    render(<CustomButton ref={ref}>Custom Button</CustomButton>)
    
    expect(ref.current).toBeInstanceOf(HTMLButtonElement)
  })
})
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'jotai'
import { createStore } from 'jotai'
import { UserManagementTable } from '../../admin/UserManagementTable'
import type { User } from '../../../types/auth'

const mockUsers: User[] = [
  {
    id: 'user-1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'user',
    status: 'approved',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    apiAccessLevel: 'basic',
    lastLoginAt: new Date('2024-01-02')
  },
  {
    id: 'user-2',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'admin',
    status: 'pending',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    apiAccessLevel: 'premium'
  },
  {
    id: 'user-3',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'user',
    status: 'revoked',
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
    apiAccessLevel: 'none'
  }
]

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const store = createStore()
  return <Provider store={store}>{children}</Provider>
}

describe('UserManagementTable', () => {
  const mockOnApprove = vi.fn()
  const mockOnRevoke = vi.fn()
  const mockOnDelete = vi.fn()
  const mockOnViewDetails = vi.fn()
  const mockOnBulkAction = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render table with user data', () => {
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    // Check table headers
    expect(screen.getByText('Name')).toBeInTheDocument()
    expect(screen.getByText('Email')).toBeInTheDocument()
    expect(screen.getByText('Role')).toBeInTheDocument()
    expect(screen.getByText('Status')).toBeInTheDocument()
    expect(screen.getByText('API Access')).toBeInTheDocument()
    expect(screen.getByText('Created')).toBeInTheDocument()
    expect(screen.getByText('Last Login')).toBeInTheDocument()
    expect(screen.getByText('Actions')).toBeInTheDocument()

    // Check user data
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('Bob Johnson')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('should show correct status badges', () => {
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    const approvedBadge = screen.getByText('Approved')
    expect(approvedBadge).toHaveClass('bg-green-100', 'text-green-800')

    const pendingBadge = screen.getByText('Pending')
    expect(pendingBadge).toHaveClass('bg-yellow-100', 'text-yellow-800')

    const revokedBadge = screen.getByText('Revoked')
    expect(revokedBadge).toHaveClass('bg-red-100', 'text-red-800')
  })

  it('should show correct role badges', () => {
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    const userBadges = screen.getAllByText('User')
    expect(userBadges).toHaveLength(2)
    userBadges.forEach(badge => {
      expect(badge).toHaveClass('bg-blue-100', 'text-blue-800')
    })

    const adminBadge = screen.getByText('Admin')
    expect(adminBadge).toHaveClass('bg-purple-100', 'text-purple-800')
  })

  it('should show correct API access levels', () => {
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    expect(screen.getByText('Basic')).toBeInTheDocument()
    expect(screen.getByText('Premium')).toBeInTheDocument()
    expect(screen.getByText('None')).toBeInTheDocument()
  })

  it('should call onApprove when approve button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    const approveButtons = screen.getAllByRole('button', { name: /approve/i })
    await user.click(approveButtons[0]) // Click approve for pending user

    expect(mockOnApprove).toHaveBeenCalledWith('user-2')
  })

  it('should call onRevoke when revoke button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    const revokeButtons = screen.getAllByRole('button', { name: /revoke/i })
    await user.click(revokeButtons[0]) // Click revoke for approved user

    expect(mockOnRevoke).toHaveBeenCalledWith('user-1')
  })

  it('should call onDelete when delete button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    const deleteButtons = screen.getAllByRole('button', { name: /delete/i })
    await user.click(deleteButtons[0])

    expect(mockOnDelete).toHaveBeenCalledWith('user-1')
  })

  it('should call onViewDetails when view button is clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    const viewButtons = screen.getAllByRole('button', { name: /view details/i })
    await user.click(viewButtons[0])

    expect(mockOnViewDetails).toHaveBeenCalledWith(mockUsers[0])
  })

  it('should handle bulk selection', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    // Select all checkbox
    const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i })
    await user.click(selectAllCheckbox)

    // All individual checkboxes should be checked
    const individualCheckboxes = screen.getAllByRole('checkbox')
    individualCheckboxes.slice(1).forEach(checkbox => { // Skip the "select all" checkbox
      expect(checkbox).toBeChecked()
    })
  })

  it('should show bulk actions when users are selected', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    // Select first user
    const checkboxes = screen.getAllByRole('checkbox')
    await user.click(checkboxes[1]) // First user checkbox

    // Bulk actions should appear
    expect(screen.getByText(/1 user selected/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /bulk approve/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /bulk revoke/i })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /bulk delete/i })).toBeInTheDocument()
  })

  it('should call onBulkAction when bulk action is performed', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    // Select first two users
    const checkboxes = screen.getAllByRole('checkbox')
    await user.click(checkboxes[1])
    await user.click(checkboxes[2])

    // Perform bulk approve
    const bulkApproveButton = screen.getByRole('button', { name: /bulk approve/i })
    await user.click(bulkApproveButton)

    expect(mockOnBulkAction).toHaveBeenCalledWith('approve', ['user-1', 'user-2'])
  })

  it('should sort table columns when headers are clicked', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    const nameHeader = screen.getByRole('button', { name: /sort by name/i })
    await user.click(nameHeader)

    // Check that sort indicator is shown
    expect(screen.getByTestId('sort-asc-icon')).toBeInTheDocument()

    // Click again to reverse sort
    await user.click(nameHeader)
    expect(screen.getByTestId('sort-desc-icon')).toBeInTheDocument()
  })

  it('should show empty state when no users', () => {
    render(
      <TestWrapper>
        <UserManagementTable
          users={[]}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    expect(screen.getByText(/no users found/i)).toBeInTheDocument()
    expect(screen.getByText(/try adjusting your filters/i)).toBeInTheDocument()
  })

  it('should show loading state', () => {
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
          isLoading={true}
        />
      </TestWrapper>
    )

    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument()
  })

  it('should be accessible with proper ARIA labels', () => {
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    const table = screen.getByRole('table')
    expect(table).toHaveAttribute('aria-label', 'User management table')

    const selectAllCheckbox = screen.getByRole('checkbox', { name: /select all/i })
    expect(selectAllCheckbox).toBeInTheDocument()

    // Check that action buttons have proper labels
    const approveButtons = screen.getAllByRole('button', { name: /approve/i })
    approveButtons.forEach(button => {
      expect(button).toHaveAttribute('aria-label', expect.stringContaining('Approve'))
    })
  })

  it('should handle keyboard navigation', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    // Tab through interactive elements
    await user.tab()
    expect(screen.getByRole('checkbox', { name: /select all/i })).toHaveFocus()

    await user.tab()
    expect(screen.getAllByRole('checkbox')[1]).toHaveFocus() // First user checkbox
  })

  it('should format dates correctly', () => {
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    // Check that dates are formatted in a readable way
    expect(screen.getByText(/Jan 1, 2024/i)).toBeInTheDocument()
    expect(screen.getByText(/Jan 2, 2024/i)).toBeInTheDocument()
  })

  it('should show "Never" for users who have never logged in', () => {
    const usersWithNeverLogin = [
      { ...mockUsers[0], lastLoginAt: undefined }
    ]
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={usersWithNeverLogin}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
        />
      </TestWrapper>
    )

    expect(screen.getByText(/never/i)).toBeInTheDocument()
  })

  it('should disable actions for current user', () => {
    const currentUserId = 'user-1'
    
    render(
      <TestWrapper>
        <UserManagementTable
          users={mockUsers}
          onApprove={mockOnApprove}
          onRevoke={mockOnRevoke}
          onDelete={mockOnDelete}
          onViewDetails={mockOnViewDetails}
          onBulkAction={mockOnBulkAction}
          currentUserId={currentUserId}
        />
      </TestWrapper>
    )

    // Find the row for the current user and check that action buttons are disabled
    const userRow = screen.getByText('John Doe').closest('tr')
    const deleteButton = userRow?.querySelector('button[aria-label*="Delete"]')
    expect(deleteButton).toBeDisabled()
  })
})
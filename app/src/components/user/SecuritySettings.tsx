import React, { useState } from 'react'
import { use<PERSON>tom, useAtomValue } from 'jotai'
import {
    Shield,
    Key,
    Bell,
    Smartphone,
    Eye,
    EyeOff,
    Save,
    Loader2,
    <PERSON><PERSON><PERSON><PERSON>gle,
    CheckCircle
} from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Switch } from '../ui/switch'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { toast } from '../../hooks/useToast'
import { userAtom, changePassword<PERSON>tom, updateUserPreferencesAtom } from '../../stores/auth'

interface PasswordFormData {
    currentPassword: string
    newPassword: string
    confirmPassword: string
}

interface SecurityPreferences {
    emailNotifications: boolean
    securityAlerts: boolean
    loginNotifications: boolean
    apiKeyAlerts: boolean
    twoFactorEnabled: boolean
}

interface SecuritySettingsProps {
    className?: string
}

const SecuritySettings: React.FC<SecuritySettingsProps> = ({ className }) => {
    const user = useAtomValue(userAtom)
    const [, changePassword] = useAtom(changePasswordAtom)
    const [, updatePreferences] = useAtom(updateUserPreferencesAtom)

    const [isChangingPassword, setIsChangingPassword] = useState(false)
    const [isUpdatingPreferences, setIsUpdatingPreferences] = useState(false)
    const [showPasswords, setShowPasswords] = useState({
        current: false,
        new: false,
        confirm: false
    })

    const [passwordForm, setPasswordForm] = useState<PasswordFormData>({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
    })

    const [preferences, setPreferences] = useState<SecurityPreferences>({
        emailNotifications: user?.preferences?.emailNotifications ?? true,
        securityAlerts: user?.preferences?.securityAlerts ?? true,
        loginNotifications: user?.preferences?.loginNotifications ?? true,
        apiKeyAlerts: user?.preferences?.apiKeyAlerts ?? true,
        twoFactorEnabled: user?.preferences?.twoFactorEnabled ?? false
    })

    const handlePasswordChange = (field: keyof PasswordFormData, value: string) => {
        setPasswordForm(prev => ({ ...prev, [field]: value }))
    }

    const handlePreferenceChange = (field: keyof SecurityPreferences, value: boolean) => {
        setPreferences(prev => ({ ...prev, [field]: value }))
    }

    const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
        setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }))
    }

    const validatePassword = (password: string): string[] => {
        const errors: string[] = []

        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long')
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter')
        }
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter')
        }
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number')
        }
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
            errors.push('Password must contain at least one special character')
        }

        return errors
    }

    const handlePasswordSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!user) {
            toast({
                title: 'Authentication Error',
                description: 'You must be logged in to change your password',
                variant: 'destructive',
            })
            return
        }

        // Validate form
        if (!passwordForm.currentPassword) {
            toast({
                title: 'Validation Error',
                description: 'Current password is required',
                variant: 'destructive',
            })
            return
        }

        if (!passwordForm.newPassword) {
            toast({
                title: 'Validation Error',
                description: 'New password is required',
                variant: 'destructive',
            })
            return
        }

        if (passwordForm.newPassword !== passwordForm.confirmPassword) {
            toast({
                title: 'Validation Error',
                description: 'New passwords do not match',
                variant: 'destructive',
            })
            return
        }

        // Validate password strength
        const passwordErrors = validatePassword(passwordForm.newPassword)
        if (passwordErrors.length > 0) {
            toast({
                title: 'Password Requirements',
                description: passwordErrors[0],
                variant: 'destructive',
            })
            return
        }

        setIsChangingPassword(true)
        try {
            await changePassword({
                currentPassword: passwordForm.currentPassword,
                newPassword: passwordForm.newPassword
            })

            // Clear form
            setPasswordForm({
                currentPassword: '',
                newPassword: '',
                confirmPassword: ''
            })

            toast({
                title: 'Password Changed',
                description: 'Your password has been updated successfully',
            })
        } catch (error) {
            toast({
                title: 'Password Change Failed',
                description: error instanceof Error ? error.message : 'Failed to change password',
                variant: 'destructive',
            })
        } finally {
            setIsChangingPassword(false)
        }
    }

    const handlePreferencesSubmit = async () => {
        if (!user) {
            toast({
                title: 'Authentication Error',
                description: 'You must be logged in to update preferences',
                variant: 'destructive',
            })
            return
        }

        setIsUpdatingPreferences(true)
        try {
            await updatePreferences(preferences)
            toast({
                title: 'Preferences Updated',
                description: 'Your security preferences have been saved',
            })
        } catch (error) {
            toast({
                title: 'Update Failed',
                description: error instanceof Error ? error.message : 'Failed to update preferences',
                variant: 'destructive',
            })
        } finally {
            setIsUpdatingPreferences(false)
        }
    }

    const getPasswordStrength = (password: string): { score: number; label: string; color: string } => {
        const errors = validatePassword(password)
        const score = Math.max(0, 5 - errors.length)

        if (score === 0) return { score: 0, label: 'Very Weak', color: 'text-red-600' }
        if (score === 1) return { score: 20, label: 'Weak', color: 'text-red-500' }
        if (score === 2) return { score: 40, label: 'Fair', color: 'text-yellow-500' }
        if (score === 3) return { score: 60, label: 'Good', color: 'text-blue-500' }
        if (score === 4) return { score: 80, label: 'Strong', color: 'text-green-500' }
        return { score: 100, label: 'Very Strong', color: 'text-green-600' }
    }

    const passwordStrength = getPasswordStrength(passwordForm.newPassword)

    if (!user) {
        return (
            <Card className={className}>
                <CardContent className="p-8 text-center">
                    <p className="text-muted-foreground">Please log in to view security settings</p>
                </CardContent>
            </Card>
        )
    }

    return (
        <div className={className}>
            <div className="space-y-6">
                {/* Security Overview */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Shield className="h-5 w-5" />
                            Security Overview
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="flex items-center gap-3 p-3 border rounded-lg">
                                <CheckCircle className="h-5 w-5 text-green-500" />
                                <div>
                                    <p className="font-medium">Account Verified</p>
                                    <p className="text-sm text-muted-foreground">Email confirmed</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-3 p-3 border rounded-lg">
                                <Key className="h-5 w-5 text-blue-500" />
                                <div>
                                    <p className="font-medium">Strong Password</p>
                                    <p className="text-sm text-muted-foreground">Last changed recently</p>
                                </div>
                            </div>

                            <div className="flex items-center gap-3 p-3 border rounded-lg">
                                {preferences.twoFactorEnabled ? (
                                    <CheckCircle className="h-5 w-5 text-green-500" />
                                ) : (
                                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                                )}
                                <div>
                                    <p className="font-medium">Two-Factor Auth</p>
                                    <p className="text-sm text-muted-foreground">
                                        {preferences.twoFactorEnabled ? 'Enabled' : 'Disabled'}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Change Password */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Key className="h-5 w-5" />
                            Change Password
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handlePasswordSubmit} className="space-y-4">
                            <div>
                                <Label htmlFor="currentPassword">Current Password</Label>
                                <div className="relative">
                                    <Input
                                        id="currentPassword"
                                        type={showPasswords.current ? 'text' : 'password'}
                                        value={passwordForm.currentPassword}
                                        onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                                        placeholder="Enter your current password"
                                        required
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2"
                                        onClick={() => togglePasswordVisibility('current')}
                                    >
                                        {showPasswords.current ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </Button>
                                </div>
                            </div>

                            <div>
                                <Label htmlFor="newPassword">New Password</Label>
                                <div className="relative">
                                    <Input
                                        id="newPassword"
                                        type={showPasswords.new ? 'text' : 'password'}
                                        value={passwordForm.newPassword}
                                        onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                                        placeholder="Enter your new password"
                                        required
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2"
                                        onClick={() => togglePasswordVisibility('new')}
                                    >
                                        {showPasswords.new ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </Button>
                                </div>

                                {passwordForm.newPassword && (
                                    <div className="mt-2 space-y-2">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm">Password strength:</span>
                                            <Badge variant="outline" className={passwordStrength.color}>
                                                {passwordStrength.label}
                                            </Badge>
                                        </div>
                                        <div className="w-full bg-gray-200 rounded-full h-2">
                                            <div
                                                className="bg-current h-2 rounded-full transition-all duration-300"
                                                style={{
                                                    width: `${passwordStrength.score}%`,
                                                    color: passwordStrength.color.replace('text-', '')
                                                }}
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="confirmPassword">Confirm New Password</Label>
                                <div className="relative">
                                    <Input
                                        id="confirmPassword"
                                        type={showPasswords.confirm ? 'text' : 'password'}
                                        value={passwordForm.confirmPassword}
                                        onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                                        placeholder="Confirm your new password"
                                        required
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-2 top-1/2 transform -translate-y-1/2"
                                        onClick={() => togglePasswordVisibility('confirm')}
                                    >
                                        {showPasswords.confirm ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </Button>
                                </div>
                            </div>

                            <Button type="submit" disabled={isChangingPassword}>
                                {isChangingPassword ? (
                                    <>
                                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                        Changing Password...
                                    </>
                                ) : (
                                    <>
                                        <Save className="h-4 w-4 mr-2" />
                                        Change Password
                                    </>
                                )}
                            </Button>
                        </form>
                    </CardContent>
                </Card>

                {/* Security Preferences */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Bell className="h-5 w-5" />
                            Security Preferences
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Email Notifications</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Receive general email notifications
                                    </p>
                                </div>
                                <Switch
                                    checked={preferences.emailNotifications}
                                    onCheckedChange={(checked) => handlePreferenceChange('emailNotifications', checked)}
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Security Alerts</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Get notified about security-related events
                                    </p>
                                </div>
                                <Switch
                                    checked={preferences.securityAlerts}
                                    onCheckedChange={(checked) => handlePreferenceChange('securityAlerts', checked)}
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>Login Notifications</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Receive alerts for new login attempts
                                    </p>
                                </div>
                                <Switch
                                    checked={preferences.loginNotifications}
                                    onCheckedChange={(checked) => handlePreferenceChange('loginNotifications', checked)}
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <Label>API Key Alerts</Label>
                                    <p className="text-sm text-muted-foreground">
                                        Get notified about API key usage and changes
                                    </p>
                                </div>
                                <Switch
                                    checked={preferences.apiKeyAlerts}
                                    onCheckedChange={(checked) => handlePreferenceChange('apiKeyAlerts', checked)}
                                />
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <Label className="flex items-center gap-2">
                                        <Smartphone className="h-4 w-4" />
                                        Two-Factor Authentication
                                    </Label>
                                    <p className="text-sm text-muted-foreground">
                                        Add an extra layer of security to your account
                                    </p>
                                </div>
                                <Switch
                                    checked={preferences.twoFactorEnabled}
                                    onCheckedChange={(checked) => handlePreferenceChange('twoFactorEnabled', checked)}
                                />
                            </div>
                        </div>

                        <div className="flex justify-end pt-4 border-t">
                            <Button onClick={handlePreferencesSubmit} disabled={isUpdatingPreferences}>
                                {isUpdatingPreferences ? (
                                    <>
                                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                        Updating...
                                    </>
                                ) : (
                                    <>
                                        <Save className="h-4 w-4 mr-2" />
                                        Save Preferences
                                    </>
                                )}
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}

export default SecuritySettings
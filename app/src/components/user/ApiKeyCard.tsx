import React, { useState } from 'react'
import { useAtom } from 'jotai'
import { 
  MoreHorizontal, 
  Copy, 
  Eye, 
  RotateCcw, 
  Trash2, 
  Calendar,
  Activity,
  Shield,
  AlertTriangle
} from 'lucide-react'
import { Card, CardContent, CardHeader } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'
import { toast } from '../../hooks/useToast'
import { 
  regenerateApiKeyAtom, 
  deleteApiKeyAtom,
  selectApiKeyWithUsageAtom 
} from '../../stores/apiKeys'
import type { ApiKey } from '../../types/apiKey'
import ApiKeyDetailsModal from './ApiKeyDetailsModal'

interface ApiKeyCardProps {
  apiKey: ApiKey
  className?: string
}

const ApiKeyCard: React.FC<ApiKeyCardProps> = ({ apiKey, className }) => {
  const [showDetails, setShowDetails] = useState(false)
  const [showKey, setShowKey] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  
  const [, regenerateKey] = useAtom(regenerateApiKeyAtom.optimisticActionAtom)
  const [, deleteKey] = useAtom(deleteApiKeyAtom.optimisticActionAtom)
  const [, selectKeyWithUsage] = useAtom(selectApiKeyWithUsageAtom)

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: 'Copied!',
        description: 'API key copied to clipboard',
      })
    } catch (error) {
      toast({
        title: 'Failed to copy',
        description: 'Could not copy API key to clipboard',
        variant: 'destructive',
      })
    }
  }

  const handleRegenerate = async () => {
    if (!confirm('Are you sure you want to regenerate this API key? The old key will stop working immediately.')) {
      return
    }

    setIsLoading(true)
    try {
      await regenerateKey(apiKey.id)
      toast({
        title: 'API Key Regenerated',
        description: 'Your API key has been regenerated successfully',
      })
    } catch (error) {
      toast({
        title: 'Failed to regenerate',
        description: 'Could not regenerate API key',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this API key? This action cannot be undone.')) {
      return
    }

    setIsLoading(true)
    try {
      await deleteKey(apiKey.id)
      toast({
        title: 'API Key Deleted',
        description: 'Your API key has been deleted successfully',
      })
    } catch (error) {
      toast({
        title: 'Failed to delete',
        description: 'Could not delete API key',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleViewDetails = () => {
    selectKeyWithUsage(apiKey)
    setShowDetails(true)
  }

  const isExpiringSoon = apiKey.expiresAt && 
    new Date(apiKey.expiresAt) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)

  const isExpired = apiKey.expiresAt && new Date(apiKey.expiresAt) <= new Date()

  const maskedKey = apiKey.keyHash ? `${apiKey.keyHash.slice(0, 8)}...${apiKey.keyHash.slice(-4)}` : 'Hidden'
  const displayKey = showKey ? apiKey.keyHash : maskedKey

  return (
    <>
      <Card className={className}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <h3 className="font-semibold">{apiKey.name}</h3>
                <Badge 
                  variant={apiKey.status === 'active' ? 'default' : 'destructive'}
                  className={
                    apiKey.status === 'active' 
                      ? 'bg-green-100 text-green-800 hover:bg-green-100' 
                      : ''
                  }
                >
                  {apiKey.status}
                </Badge>
                {isExpired && (
                  <Badge variant="destructive">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Expired
                  </Badge>
                )}
                {isExpiringSoon && !isExpired && (
                  <Badge variant="outline" className="border-yellow-500 text-yellow-700">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Expiring Soon
                  </Badge>
                )}
              </div>
              {apiKey.description && (
                <p className="text-sm text-muted-foreground">{apiKey.description}</p>
              )}
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" disabled={isLoading}>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleViewDetails}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => copyToClipboard(maskedKey)}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Key
                </DropdownMenuItem>
                {apiKey.status === 'active' && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleRegenerate}>
                      <RotateCcw className="h-4 w-4 mr-2" />
                      Regenerate
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={handleDelete}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-4">
            {/* API Key Display */}
            <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
              <code className="flex-1 text-sm font-mono">
                {showKey ? apiKey.keyHash : maskedKey}
              </code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowKey(!showKey)}
              >
                <Eye className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(apiKey.keyHash)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>

            {/* Metadata */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-muted-foreground">Created</p>
                  <p className="font-medium">{formatDate(apiKey.createdAt)}</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-muted-foreground">Last Used</p>
                  <p className="font-medium">
                    {apiKey.lastUsedAt ? formatDate(apiKey.lastUsedAt) : 'Never'}
                  </p>
                </div>
              </div>
              
              {apiKey.expiresAt && (
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-muted-foreground">Expires</p>
                    <p className="font-medium">{formatDate(apiKey.expiresAt)}</p>
                  </div>
                </div>
              )}
              
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-muted-foreground" />
                <div>
                  <p className="text-muted-foreground">Permissions</p>
                  <p className="font-medium">{apiKey.permissions.length} resources</p>
                </div>
              </div>
            </div>

            {/* Rate Limits */}
            <div className="border-t pt-3">
              <p className="text-sm font-medium mb-2">Rate Limits</p>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center p-2 bg-muted rounded">
                  <p className="font-medium">{apiKey.rateLimit.requestsPerMinute}</p>
                  <p className="text-muted-foreground">per minute</p>
                </div>
                <div className="text-center p-2 bg-muted rounded">
                  <p className="font-medium">{apiKey.rateLimit.requestsPerHour}</p>
                  <p className="text-muted-foreground">per hour</p>
                </div>
                <div className="text-center p-2 bg-muted rounded">
                  <p className="font-medium">{apiKey.rateLimit.requestsPerDay}</p>
                  <p className="text-muted-foreground">per day</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Details Modal */}
      <ApiKeyDetailsModal
        open={showDetails}
        onOpenChange={setShowDetails}
        apiKey={apiKey}
      />
    </>
  )
}

export default ApiKeyCard
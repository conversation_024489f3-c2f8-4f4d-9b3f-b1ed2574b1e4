import React, { useState, useEffect } from 'react'
import { useAtomValue } from 'jotai'
import { 
  X, 
  Calendar, 
  Activity, 
  Shield, 
  Zap, 
  TrendingUp,
  AlertTriangle,
  Copy,
  BarChart3
} from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Progress } from '../ui/progress'
import { toast } from '../../hooks/useToast'
import { apiKeyUsageAtom } from '../../stores/apiKeys'
import type { ApiKey } from '../../types/apiKey'

interface ApiKeyDetailsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  apiKey: <PERSON>pi<PERSON>ey
}

const ApiKeyDetailsModal: React.FC<ApiKeyDetailsModalProps> = ({ 
  open, 
  onOpenChange, 
  apiKey 
}) => {
  const [activeTab, setActiveTab] = useState('overview')
  const usage = useAtomValue(apiKeyUsageAtom)[apiKey.id]

  const formatDate = (date: Date | string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: 'Copied!',
        description: 'API key copied to clipboard',
      })
    } catch (error) {
      toast({
        title: 'Failed to copy',
        description: 'Could not copy API key to clipboard',
        variant: 'destructive',
      })
    }
  }

  const isExpiringSoon = apiKey.expiresAt && 
    new Date(apiKey.expiresAt) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)

  const isExpired = apiKey.expiresAt && new Date(apiKey.expiresAt) <= new Date()

  const successRate = usage ? 
    (usage.successfulRequests / usage.totalRequests) * 100 : 0

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Shield className="h-5 w-5" />
              <div>
                <span>{apiKey.name}</span>
                <div className="flex items-center gap-2 mt-1">
                  <Badge 
                    variant={apiKey.status === 'active' ? 'default' : 'destructive'}
                    className={
                      apiKey.status === 'active' 
                        ? 'bg-green-100 text-green-800 hover:bg-green-100' 
                        : ''
                    }
                  >
                    {apiKey.status}
                  </Badge>
                  {isExpired && (
                    <Badge variant="destructive">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Expired
                    </Badge>
                  )}
                  {isExpiringSoon && !isExpired && (
                    <Badge variant="outline" className="border-yellow-500 text-yellow-700">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Expiring Soon
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="permissions">Permissions</TabsTrigger>
            <TabsTrigger value="usage">Usage</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Key Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">API Key Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {apiKey.description && (
                  <div>
                    <p className="text-sm font-medium">Description</p>
                    <p className="text-sm text-muted-foreground">{apiKey.description}</p>
                  </div>
                )}

                <div className="flex items-center gap-2 p-3 bg-muted rounded-md">
                  <code className="flex-1 text-sm font-mono break-all">
                    {apiKey.keyHash}
                  </code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(apiKey.keyHash)}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Created</p>
                      <p className="text-sm text-muted-foreground">{formatDate(apiKey.createdAt)}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Last Used</p>
                      <p className="text-sm text-muted-foreground">
                        {apiKey.lastUsedAt ? formatDate(apiKey.lastUsedAt) : 'Never'}
                      </p>
                    </div>
                  </div>
                  
                  {apiKey.expiresAt && (
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Expires</p>
                        <p className="text-sm text-muted-foreground">{formatDate(apiKey.expiresAt)}</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Usage Summary */}
            {usage && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Usage Summary (Last 30 Days)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold">{usage.totalRequests.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">Total Requests</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">{successRate.toFixed(1)}%</p>
                      <p className="text-sm text-muted-foreground">Success Rate</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{usage.averageResponseTime.toFixed(0)}ms</p>
                      <p className="text-sm text-muted-foreground">Avg Response</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{formatBytes(usage.dataTransferred)}</p>
                      <p className="text-sm text-muted-foreground">Data Transfer</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="permissions" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Resource Permissions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {apiKey.permissions.map((permission, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{permission.resource}</h4>
                      <Badge variant="outline">{permission.actions.length} actions</Badge>
                    </div>
                    <div className="flex gap-2">
                      {permission.actions.map((action) => (
                        <Badge key={action} variant="secondary">
                          {action}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="usage" className="space-y-6">
            {usage ? (
              <>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Request Statistics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Successful Requests</span>
                        <span className="text-sm font-medium">{usage.successfulRequests.toLocaleString()}</span>
                      </div>
                      <Progress value={successRate} className="h-2" />
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Failed Requests</span>
                        <span className="text-sm font-medium text-red-600">{usage.failedRequests.toLocaleString()}</span>
                      </div>
                      <Progress value={(usage.failedRequests / usage.totalRequests) * 100} className="h-2" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Top Endpoints</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {usage.topEndpoints.map((endpoint, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <p className="font-medium">{endpoint.endpoint}</p>
                            <p className="text-sm text-muted-foreground">
                              Avg: {endpoint.averageResponseTime.toFixed(0)}ms
                            </p>
                          </div>
                          <Badge variant="outline">{endpoint.count.toLocaleString()} calls</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="flex flex-col items-center gap-4">
                    <BarChart3 className="h-12 w-12 text-muted-foreground" />
                    <div>
                      <h3 className="text-lg font-semibold">No Usage Data</h3>
                      <p className="text-muted-foreground">
                        This API key hasn't been used yet or usage data is still loading.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Rate Limits
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-4 border rounded-lg">
                    <p className="text-2xl font-bold">{apiKey.rateLimit.requestsPerMinute}</p>
                    <p className="text-sm text-muted-foreground">per minute</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <p className="text-2xl font-bold">{apiKey.rateLimit.requestsPerHour}</p>
                    <p className="text-sm text-muted-foreground">per hour</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <p className="text-2xl font-bold">{apiKey.rateLimit.requestsPerDay}</p>
                    <p className="text-sm text-muted-foreground">per day</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Security Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-2">
                    <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-blue-800">Security Best Practices</p>
                      <ul className="text-blue-700 mt-2 space-y-1">
                        <li>• Keep your API key secure and never share it publicly</li>
                        <li>• Rotate your keys regularly for better security</li>
                        <li>• Use the minimum required permissions for your use case</li>
                        <li>• Monitor usage patterns for any suspicious activity</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ApiKeyDetailsModal
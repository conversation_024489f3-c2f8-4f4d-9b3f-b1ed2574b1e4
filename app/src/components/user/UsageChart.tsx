import React, { useMemo } from 'react'
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'

interface ChartDataPoint {
  timestamp: string
  date: string
  requests: number
  successfulRequests: number
  failedRequests: number
  responseTime: number
  dataTransferred: number
}

interface EndpointUsage {
  endpoint: string
  count: number
  percentage: number
  averageResponseTime: number
}

interface UsageChartProps {
  data: ChartDataPoint[]
  endpointData?: EndpointUsage[]
  type: 'line' | 'area' | 'bar' | 'pie'
  metric: 'requests' | 'responseTime' | 'dataTransfer' | 'endpoints'
  title: string
  className?: string
  height?: number
}

const COLORS = [
  '#3b82f6', // blue
  '#10b981', // green
  '#f59e0b', // yellow
  '#ef4444', // red
  '#8b5cf6', // purple
  '#06b6d4', // cyan
  '#f97316', // orange
  '#84cc16', // lime
]

const UsageChart: React.FC<UsageChartProps> = ({
  data,
  endpointData,
  type,
  metric,
  title,
  className,
  height = 300
}) => {
  const chartData = useMemo(() => {
    if (metric === 'endpoints' && endpointData) {
      return endpointData.map((item, index) => ({
        ...item,
        color: COLORS[index % COLORS.length]
      }))
    }
    return data
  }, [data, endpointData, metric])

  const formatValue = (value: number, metric: string) => {
    switch (metric) {
      case 'requests':
        return value.toLocaleString()
      case 'responseTime':
        return `${value.toFixed(0)}ms`
      case 'dataTransfer':
        if (value >= 1024 * 1024) {
          return `${(value / (1024 * 1024)).toFixed(1)}MB`
        } else if (value >= 1024) {
          return `${(value / 1024).toFixed(1)}KB`
        }
        return `${value}B`
      default:
        return value.toString()
    }
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {formatValue(entry.value, metric)}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  const renderChart = () => {
    switch (type) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="date" 
                className="text-xs"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                className="text-xs"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => formatValue(value, metric)}
              />
              <Tooltip content={<CustomTooltip />} />
              {metric === 'requests' && (
                <>
                  <Line 
                    type="monotone" 
                    dataKey="requests" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    name="Total Requests"
                    dot={{ r: 4 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="successfulRequests" 
                    stroke="#10b981" 
                    strokeWidth={2}
                    name="Successful"
                    dot={{ r: 4 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="failedRequests" 
                    stroke="#ef4444" 
                    strokeWidth={2}
                    name="Failed"
                    dot={{ r: 4 }}
                  />
                </>
              )}
              {metric === 'responseTime' && (
                <Line 
                  type="monotone" 
                  dataKey="responseTime" 
                  stroke="#f59e0b" 
                  strokeWidth={2}
                  name="Response Time"
                  dot={{ r: 4 }}
                />
              )}
              {metric === 'dataTransfer' && (
                <Line 
                  type="monotone" 
                  dataKey="dataTransferred" 
                  stroke="#8b5cf6" 
                  strokeWidth={2}
                  name="Data Transferred"
                  dot={{ r: 4 }}
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        )

      case 'area':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="date" 
                className="text-xs"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                className="text-xs"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => formatValue(value, metric)}
              />
              <Tooltip content={<CustomTooltip />} />
              {metric === 'requests' && (
                <>
                  <Area 
                    type="monotone" 
                    dataKey="requests" 
                    stackId="1"
                    stroke="#3b82f6" 
                    fill="#3b82f6"
                    fillOpacity={0.6}
                    name="Total Requests"
                  />
                </>
              )}
              {metric === 'responseTime' && (
                <Area 
                  type="monotone" 
                  dataKey="responseTime" 
                  stroke="#f59e0b" 
                  fill="#f59e0b"
                  fillOpacity={0.6}
                  name="Response Time"
                />
              )}
            </AreaChart>
          </ResponsiveContainer>
        )

      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="date" 
                className="text-xs"
                tick={{ fontSize: 12 }}
              />
              <YAxis 
                className="text-xs"
                tick={{ fontSize: 12 }}
                tickFormatter={(value) => formatValue(value, metric)}
              />
              <Tooltip content={<CustomTooltip />} />
              {metric === 'requests' && (
                <>
                  <Bar dataKey="successfulRequests" fill="#10b981" name="Successful" />
                  <Bar dataKey="failedRequests" fill="#ef4444" name="Failed" />
                </>
              )}
              {metric === 'responseTime' && (
                <Bar dataKey="responseTime" fill="#f59e0b" name="Response Time" />
              )}
            </BarChart>
          </ResponsiveContainer>
        )

      case 'pie':
        if (metric === 'endpoints' && endpointData) {
          return (
            <div className="flex items-center gap-6">
              <ResponsiveContainer width="60%" height={height}>
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="count"
                    label={({ percentage }) => `${percentage.toFixed(1)}%`}
                  >
                    {chartData.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip 
                    formatter={(value: number) => [value.toLocaleString(), 'Requests']}
                  />
                </PieChart>
              </ResponsiveContainer>
              
              <div className="flex-1 space-y-2">
                {chartData.map((entry: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-muted">
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: entry.color }}
                      />
                      <span className="text-sm font-medium truncate max-w-[150px]">
                        {entry.endpoint}
                      </span>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline">{entry.count.toLocaleString()}</Badge>
                      <p className="text-xs text-muted-foreground mt-1">
                        {entry.averageResponseTime.toFixed(0)}ms avg
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        }
        return null

      default:
        return null
    }
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        {renderChart()}
      </CardContent>
    </Card>
  )
}

export default UsageChart
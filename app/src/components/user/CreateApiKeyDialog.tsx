import React, { useState } from 'react'
import { use<PERSON>tom, useAtomValue } from 'jotai'
import { Plus, X, Calendar, Shield, Zap } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Textarea } from '../ui/textarea'
import { Switch } from '../ui/switch'
import { Badge } from '../ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { toast } from '../../hooks/useToast'
import { createApiKeyAtom } from '../../stores/apiKeys'
import { userAtom } from '../../stores/auth'
import type { CreateApiKeyRequest, ApiPermission } from '../../types/apiKey'

interface CreateApiKeyDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const AVAILABLE_RESOURCES = [
  { id: 'ollama.chat', name: 'Chat <PERSON>tions', description: 'Generate chat responses' },
  { id: 'ollama.embeddings', name: 'Embeddings', description: 'Generate text embeddings' },
  { id: 'ollama.models', name: 'Model Management', description: 'List and manage models' },
  { id: 'ollama.generate', name: 'Text Generation', description: 'Generate text completions' },
]

const AVAILABLE_ACTIONS = ['read', 'write', 'delete']

const CreateApiKeyDialog: React.FC<CreateApiKeyDialogProps> = ({ 
  open, 
  onOpenChange 
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [createdKey, setCreatedKey] = useState<string | null>(null)
  const [formData, setFormData] = useState<CreateApiKeyRequest>({
    name: '',
    description: '',
    permissions: [],
    rateLimit: {
      requestsPerMinute: 60,
      requestsPerHour: 1000,
      requestsPerDay: 10000
    }
  })
  const [hasExpiry, setHasExpiry] = useState(false)
  const [expiryDays, setExpiryDays] = useState(30)

  const user = useAtomValue(userAtom)
  const [, createKey] = useAtom(createApiKeyAtom.optimisticActionAtom)

  const handleResourceToggle = (resource: string) => {
    const existingPermission = formData.permissions.find(p => p.resource === resource)
    
    if (existingPermission) {
      // Remove permission
      setFormData(prev => ({
        ...prev,
        permissions: prev.permissions.filter(p => p.resource !== resource)
      }))
    } else {
      // Add permission with default read action
      setFormData(prev => ({
        ...prev,
        permissions: [...prev.permissions, { resource, actions: ['read'] }]
      }))
    }
  }

  const handleActionToggle = (resource: string, action: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.map(p => {
        if (p.resource === resource) {
          const hasAction = p.actions.includes(action)
          return {
            ...p,
            actions: hasAction 
              ? p.actions.filter(a => a !== action)
              : [...p.actions, action]
          }
        }
        return p
      })
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) {
      toast({
        title: 'Authentication Error',
        description: 'You must be logged in to create API keys',
        variant: 'destructive',
      })
      return
    }

    if (!formData.name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'API key name is required',
        variant: 'destructive',
      })
      return
    }

    if (formData.permissions.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'At least one permission is required',
        variant: 'destructive',
      })
      return
    }

    setIsLoading(true)
    try {
      const keyData: CreateApiKeyRequest = {
        ...formData
      }
      
      if (hasExpiry) {
        keyData.expiresAt = new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000)
      }

      const newKey = await createKey(keyData, user.id)
      setCreatedKey(newKey.key || newKey.keyHash)
      
      toast({
        title: 'API Key Created',
        description: 'Your new API key has been created successfully',
      })
    } catch (error) {
      toast({
        title: 'Failed to create API key',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (createdKey) {
      // Reset form when closing after successful creation
      setFormData({
        name: '',
        description: '',
        permissions: [],
        rateLimit: {
          requestsPerMinute: 60,
          requestsPerHour: 1000,
          requestsPerDay: 10000
        }
      })
      setCreatedKey(null)
      setHasExpiry(false)
      setExpiryDays(30)
    }
    onOpenChange(false)
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: 'Copied!',
        description: 'API key copied to clipboard',
      })
    } catch (error) {
      toast({
        title: 'Failed to copy',
        description: 'Could not copy API key to clipboard',
        variant: 'destructive',
      })
    }
  }

  if (createdKey) {
    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-600" />
              API Key Created
            </DialogTitle>
            <DialogDescription>
              Your API key has been created successfully. Make sure to copy it now as you won't be able to see it again.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 bg-muted rounded-lg">
              <Label className="text-sm font-medium">Your API Key</Label>
              <div className="flex items-center gap-2 mt-2">
                <code className="flex-1 p-2 bg-background rounded text-sm font-mono break-all">
                  {createdKey}
                </code>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(createdKey)}
                >
                  Copy
                </Button>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <div className="h-5 w-5 text-yellow-600 mt-0.5">⚠️</div>
                <div className="text-sm">
                  <p className="font-medium text-yellow-800">Important Security Notice</p>
                  <p className="text-yellow-700 mt-1">
                    This is the only time you'll see this API key. Store it securely and don't share it with anyone.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end">
              <Button onClick={handleClose}>
                Done
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Create New API Key
          </DialogTitle>
          <DialogDescription>
            Create a new API key to access Ollama services. Configure permissions and rate limits as needed.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Production API Key"
                required
              />
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description for this API key"
                rows={2}
              />
            </div>
          </div>

          {/* Expiry Settings */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Expiry Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Set Expiry Date</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically revoke this key after a specified time
                  </p>
                </div>
                <Switch
                  checked={hasExpiry}
                  onCheckedChange={setHasExpiry}
                />
              </div>

              {hasExpiry && (
                <div>
                  <Label htmlFor="expiryDays">Expires in (days)</Label>
                  <Input
                    id="expiryDays"
                    type="number"
                    min="1"
                    max="365"
                    value={expiryDays}
                    onChange={(e) => setExpiryDays(parseInt(e.target.value) || 30)}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Permissions */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Permissions *
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {AVAILABLE_RESOURCES.map((resource) => {
                const permission = formData.permissions.find(p => p.resource === resource.id)
                const isSelected = !!permission

                return (
                  <div key={resource.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={isSelected}
                            onCheckedChange={() => handleResourceToggle(resource.id)}
                          />
                          <Label className="font-medium">{resource.name}</Label>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {resource.description}
                        </p>
                      </div>
                    </div>

                    {isSelected && (
                      <div className="ml-6 space-y-2">
                        <Label className="text-sm">Actions</Label>
                        <div className="flex gap-2">
                          {AVAILABLE_ACTIONS.map((action) => (
                            <Badge
                              key={action}
                              variant={permission?.actions.includes(action) ? 'default' : 'outline-solid'}
                              className="cursor-pointer"
                              onClick={() => handleActionToggle(resource.id, action)}
                            >
                              {action}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )
              })}
            </CardContent>
          </Card>

          {/* Rate Limits */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Rate Limits
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="perMinute">Per Minute</Label>
                  <Input
                    id="perMinute"
                    type="number"
                    min="1"
                    value={formData.rateLimit?.requestsPerMinute || 60}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      rateLimit: {
                        ...prev.rateLimit!,
                        requestsPerMinute: parseInt(e.target.value) || 60
                      }
                    }))}
                  />
                </div>

                <div>
                  <Label htmlFor="perHour">Per Hour</Label>
                  <Input
                    id="perHour"
                    type="number"
                    min="1"
                    value={formData.rateLimit?.requestsPerHour || 1000}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      rateLimit: {
                        ...prev.rateLimit!,
                        requestsPerHour: parseInt(e.target.value) || 1000
                      }
                    }))}
                  />
                </div>

                <div>
                  <Label htmlFor="perDay">Per Day</Label>
                  <Input
                    id="perDay"
                    type="number"
                    min="1"
                    value={formData.rateLimit?.requestsPerDay || 10000}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      rateLimit: {
                        ...prev.rateLimit!,
                        requestsPerDay: parseInt(e.target.value) || 10000
                      }
                    }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creating...' : 'Create API Key'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default CreateApiKeyDialog
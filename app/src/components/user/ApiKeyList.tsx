import React, { useState } from 'react'
import { useAtom, useAtomValue } from 'jotai'
import { Plus, Search } from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent } from '../ui/card'
import { Badge } from '../ui/badge'
import { 
  userApiKeysAtom, 
  apiKeyFiltersAtom, 
  updateApiKeyFiltersAtom,
  apiKeyStatsSummaryAtom 
} from '../../stores/apiKeys'
import ApiKeyCard from './ApiKeyCard'
import CreateApiKeyDialog from './CreateApiKeyDialog'

interface ApiKeyListProps {
  className?: string
}

const ApiKeyList: React.FC<ApiKeyListProps> = ({ className }) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  
  const apiKeys = useAtomValue(userApiKeysAtom)
  const filters = useAtomValue(apiKeyFiltersAtom)
  const stats = useAtomValue(apiKeyStatsSummaryAtom)
  const [, updateFilters] = useAtom(updateApiKeyFiltersAtom)

  const handleSearchChange = (value: string) => {
    updateFilters({ search: value })
  }

  const handleStatusFilter = (status: 'all' | 'active' | 'revoked') => {
    updateFilters({ status })
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold">API Keys</h2>
          <p className="text-muted-foreground">
            Manage your API keys for accessing Ollama services
          </p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create API Key
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Keys</p>
                <p className="text-2xl font-bold">{stats?.totalKeys || 0}</p>
              </div>
              <Badge variant="secondary">{stats?.totalKeys || 0}</Badge>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active</p>
                <p className="text-2xl font-bold text-green-600">{stats?.activeKeys || 0}</p>
              </div>
              <Badge variant="default" className="bg-green-100 text-green-800">
                {stats?.activeKeys || 0}
              </Badge>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Revoked</p>
                <p className="text-2xl font-bold text-red-600">{stats?.revokedKeys || 0}</p>
              </div>
              <Badge variant="destructive">{stats?.revokedKeys || 0}</Badge>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Expiring Soon</p>
                <p className="text-2xl font-bold text-yellow-600">{stats?.expiringSoon || 0}</p>
              </div>
              <Badge variant="outline" className="border-yellow-500 text-yellow-700">
                {stats?.expiringSoon || 0}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4 mb-6">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search API keys..."
            value={filters.search}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={filters.status === 'all' ? 'default' : 'outline-solid'}
            size="sm"
            onClick={() => handleStatusFilter('all')}
          >
            All
          </Button>
          <Button
            variant={filters.status === 'active' ? 'default' : 'outline-solid'}
            size="sm"
            onClick={() => handleStatusFilter('active')}
          >
            Active
          </Button>
          <Button
            variant={filters.status === 'revoked' ? 'default' : 'outline-solid'}
            size="sm"
            onClick={() => handleStatusFilter('revoked')}
          >
            Revoked
          </Button>
        </div>
      </div>

      {/* API Keys List */}
      {apiKeys.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="flex flex-col items-center gap-4">
              <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center">
                <Plus className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">No API Keys</h3>
                <p className="text-muted-foreground">
                  Create your first API key to start using Ollama services
                </p>
              </div>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create API Key
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {apiKeys.map((apiKey) => (
            <ApiKeyCard key={apiKey.id} apiKey={apiKey} />
          ))}
        </div>
      )}

      {/* Create API Key Dialog */}
      <CreateApiKeyDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </div>
  )
}

export default ApiKeyList
import React from 'react'
import { TrendingUp, TrendingDown, Activity, Clock, Database, AlertCircle } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'

interface UsageMetric {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon?: React.ReactNode
  description?: string
  progress?: {
    value: number
    max: number
    label: string
  }
}

interface UsageMetricsCardProps {
  metrics: UsageMetric[]
  title: string
  className?: string
}

const UsageMetricsCard: React.FC<UsageMetricsCardProps> = ({ 
  metrics, 
  title, 
  className 
}) => {
  const formatValue = (value: string | number) => {
    if (typeof value === 'number') {
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K`
      }
      return value.toLocaleString()
    }
    return value
  }

  const getChangeIcon = (type: 'increase' | 'decrease') => {
    return type === 'increase' ? (
      <TrendingUp className="h-3 w-3" />
    ) : (
      <TrendingDown className="h-3 w-3" />
    )
  }

  const getChangeColor = (type: 'increase' | 'decrease') => {
    return type === 'increase' ? 'text-green-600' : 'text-red-600'
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {metrics.map((metric, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {metric.icon}
                  <span className="text-sm font-medium">{metric.title}</span>
                </div>
                {metric.change && (
                  <Badge 
                    variant="outline" 
                    className={`${getChangeColor(metric.change.type)} border-current`}
                  >
                    {getChangeIcon(metric.change.type)}
                    <span className="ml-1">
                      {metric.change.value > 0 ? '+' : ''}{metric.change.value}%
                    </span>
                  </Badge>
                )}
              </div>
              
              <div className="space-y-1">
                <p className="text-2xl font-bold">{formatValue(metric.value)}</p>
                {metric.description && (
                  <p className="text-xs text-muted-foreground">{metric.description}</p>
                )}
                {metric.change && (
                  <p className="text-xs text-muted-foreground">
                    vs {metric.change.period}
                  </p>
                )}
              </div>

              {metric.progress && (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>{metric.progress.label}</span>
                    <span>{metric.progress.value} / {metric.progress.max}</span>
                  </div>
                  <Progress 
                    value={(metric.progress.value / metric.progress.max) * 100} 
                    className="h-2"
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

// Predefined metric configurations
export const createRequestMetrics = (data: {
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  previousPeriodData?: {
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    averageResponseTime: number
  }
}): UsageMetric[] => {
  const successRate = data.totalRequests > 0 ? (data.successfulRequests / data.totalRequests) * 100 : 0
  const errorRate = data.totalRequests > 0 ? (data.failedRequests / data.totalRequests) * 100 : 0

  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return null
    const change = ((current - previous) / previous) * 100
    return {
      value: Math.round(change),
      type: change >= 0 ? 'increase' as const : 'decrease' as const,
      period: 'last period'
    }
  }

  return [
    {
      title: 'Total Requests',
      value: data.totalRequests,
      icon: <Activity className="h-4 w-4 text-blue-500" />,
      change: data.previousPeriodData ? 
        calculateChange(data.totalRequests, data.previousPeriodData.totalRequests) : undefined,
      description: 'API calls made'
    },
    {
      title: 'Success Rate',
      value: `${successRate.toFixed(1)}%`,
      icon: <TrendingUp className="h-4 w-4 text-green-500" />,
      change: data.previousPeriodData ? 
        calculateChange(
          successRate, 
          (data.previousPeriodData.successfulRequests / data.previousPeriodData.totalRequests) * 100
        ) : undefined,
      description: 'Successful requests'
    },
    {
      title: 'Error Rate',
      value: `${errorRate.toFixed(1)}%`,
      icon: <AlertCircle className="h-4 w-4 text-red-500" />,
      change: data.previousPeriodData ? 
        calculateChange(
          errorRate, 
          (data.previousPeriodData.failedRequests / data.previousPeriodData.totalRequests) * 100
        ) : undefined,
      description: 'Failed requests'
    },
    {
      title: 'Avg Response Time',
      value: `${data.averageResponseTime.toFixed(0)}ms`,
      icon: <Clock className="h-4 w-4 text-yellow-500" />,
      change: data.previousPeriodData ? 
        calculateChange(data.averageResponseTime, data.previousPeriodData.averageResponseTime) : undefined,
      description: 'Response latency'
    }
  ]
}

export const createDataTransferMetrics = (data: {
  dataTransferred: number
  requestsThisMonth: number
  monthlyLimit: number
  dailyAverage: number
  previousPeriodData?: {
    dataTransferred: number
    dailyAverage: number
  }
}): UsageMetric[] => {
  const calculateChange = (current: number, previous: number) => {
    if (previous === 0) return null
    const change = ((current - previous) / previous) * 100
    return {
      value: Math.round(change),
      type: change >= 0 ? 'increase' as const : 'decrease' as const,
      period: 'last period'
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  return [
    {
      title: 'Data Transferred',
      value: formatBytes(data.dataTransferred),
      icon: <Database className="h-4 w-4 text-purple-500" />,
      change: data.previousPeriodData ? 
        calculateChange(data.dataTransferred, data.previousPeriodData.dataTransferred) : undefined,
      description: 'Total data usage'
    },
    {
      title: 'Monthly Usage',
      value: data.requestsThisMonth,
      icon: <Activity className="h-4 w-4 text-blue-500" />,
      description: 'Requests this month',
      progress: {
        value: data.requestsThisMonth,
        max: data.monthlyLimit,
        label: 'Monthly limit'
      }
    },
    {
      title: 'Daily Average',
      value: data.dailyAverage.toFixed(0),
      icon: <TrendingUp className="h-4 w-4 text-green-500" />,
      change: data.previousPeriodData ? 
        calculateChange(data.dailyAverage, data.previousPeriodData.dailyAverage) : undefined,
      description: 'Requests per day'
    }
  ]
}

export default UsageMetricsCard
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Toolt<PERSON>,
  Legend,
} from 'recharts'
import { ChartContainer } from './ChartContainer'
import { chartTheme, getChartColor } from '@/lib/chart-theme'
import type { PieChartData, PieChartConfig } from '@/types/charts'

interface BasePieChartProps {
  data: PieChartData[]
  config: PieChartConfig
  className?: string
  loading?: boolean
  error?: string
}

export function BasePieChart({
  data,
  config,
  className = '',
  loading = false,
  error = '',
}: BasePieChartProps) {
  const {
    title,
    showLegend = true,
    showTooltip = true,
    height = 300,
    innerRadius = 0,
    outerRadius = 80,
  } = config

  return (
    <ChartContainer
      title={title || ''}
      className={className}
      loading={loading}
      error={error}
      height={height}
    >
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          paddingAngle={2}
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={entry.color || getChartColor(index)}
            />
          ))}
        </Pie>
        {showTooltip && (
          <Tooltip
            contentStyle={{
              backgroundColor: chartTheme.tooltip.backgroundColor,
              border: chartTheme.tooltip.border,
              borderRadius: chartTheme.tooltip.borderRadius,
              boxShadow: chartTheme.tooltip.boxShadow,
              color: chartTheme.tooltip.color,
              fontSize: chartTheme.tooltip.fontSize,
              fontFamily: chartTheme.tooltip.fontFamily,
            }}
          />
        )}
        {showLegend && (
          <Legend
            wrapperStyle={{
              fontSize: chartTheme.legend.fontSize,
              fontFamily: chartTheme.legend.fontFamily,
              fill: chartTheme.legend.fill,
            }}
          />
        )}
      </PieChart>
    </ChartContainer>
  )
}
import { useState } from 'react'
import { Calendar, Download, Filter, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar as CalendarComponent } from '@/components/ui/calendar'
import { format, subDays, subHours, subWeeks, subMonths } from 'date-fns'
import type { TimeRange } from '@/types/api'

interface ChartFiltersProps {
  timeRange: TimeRange
  onTimeRangeChange: (timeRange: TimeRange) => void
  onRefresh?: () => void
  onExport?: (format: 'png' | 'svg' | 'pdf') => void
  loading?: boolean
  showExport?: boolean
  showRefresh?: boolean
  additionalFilters?: React.ReactNode
}

const PRESET_RANGES = [
  { label: 'Last Hour', value: 'hour', getRange: () => ({ start: subHours(new Date(), 1), end: new Date() }) },
  { label: 'Last 24 Hours', value: 'day', getRange: () => ({ start: subDays(new Date(), 1), end: new Date() }) },
  { label: 'Last 7 Days', value: 'week', getRange: () => ({ start: subWeeks(new Date(), 1), end: new Date() }) },
  { label: 'Last 30 Days', value: 'month', getRange: () => ({ start: subMonths(new Date(), 1), end: new Date() }) },
  { label: 'Last 90 Days', value: '3months', getRange: () => ({ start: subMonths(new Date(), 3), end: new Date() }) },
  { label: 'Custom', value: 'custom', getRange: () => ({ start: new Date(), end: new Date() }) },
]

export function ChartFilters({
  timeRange,
  onTimeRangeChange,
  onRefresh,
  onExport,
  loading = false,
  showExport = true,
  showRefresh = true,
  additionalFilters,
}: ChartFiltersProps) {
  const [selectedPreset, setSelectedPreset] = useState('day')
  const [customStartDate, setCustomStartDate] = useState<Date | undefined>(timeRange.start)
  const [customEndDate, setCustomEndDate] = useState<Date | undefined>(timeRange.end)
  const [showCustomCalendar, setShowCustomCalendar] = useState(false)

  const handlePresetChange = (preset: string) => {
    setSelectedPreset(preset)
    if (preset !== 'custom') {
      const range = PRESET_RANGES.find(r => r.value === preset)?.getRange()
      if (range) {
        onTimeRangeChange(range)
      }
    }
  }

  const handleCustomDateChange = () => {
    if (customStartDate && customEndDate) {
      onTimeRangeChange({ start: customStartDate, end: customEndDate })
      setShowCustomCalendar(false)
    }
  }

  const handleExport = (format: 'png' | 'svg' | 'pdf') => {
    onExport?.(format)
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <Filter className="h-4 w-4" />
          Chart Filters
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-wrap items-center gap-3">
          {/* Time Range Selector */}
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <Select value={selectedPreset} onValueChange={handlePresetChange}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select time range" />
              </SelectTrigger>
              <SelectContent>
                {PRESET_RANGES.map((range) => (
                  <SelectItem key={range.value} value={range.value}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Custom Date Range */}
          {selectedPreset === 'custom' && (
            <Popover open={showCustomCalendar} onOpenChange={setShowCustomCalendar}>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm">
                  {customStartDate && customEndDate
                    ? `${format(customStartDate, 'MMM dd')} - ${format(customEndDate, 'MMM dd')}`
                    : 'Select dates'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="p-3 space-y-3">
                  <div>
                    <label className="text-sm font-medium">Start Date</label>
                    <CalendarComponent
                      mode="single"
                      selected={customStartDate}
                      onSelect={setCustomStartDate}
                      disabled={(date) => date > new Date() || (customEndDate ? date > customEndDate : false)}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">End Date</label>
                    <CalendarComponent
                      mode="single"
                      selected={customEndDate}
                      onSelect={setCustomEndDate}
                      disabled={(date) => date > new Date() || (customStartDate ? date < customStartDate : false)}
                    />
                  </div>
                  <Button onClick={handleCustomDateChange} size="sm" className="w-full">
                    Apply Date Range
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          )}

          {/* Additional Filters */}
          {additionalFilters}

          {/* Action Buttons */}
          <div className="flex items-center gap-2 ml-auto">
            {showRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                disabled={loading}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            )}

            {showExport && (
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Export
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-40" align="end">
                  <div className="space-y-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => handleExport('png')}
                    >
                      Export as PNG
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => handleExport('svg')}
                    >
                      Export as SVG
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => handleExport('pdf')}
                    >
                      Export as PDF
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>

        {/* Current Range Display */}
        <div className="text-xs text-muted-foreground">
          Current range: {format(timeRange.start, 'MMM dd, yyyy HH:mm')} - {format(timeRange.end, 'MMM dd, yyyy HH:mm')}
        </div>
      </CardContent>
    </Card>
  )
}
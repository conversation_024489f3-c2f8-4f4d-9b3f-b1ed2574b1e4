import { format } from 'date-fns'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface BaseTooltipProps {
  active?: boolean
  payload?: any[]
  label?: string
}

export function ApiUsageTooltip({ active, payload, label }: BaseTooltipProps) {
  if (!active || !payload || !payload.length) return null

  return (
    <Card className="border shadow-lg">
      <CardContent className="p-3 space-y-2">
        <p className="font-medium text-sm">{label}</p>
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm">{entry.name}</span>
            </div>
            <span className="text-sm font-medium">
              {typeof entry.value === 'number' 
                ? entry.value.toLocaleString()
                : entry.value
              }
            </span>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

export function EndpointTooltip({ active, payload }: BaseTooltipProps) {
  if (!active || !payload || !payload.length) return null

  const data = payload[0].payload

  return (
    <Card className="border shadow-lg">
      <CardContent className="p-3 space-y-2">
        <p className="font-medium text-sm">{data.name}</p>
        <div className="space-y-1">
          <div className="flex justify-between">
            <span className="text-xs text-muted-foreground">Requests:</span>
            <span className="text-xs font-medium">{data.value.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-xs text-muted-foreground">Share:</span>
            <span className="text-xs font-medium">{data.percentage}%</span>
          </div>
          {data.averageResponseTime && (
            <div className="flex justify-between">
              <span className="text-xs text-muted-foreground">Avg Response:</span>
              <span className="text-xs font-medium">{Math.round(data.averageResponseTime)}ms</span>
            </div>
          )}
          {data.errorRate !== undefined && (
            <div className="flex justify-between">
              <span className="text-xs text-muted-foreground">Error Rate:</span>
              <Badge variant={data.errorRate > 0.05 ? 'destructive' : 'secondary'} className="text-xs">
                {(data.errorRate * 100).toFixed(1)}%
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function ResponseTimeTooltip({ active, payload, label }: BaseTooltipProps) {
  if (!active || !payload || !payload.length) return null

  const data = payload[0].payload

  return (
    <Card className="border shadow-lg">
      <CardContent className="p-3 space-y-2">
        <p className="font-medium text-sm">{label}</p>
        <div className="space-y-1">
          <div className="flex justify-between">
            <span className="text-xs text-muted-foreground">Response Time:</span>
            <span className="text-xs font-medium">{data.responseTime}ms</span>
          </div>
          <div className="flex justify-between">
            <span className="text-xs text-muted-foreground">Total Requests:</span>
            <span className="text-xs font-medium">{data.requests.toLocaleString()}</span>
          </div>
          {data.errorRate !== undefined && (
            <div className="flex justify-between">
              <span className="text-xs text-muted-foreground">Error Rate:</span>
              <Badge variant={data.errorRate > 5 ? 'destructive' : 'secondary'} className="text-xs">
                {data.errorRate}%
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function ErrorRateTooltip({ active, payload, label }: BaseTooltipProps) {
  if (!active || !payload || !payload.length) return null

  return (
    <Card className="border shadow-lg">
      <CardContent className="p-3 space-y-2">
        <p className="font-medium text-sm">{label}</p>
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm">{entry.name}</span>
            </div>
            <Badge 
              variant={entry.value > 5 ? 'destructive' : 'secondary'} 
              className="text-xs"
            >
              {entry.value}%
            </Badge>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

export function PerformanceTooltip({ active, payload, label }: BaseTooltipProps) {
  if (!active || !payload || !payload.length) return null

  const data = payload[0].payload

  return (
    <Card className="border shadow-lg">
      <CardContent className="p-3 space-y-2">
        <p className="font-medium text-sm">
          {typeof label === 'string' && label.includes('T') 
            ? format(new Date(label), 'MMM dd, HH:mm')
            : label
          }
        </p>
        {payload.map((entry, index) => (
          <div key={index} className="space-y-1">
            <div className="flex items-center justify-between gap-4">
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-sm">{entry.name}</span>
              </div>
              <span className="text-sm font-medium">
                {entry.name.includes('Time') 
                  ? `${entry.value}ms`
                  : entry.name.includes('Rate') || entry.name.includes('%')
                  ? `${entry.value}%`
                  : entry.value.toLocaleString()
                }
              </span>
            </div>
          </div>
        ))}
        
        {/* Additional context if available */}
        {data.totalRequests && (
          <div className="pt-1 border-t">
            <div className="flex justify-between">
              <span className="text-xs text-muted-foreground">Total Requests:</span>
              <span className="text-xs font-medium">{data.totalRequests.toLocaleString()}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
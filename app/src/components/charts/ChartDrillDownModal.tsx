import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { X, TrendingUp, TrendingDown, Activity, Clock } from 'lucide-react'
import { ApiUsageOverTimeChart } from './ApiUsageOverTimeChart'
import { ResponseTimeChart } from './ResponseTimeChart'
import { ErrorRateTrendChart } from './ErrorRateTrendChart'
import type { UsageMetrics, TrendData } from '@/types/analytics'
import { format } from 'date-fns'

interface DrillDownData {
  endpoint?: string
  userId?: string
  apiKeyId?: string
  timeRange: {
    start: Date
    end: Date
  }
  metrics: UsageMetrics
  trends: {
    requests: TrendData[]
    responseTime: TrendData[]
    errors: TrendData[]
  }
}

interface ChartDrillDownModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: DrillDownData | null
  title?: string
}

export function ChartDrillDownModal({
  open,
  onOpenChange,
  data,
  title = 'Detailed Analytics',
}: ChartDrillDownModalProps) {
  const [activeTab, setActiveTab] = useState('overview')

  if (!data) return null

  const { endpoint, userId, apiKeyId, timeRange, metrics, trends } = data

  const getContextTitle = () => {
    if (endpoint) return `Endpoint: ${endpoint}`
    if (userId) return `User: ${userId}`
    if (apiKeyId) return `API Key: ${apiKeyId}`
    return title
  }

  const calculateTrend = (data: TrendData[]) => {
    if (data.length < 2) return { direction: 'stable', percentage: 0 }
    
    const recent = data.slice(-5).reduce((sum, item) => sum + item.value, 0) / 5
    const previous = data.slice(-10, -5).reduce((sum, item) => sum + item.value, 0) / 5
    
    if (previous === 0) return { direction: 'stable', percentage: 0 }
    
    const percentage = ((recent - previous) / previous) * 100
    const direction = percentage > 5 ? 'up' : percentage < -5 ? 'down' : 'stable'
    
    return { direction, percentage: Math.abs(percentage) }
  }

  const requestsTrend = calculateTrend(trends.requests)
  const responseTimeTrend = calculateTrend(trends.responseTime)
  const errorsTrend = calculateTrend(trends.errors)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0">
          <DialogTitle className="text-lg font-semibold">
            {getContextTitle()}
          </DialogTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="h-6 w-6 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Requests</p>
                    <p className="text-2xl font-bold">{metrics.totalRequests.toLocaleString()}</p>
                  </div>
                  <div className="flex items-center gap-1">
                    {requestsTrend.direction === 'up' ? (
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    ) : requestsTrend.direction === 'down' ? (
                      <TrendingDown className="h-4 w-4 text-red-500" />
                    ) : (
                      <Activity className="h-4 w-4 text-gray-500" />
                    )}
                    <span className="text-xs text-muted-foreground">
                      {requestsTrend.percentage.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Avg Response Time</p>
                    <p className="text-2xl font-bold">{Math.round(metrics.averageResponseTime)}ms</p>
                  </div>
                  <div className="flex items-center gap-1">
                    {responseTimeTrend.direction === 'up' ? (
                      <TrendingUp className="h-4 w-4 text-red-500" />
                    ) : responseTimeTrend.direction === 'down' ? (
                      <TrendingDown className="h-4 w-4 text-green-500" />
                    ) : (
                      <Clock className="h-4 w-4 text-gray-500" />
                    )}
                    <span className="text-xs text-muted-foreground">
                      {responseTimeTrend.percentage.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Success Rate</p>
                    <p className="text-2xl font-bold">
                      {((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1)}%
                    </p>
                  </div>
                  <Badge variant={metrics.successfulRequests / metrics.totalRequests > 0.95 ? 'default' : 'destructive'}>
                    {metrics.successfulRequests.toLocaleString()} / {metrics.totalRequests.toLocaleString()}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">Data Transferred</p>
                    <p className="text-2xl font-bold">
                      {(metrics.totalDataTransferred / 1024 / 1024).toFixed(1)}MB
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {format(timeRange.start, 'MMM dd')} - {format(timeRange.end, 'MMM dd')}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Charts */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="errors">Errors</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <ApiUsageOverTimeChart
                  data={trends.requests}
                  title="Request Volume Over Time"
                  height={350}
                  showMultipleMetrics={false}
                />
                <ResponseTimeChart
                  data={metrics.topEndpoints}
                  title="Response Time by Endpoint"
                  height={350}
                  showErrorRate={false}
                  maxEndpoints={8}
                />
              </div>
            </TabsContent>

            <TabsContent value="performance" className="space-y-6">
              <div className="grid grid-cols-1 gap-6">
                <ApiUsageOverTimeChart
                  data={trends.responseTime}
                  title="Response Time Trends"
                  height={400}
                  showMultipleMetrics={true}
                  additionalMetrics={{
                    responseTime: trends.responseTime,
                    errors: trends.errors,
                  }}
                />
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Performance Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {metrics.topEndpoints.slice(0, 5).map((endpoint) => (
                          <div key={endpoint.endpoint} className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium truncate">
                                {endpoint.endpoint.replace('/api/', '')}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {endpoint.count.toLocaleString()} requests
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium">
                                {Math.round(endpoint.averageResponseTime)}ms
                              </p>
                              <Badge variant={endpoint.averageResponseTime > 1000 ? 'destructive' : 'secondary'} className="text-xs">
                                {endpoint.averageResponseTime > 1000 ? 'Slow' : 'Fast'}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Response Time Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {metrics.responseTimeDistribution?.map((dist) => (
                          <div key={dist.range} className="flex items-center justify-between">
                            <span className="text-sm">{dist.range}</span>
                            <div className="flex items-center gap-2">
                              <div className="w-20 bg-muted rounded-full h-2">
                                <div 
                                  className="bg-primary h-2 rounded-full"
                                  style={{ width: `${dist.percentage}%` }}
                                />
                              </div>
                              <span className="text-xs text-muted-foreground w-12 text-right">
                                {dist.percentage.toFixed(1)}%
                              </span>
                            </div>
                          </div>
                        )) || (
                          <p className="text-sm text-muted-foreground">No distribution data available</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="errors" className="space-y-6">
              <div className="grid grid-cols-1 gap-6">
                <ErrorRateTrendChart
                  data={trends.errors}
                  title="Error Rate Trends"
                  height={400}
                  showByStatusCode={true}
                  errorBreakdown={metrics.errorBreakdown}
                />
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Error Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {metrics.errorBreakdown.map((error) => (
                          <div key={error.statusCode} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <div 
                                className="w-3 h-3 rounded-full"
                                style={{ 
                                  backgroundColor: error.statusCode >= 500 ? '#ef4444' : '#f59e0b' 
                                }}
                              />
                              <span className="text-sm font-medium">
                                {error.statusCode} Errors
                              </span>
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium">{error.count}</p>
                              <p className="text-xs text-muted-foreground">
                                {error.percentage.toFixed(1)}%
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Error Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Total Errors</span>
                          <span className="text-sm font-medium">
                            {metrics.failedRequests.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Error Rate</span>
                          <Badge variant={metrics.failedRequests / metrics.totalRequests > 0.05 ? 'destructive' : 'secondary'}>
                            {((metrics.failedRequests / metrics.totalRequests) * 100).toFixed(2)}%
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Most Common</span>
                          <span className="text-sm font-medium">
                            {metrics.errorBreakdown[0]?.statusCode || 'N/A'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Trend</span>
                          <div className="flex items-center gap-1">
                            {errorsTrend.direction === 'up' ? (
                              <TrendingUp className="h-4 w-4 text-red-500" />
                            ) : errorsTrend.direction === 'down' ? (
                              <TrendingDown className="h-4 w-4 text-green-500" />
                            ) : (
                              <Activity className="h-4 w-4 text-gray-500" />
                            )}
                            <span className="text-xs">
                              {errorsTrend.percentage.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}
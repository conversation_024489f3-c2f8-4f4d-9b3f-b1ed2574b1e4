import { type ReactNode, useRef, useState } from 'react'
import { ResponsiveContainer } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Skeleton } from '@/components/ui/skeleton'
import { AlertCircle, Download, Maximize2, Minimize2, ZoomIn } from 'lucide-react'
import { cn } from '@/lib/utils'
import { ChartExporter, type ExportOptions } from '@/lib/chart-export'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useToast } from '@/hooks/useToast'

interface InteractiveChartContainerProps {
  title?: string
  children: ReactNode
  className?: string
  loading?: boolean
  error?: string
  height?: number
  actions?: ReactNode
  enableExport?: boolean
  enableDrillDown?: boolean
  onDrillDown?: (data: any) => void
  drillDownData?: any
  exportFilename?: string
}

export function InteractiveChartContainer({
  title,
  children,
  className,
  loading = false,
  error,
  height = 300,
  actions,
  enableExport = true,
  enableDrillDown = false,
  onDrillDown,
  drillDownData,
  exportFilename = 'chart',
}: InteractiveChartContainerProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const { toast } = useToast()

  const handleExport = async (format: 'png' | 'svg' | 'pdf') => {
    if (!chartRef.current) return

    setIsExporting(true)
    try {
      const options: ExportOptions = {
        filename: exportFilename,
        quality: 1,
        backgroundColor: '#ffffff',
        scale: 2,
      }

      await ChartExporter.exportChart(chartRef.current, format, options)
      
      toast({
        title: 'Export Successful',
        description: `Chart exported as ${format.toUpperCase()}`,
      })
    } catch (error) {
      console.error('Export failed:', error)
      toast({
        title: 'Export Failed',
        description: 'Failed to export chart. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  const handleDrillDown = () => {
    if (onDrillDown && drillDownData) {
      onDrillDown(drillDownData)
    }
  }

  const chartActions = (
    <div className="flex items-center gap-2">
      {enableDrillDown && onDrillDown && (
        <Button
          variant="outline"
          size="sm"
          onClick={handleDrillDown}
          className="flex items-center gap-2"
        >
          <ZoomIn className="h-4 w-4" />
          Drill Down
        </Button>
      )}
      
      {enableExport && (
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('png')}
            disabled={isExporting}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            PNG
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('svg')}
            disabled={isExporting}
          >
            SVG
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('pdf')}
            disabled={isExporting}
          >
            PDF
          </Button>
        </div>
      )}

      <Dialog open={isFullscreen} onOpenChange={setIsFullscreen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <Maximize2 className="h-4 w-4" />
            Fullscreen
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-6xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              {title}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(false)}
                className="flex items-center gap-2"
              >
                <Minimize2 className="h-4 w-4" />
                Close
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="w-full" style={{ height: height * 1.5 }}>
            <ResponsiveContainer width="100%" height="100%">
              {children as React.ReactElement}
            </ResponsiveContainer>
          </div>
        </DialogContent>
      </Dialog>

      {actions}
    </div>
  )

  if (loading) {
    return (
      <Card className={cn('w-full', className)}>
        {title && (
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-base font-medium">{title}</CardTitle>
            {chartActions}
          </CardHeader>
        )}
        <CardContent>
          <Skeleton className="w-full" style={{ height }} />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={cn('w-full', className)}>
        {title && (
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-base font-medium">{title}</CardTitle>
            {chartActions}
          </CardHeader>
        )}
        <CardContent>
          <div 
            className="flex items-center justify-center text-muted-foreground"
            style={{ height }}
          >
            <div className="flex flex-col items-center gap-2">
              <AlertCircle className="h-8 w-8" />
              <p className="text-sm">{error}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn('w-full', className)}>
      {title && (
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-base font-medium">{title}</CardTitle>
          {chartActions}
        </CardHeader>
      )}
      <CardContent>
        <div ref={chartRef} className="w-full">
          <ResponsiveContainer width="100%" height={height}>
            {children as React.ReactElement}
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  )
}
import { ApiUsageOverTime<PERSON>hart } from './ApiUsageOverTimeChart'
import { EndpointUsageChart } from './EndpointUsageChart'
import { ResponseTimeChart } from './ResponseTimeChart'
import { ErrorRateTrendChart } from './ErrorRateTrendChart'
import type { UsageMetrics, TrendData } from '@/types/analytics'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Activity, BarChart3, PieChart, TrendingUp } from 'lucide-react'

interface UsageAnalyticsDashboardProps {
  metrics: UsageMetrics
  trends: {
    requests: TrendData[]
    responseTime: TrendData[]
    errors: TrendData[]
  }
  loading?: boolean
  error?: string
  className?: string
}

export function UsageAnalyticsDashboard({
  metrics,
  trends,
  loading = false,
  error,
  className,
}: UsageAnalyticsDashboardProps) {
  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Error loading analytics: {error}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="endpoints" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Endpoints
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="errors" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Errors
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ApiUsageOverTimeChart
              data={trends.requests}
              title="API Requests Over Time"
              loading={loading}
              height={350}
              showMultipleMetrics={true}
              additionalMetrics={{
                responseTime: trends.responseTime,
                errors: trends.errors,
              }}
            />
            <EndpointUsageChart
              data={metrics.topEndpoints}
              title="Top Endpoints"
              loading={loading}
              height={350}
              showAsDonut={true}
              maxEndpoints={8}
            />
          </div>
        </TabsContent>

        <TabsContent value="endpoints" className="space-y-6">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <EndpointUsageChart
              data={metrics.topEndpoints}
              title="Endpoint Usage Distribution"
              loading={loading}
              height={400}
              showAsDonut={false}
              maxEndpoints={12}
            />
            <Card>
              <CardHeader>
                <CardTitle className="text-base font-medium">Endpoint Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {metrics.topEndpoints.slice(0, 5).map((endpoint) => (
                    <div key={endpoint.endpoint} className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {endpoint.endpoint}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {endpoint.count.toLocaleString()} requests
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {Math.round(endpoint.averageResponseTime)}ms
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {(endpoint.errorRate * 100).toFixed(1)}% errors
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <ResponseTimeChart
              data={metrics.topEndpoints}
              title="Response Time by Endpoint"
              loading={loading}
              height={400}
              showErrorRate={true}
              maxEndpoints={15}
            />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <ApiUsageOverTimeChart
                data={trends.responseTime}
                title="Average Response Time Trend"
                loading={loading}
                height={300}
              />
              <Card>
                <CardHeader>
                  <CardTitle className="text-base font-medium">Performance Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Average Response Time</span>
                      <span className="text-sm font-medium">
                        {Math.round(metrics.averageResponseTime)}ms
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Total Requests</span>
                      <span className="text-sm font-medium">
                        {metrics.totalRequests.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Success Rate</span>
                      <span className="text-sm font-medium">
                        {((metrics.successfulRequests / metrics.totalRequests) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Data Transferred</span>
                      <span className="text-sm font-medium">
                        {(metrics.totalDataTransferred / 1024 / 1024).toFixed(1)} MB
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="errors" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <ErrorRateTrendChart
              data={trends.errors}
              title="Error Rate Trends"
              loading={loading}
              height={350}
              showByStatusCode={true}
              errorBreakdown={metrics.errorBreakdown}
            />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base font-medium">Error Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {metrics.errorBreakdown.map((error) => (
                      <div key={error.statusCode} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ 
                              backgroundColor: error.statusCode >= 500 ? '#ef4444' : '#f59e0b' 
                            }}
                          />
                          <span className="text-sm font-medium">
                            {error.statusCode} Errors
                          </span>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{error.count}</p>
                          <p className="text-xs text-muted-foreground">
                            {error.percentage.toFixed(1)}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle className="text-base font-medium">Error Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Total Errors</span>
                      <span className="text-sm font-medium">
                        {metrics.failedRequests.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Error Rate</span>
                      <span className="text-sm font-medium">
                        {((metrics.failedRequests / metrics.totalRequests) * 100).toFixed(2)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Most Common Error</span>
                      <span className="text-sm font-medium">
                        {metrics.errorBreakdown[0]?.statusCode || 'N/A'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
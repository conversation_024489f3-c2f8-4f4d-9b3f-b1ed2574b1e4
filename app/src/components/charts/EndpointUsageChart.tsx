import { BasePieChart } from './BasePieChart'
import type { Pie<PERSON>hartData, PieChartConfig } from '@/types/charts'
import type { UsageMetrics } from '@/types/analytics'
import { getChartColor } from '@/lib/chart-theme'

interface EndpointUsageChartProps {
  data: UsageMetrics['topEndpoints']
  title?: string
  className?: string
  loading?: boolean
  error?: string
  height?: number
  showAsDonut?: boolean
  maxEndpoints?: number
}

export function EndpointUsageChart({
  data,
  title = 'Endpoint Usage Distribution',
  className,
  loading = false,
  error,
  height = 300,
  showAsDonut = false,
  maxEndpoints = 10,
}: EndpointUsageChartProps) {
  // Transform and limit data for chart consumption
  const sortedData = [...data]
    .sort((a, b) => b.count - a.count)
    .slice(0, maxEndpoints)

  const totalRequests = sortedData.reduce((sum, item) => sum + item.count, 0)

  const chartData: PieChartData[] = sortedData.map((item, index) => ({
    name: item.endpoint.replace('/api/', '').replace('/', '') || 'Root',
    value: item.count,
    color: getChartColor(index),
    percentage: ((item.count / totalRequests) * 100).toFixed(1),
  }))

  // Add "Others" category if we have more endpoints
  if (data.length > maxEndpoints) {
    const othersCount = data
      .slice(maxEndpoints)
      .reduce((sum, item) => sum + item.count, 0)
    
    if (othersCount > 0) {
      chartData.push({
        name: 'Others',
        value: othersCount,
        color: getChartColor(maxEndpoints),
        percentage: ((othersCount / (totalRequests + othersCount)) * 100).toFixed(1),
      })
    }
  }

  const config: PieChartConfig = {
    title,
    showLegend: true,
    showTooltip: true,
    height,
    innerRadius: showAsDonut ? 60 : 0,
    outerRadius: 100,
  }



  return (
    <div className={className}>
      <BasePieChart
        data={chartData}
        config={config}
        loading={loading}
        error={error}
      />
    </div>
  )
}
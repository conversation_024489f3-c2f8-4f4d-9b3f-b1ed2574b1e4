import { useState } from 'react'
import { ApiUsageOverTimeChart } from './ApiUsageOverTimeChart'
import { EndpointUsageChart } from './EndpointUsageChart'
import { ResponseTimeChart } from './ResponseTimeChart'
import { ErrorRateTrendChart } from './ErrorRateTrendChart'
import { ChartFilters } from './ChartFilters'
import { ChartDrillDownModal } from './ChartDrillDownModal'
import { InteractiveChartContainer } from './InteractiveChartContainer'
import type { UsageMetrics, TrendData } from '@/types/analytics'
import type { TimeRange } from '@/types/api'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Activity, BarChart3, Pie<PERSON><PERSON>, TrendingUp, Filter } from 'lucide-react'

interface InteractiveUsageAnalyticsDashboardProps {
  metrics: UsageMetrics
  trends: {
    requests: TrendData[]
    responseTime: TrendData[]
    errors: TrendData[]
  }
  timeRange: TimeRange
  onTimeRangeChange: (timeRange: TimeRange) => void
  onRefresh?: () => void
  loading?: boolean
  error?: string
  className?: string
}

export function InteractiveUsageAnalyticsDashboard({
  metrics,
  trends,
  timeRange,
  onTimeRangeChange,
  onRefresh,
  loading = false,
  error,
  className,
}: InteractiveUsageAnalyticsDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [drillDownData, setDrillDownData] = useState<any>(null)
  const [showDrillDown, setShowDrillDown] = useState(false)
  const [selectedEndpoint, setSelectedEndpoint] = useState<string>('all')
  const [selectedMetric, setSelectedMetric] = useState<string>('requests')

  const handleDrillDown = (data: any) => {
    setDrillDownData({
      endpoint: data.endpoint,
      timeRange,
      metrics,
      trends,
    })
    setShowDrillDown(true)
  }

  const handleExport = (format: 'png' | 'svg' | 'pdf') => {
    // Export functionality is handled by InteractiveChartContainer
    console.log(`Exporting as ${format}`)
  }

  const filteredMetrics = selectedEndpoint === 'all' 
    ? metrics 
    : {
        ...metrics,
        topEndpoints: metrics.topEndpoints.filter(e => e.endpoint === selectedEndpoint)
      }

  const additionalFilters = (
    <div className="flex items-center gap-3">
      <div className="flex items-center gap-2">
        <Filter className="h-4 w-4 text-muted-foreground" />
        <Select value={selectedEndpoint} onValueChange={setSelectedEndpoint}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="All Endpoints" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Endpoints</SelectItem>
            {metrics.topEndpoints.slice(0, 10).map((endpoint) => (
              <SelectItem key={endpoint.endpoint} value={endpoint.endpoint}>
                {endpoint.endpoint.replace('/api/', '').replace('/', '') || 'Root'}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Select value={selectedMetric} onValueChange={setSelectedMetric}>
        <SelectTrigger className="w-32">
          <SelectValue placeholder="Metric" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="requests">Requests</SelectItem>
          <SelectItem value="responseTime">Response Time</SelectItem>
          <SelectItem value="errors">Errors</SelectItem>
        </SelectContent>
      </Select>
    </div>
  )

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">Error loading analytics: {error}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      {/* Filters */}
      <ChartFilters
        timeRange={timeRange}
        onTimeRangeChange={onTimeRangeChange}
        onRefresh={onRefresh || (() => {})}
        onExport={handleExport}
        loading={loading}
        additionalFilters={additionalFilters}
      />

      {/* Main Dashboard */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="endpoints" className="flex items-center gap-2">
            <PieChart className="h-4 w-4" />
            Endpoints
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="errors" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Errors
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <InteractiveChartContainer
              title="API Requests Over Time"
              loading={loading}
              height={350}
              enableExport={true}
              enableDrillDown={true}
              onDrillDown={handleDrillDown}
              exportFilename="api-requests-overview"
            >
              <ApiUsageOverTimeChart
                data={trends.requests}
                loading={loading}
                height={350}
                showMultipleMetrics={true}
                additionalMetrics={{
                  responseTime: trends.responseTime,
                  errors: trends.errors,
                }}
              />
            </InteractiveChartContainer>

            <InteractiveChartContainer
              title="Top Endpoints"
              loading={loading}
              height={350}
              enableExport={true}
              enableDrillDown={true}
              onDrillDown={handleDrillDown}
              exportFilename="endpoint-distribution"
            >
              <EndpointUsageChart
                data={filteredMetrics.topEndpoints}
                loading={loading}
                height={350}
                showAsDonut={true}
                maxEndpoints={8}
              />
            </InteractiveChartContainer>
          </div>
        </TabsContent>

        <TabsContent value="endpoints" className="space-y-6">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <InteractiveChartContainer
              title="Endpoint Usage Distribution"
              loading={loading}
              height={400}
              enableExport={true}
              enableDrillDown={true}
              onDrillDown={handleDrillDown}
              exportFilename="endpoint-usage-distribution"
            >
              <EndpointUsageChart
                data={filteredMetrics.topEndpoints}
                loading={loading}
                height={400}
                showAsDonut={false}
                maxEndpoints={12}
              />
            </InteractiveChartContainer>

            <Card>
              <CardHeader>
                <CardTitle className="text-base font-medium">Endpoint Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredMetrics.topEndpoints.slice(0, 8).map((endpoint) => (
                    <div key={endpoint.endpoint} className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {endpoint.endpoint}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {endpoint.count.toLocaleString()} requests
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {Math.round(endpoint.averageResponseTime)}ms
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {(endpoint.errorRate * 100).toFixed(1)}% errors
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <InteractiveChartContainer
              title="Response Time by Endpoint"
              loading={loading}
              height={400}
              enableExport={true}
              enableDrillDown={true}
              onDrillDown={handleDrillDown}
              exportFilename="response-time-analysis"
            >
              <ResponseTimeChart
                data={filteredMetrics.topEndpoints}
                loading={loading}
                height={400}
                showErrorRate={true}
                maxEndpoints={15}
              />
            </InteractiveChartContainer>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <InteractiveChartContainer
                title="Response Time Trends"
                loading={loading}
                height={300}
                enableExport={true}
                exportFilename="response-time-trends"
              >
                <ApiUsageOverTimeChart
                  data={trends.responseTime}
                  loading={loading}
                  height={300}
                />
              </InteractiveChartContainer>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base font-medium">Performance Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Average Response Time</span>
                      <span className="text-sm font-medium">
                        {Math.round(filteredMetrics.averageResponseTime)}ms
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Total Requests</span>
                      <span className="text-sm font-medium">
                        {filteredMetrics.totalRequests.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Success Rate</span>
                      <span className="text-sm font-medium">
                        {((filteredMetrics.successfulRequests / filteredMetrics.totalRequests) * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Data Transferred</span>
                      <span className="text-sm font-medium">
                        {(filteredMetrics.totalDataTransferred / 1024 / 1024).toFixed(1)} MB
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="errors" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <InteractiveChartContainer
              title="Error Rate Trends"
              loading={loading}
              height={350}
              enableExport={true}
              enableDrillDown={true}
              onDrillDown={handleDrillDown}
              exportFilename="error-rate-trends"
            >
              <ErrorRateTrendChart
                data={trends.errors}
                loading={loading}
                height={350}
                showByStatusCode={true}
                errorBreakdown={filteredMetrics.errorBreakdown}
              />
            </InteractiveChartContainer>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base font-medium">Error Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {filteredMetrics.errorBreakdown.map((error) => (
                      <div key={error.statusCode} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ 
                              backgroundColor: error.statusCode >= 500 ? '#ef4444' : '#f59e0b' 
                            }}
                          />
                          <span className="text-sm font-medium">
                            {error.statusCode} Errors
                          </span>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{error.count}</p>
                          <p className="text-xs text-muted-foreground">
                            {error.percentage.toFixed(1)}%
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-base font-medium">Error Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Total Errors</span>
                      <span className="text-sm font-medium">
                        {filteredMetrics.failedRequests.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Error Rate</span>
                      <span className="text-sm font-medium">
                        {((filteredMetrics.failedRequests / filteredMetrics.totalRequests) * 100).toFixed(2)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Most Common Error</span>
                      <span className="text-sm font-medium">
                        {filteredMetrics.errorBreakdown[0]?.statusCode || 'N/A'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Drill Down Modal */}
      <ChartDrillDownModal
        open={showDrillDown}
        onOpenChange={setShowDrillDown}
        data={drillDownData}
        title="Detailed Analytics"
      />
    </div>
  )
}
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,

} from 'recharts'
import { ChartContainer } from './ChartContainer'
import { chartTheme } from '@/lib/chart-theme'
import type { BaseChartProps } from '@/types/charts'

interface BaseBarChartProps extends BaseChartProps {
  stacked?: boolean
}

export function BaseBarChart({
  data,
  config,
  className = '',
  loading = false,
  error = '',
  stacked = false,
}: BaseBarChartProps) {
  const {
    title,
    xAxisKey,
    dataKeys,
    showGrid = true,
    showLegend = true,
    showTooltip = true,
    height = 300,
    margin = { top: 5, right: 30, left: 20, bottom: 5 },
  } = config

  return (
    <ChartContainer
      title={title || ''}
      className={className}
      loading={loading}
      error={error}
      height={height}
    >
      <BarChart data={data} margin={margin}>
        {showGrid && (
          <CartesianGrid
            strokeDasharray={chartTheme.grid.strokeDasharray}
            stroke={chartTheme.grid.stroke}
            strokeOpacity={chartTheme.grid.strokeOpacity}
          />
        )}
        <XAxis
          dataKey={xAxisKey}
          stroke={chartTheme.axis.stroke}
          fontSize={chartTheme.axis.fontSize}
          fontFamily={chartTheme.axis.fontFamily}
          fill={chartTheme.axis.fill}
        />
        <YAxis
          stroke={chartTheme.axis.stroke}
          fontSize={chartTheme.axis.fontSize}
          fontFamily={chartTheme.axis.fontFamily}
          fill={chartTheme.axis.fill}
        />
        {showTooltip && (
          <Tooltip
            contentStyle={{
              backgroundColor: chartTheme.tooltip.backgroundColor,
              border: chartTheme.tooltip.border,
              borderRadius: chartTheme.tooltip.borderRadius,
              boxShadow: chartTheme.tooltip.boxShadow,
              color: chartTheme.tooltip.color,
              fontSize: chartTheme.tooltip.fontSize,
              fontFamily: chartTheme.tooltip.fontFamily,
            }}
          />
        )}
        {showLegend && (
          <Legend
            wrapperStyle={{
              fontSize: chartTheme.legend.fontSize,
              fontFamily: chartTheme.legend.fontFamily,
              fill: chartTheme.legend.fill,
            }}
          />
        )}
        {dataKeys.map((dataKey) => (
          <Bar
            key={dataKey.key}
            dataKey={dataKey.key}
            fill={dataKey.color}
            name={dataKey.name}
            {...(stacked ? { stackId: 'stack' } : {})}
            radius={[2, 2, 0, 0]}
          />
        ))}
      </BarChart>
    </ChartContainer>
  )
}
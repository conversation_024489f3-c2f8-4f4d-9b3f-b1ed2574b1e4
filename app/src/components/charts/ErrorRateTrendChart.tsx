import { BaseAreaChart } from './BaseAreaChart'
import type { ChartData, ChartConfig } from '@/types/charts'
import type { TrendData, UsageMetrics } from '@/types/analytics'
import { chartTheme } from '@/lib/chart-theme'
import { format } from 'date-fns'

interface ErrorRateTrendChartProps {
  data: TrendData[]
  title?: string
  className?: string
  loading?: boolean
  error?: string
  height?: number
  showByStatusCode?: boolean
  errorBreakdown?: UsageMetrics['errorBreakdown']
}

export function ErrorRateTrendChart({
  data,
  title = 'Error Rate Trends',
  className,
  loading = false,
  error,
  height = 300,
  showByStatusCode = false,
  errorBreakdown,
}: ErrorRateTrendChartProps) {
  // Transform data for chart consumption
  const chartData: ChartData[] = data.map((item) => {
    const baseData: ChartData = {
      timestamp: format(item.timestamp, 'MMM dd, HH:mm'),
      errorRate: Number((item.value * 100).toFixed(2)), // Convert to percentage
    }

    // If showing breakdown by status code, add mock data for demonstration
    // In real implementation, this would come from the actual data
    if (showByStatusCode && errorBreakdown) {
      errorBreakdown.forEach((breakdown) => {
        const statusKey = `status${breakdown.statusCode}`
        baseData[statusKey] = breakdown.percentage
      })
    }

    return baseData
  })

  const dataKeys = [
    {
      key: 'errorRate',
      name: 'Overall Error Rate (%)',
      color: chartTheme.colors.error,
      type: 'monotone' as const,
    },
  ]

  // Add status code specific error rates if requested
  if (showByStatusCode && errorBreakdown) {
    const statusColors = [
      chartTheme.colors.warning, // 4xx errors
      chartTheme.colors.error,   // 5xx errors
      chartTheme.colors.info,    // Other errors
    ]

    errorBreakdown.forEach((breakdown, index) => {
      dataKeys.push({
        key: `status${breakdown.statusCode}`,
        name: `${breakdown.statusCode} Errors (%)`,
        color: statusColors[index % statusColors.length] || chartTheme.colors.error,
        type: 'monotone' as const,
      })
    })
  }

  const config: ChartConfig = {
    title,
    xAxisKey: 'timestamp',
    dataKeys,
    showGrid: true,
    showLegend: showByStatusCode,
    showTooltip: true,
    height,
    margin: { top: 5, right: 30, left: 20, bottom: 5 },
  }



  return (
    <BaseAreaChart
      data={chartData}
      config={config}
      className={className}
      loading={loading}
      error={error}
      stacked={showByStatusCode}
      smooth={true}
    />
  )
}
import { BaseBar<PERSON>hart } from './BaseBarChart'
import type { ChartData, ChartConfig } from '@/types/charts'
import type { UsageMetrics } from '@/types/analytics'
import { chartTheme } from '@/lib/chart-theme'

interface ResponseTimeChartProps {
  data: UsageMetrics['topEndpoints']
  title?: string
  className?: string
  loading?: boolean
  error?: string
  height?: number
  showErrorRate?: boolean
  maxEndpoints?: number
}

export function ResponseTimeChart({
  data,
  title = 'Response Time by Endpoint',
  className,
  loading = false,
  error,
  height = 300,
  showErrorRate = false,
  maxEndpoints = 10,
}: ResponseTimeChartProps) {
  // Transform and sort data for chart consumption
  const sortedData = [...data]
    .sort((a, b) => b.averageResponseTime - a.averageResponseTime)
    .slice(0, maxEndpoints)

  const chartData: ChartData[] = sortedData.map((item) => ({
    endpoint: item.endpoint.replace('/api/', '').replace('/', '') || 'Root',
    responseTime: Math.round(item.averageResponseTime),
    errorRate: showErrorRate ? Math.round(item.errorRate * 100) : 0,
    requests: item.count,
  }))

  const dataKeys = [
    {
      key: 'responseTime',
      name: 'Avg Response Time (ms)',
      color: chartTheme.colors.primary,
    },
  ]

  if (showErrorRate) {
    dataKeys.push({
      key: 'errorRate',
      name: 'Error Rate (%)',
      color: chartTheme.colors.error,
    })
  }

  const config: ChartConfig = {
    title,
    xAxisKey: 'endpoint',
    dataKeys,
    showGrid: true,
    showLegend: showErrorRate,
    showTooltip: true,
    height,
    margin: { top: 5, right: 30, left: 20, bottom: 60 },
  }



  return (
    <BaseBarChart
      data={chartData}
      config={config}
      className={className}
      loading={loading}
      error={error}
      stacked={false}
    />
  )
}
import { lazy, Suspense } from 'react'
import { InlineSpinner } from '../ui/loading-spinner'

// Lazy load chart components to reduce initial bundle size
const BaseLineChart = lazy(() => import('./BaseLineChart').then(m => ({ default: m.BaseLineChart })))
const BaseBarChart = lazy(() => import('./BaseBarChart').then(m => ({ default: m.BaseBarChart })))
const BasePieChart = lazy(() => import('./BasePieChart').then(m => ({ default: m.BasePieChart })))
const BaseAreaChart = lazy(() => import('./BaseAreaChart').then(m => ({ default: m.BaseAreaChart })))

// Analytics charts
const ApiUsageOverTimeChart = lazy(() => import('./ApiUsageOverTimeChart').then(m => ({ default: m.ApiUsageOverTimeChart })))
const EndpointUsageChart = lazy(() => import('./EndpointUsageChart').then(m => ({ default: m.EndpointUsageChart })))
const ResponseTimeChart = lazy(() => import('./ResponseTimeChart').then(m => ({ default: m.ResponseTimeChart })))
const ErrorRateTrendChart = lazy(() => import('./ErrorRateTrendChart').then(m => ({ default: m.ErrorRateTrendChart })))

// Interactive components
const UsageAnalyticsDashboard = lazy(() => import('./UsageAnalyticsDashboard').then(m => ({ default: m.UsageAnalyticsDashboard })))
const InteractiveUsageAnalyticsDashboard = lazy(() => import('./InteractiveUsageAnalyticsDashboard').then(m => ({ default: m.InteractiveUsageAnalyticsDashboard })))

interface ChartLoaderProps {
  type: 'line' | 'bar' | 'pie' | 'area' | 'api-usage' | 'endpoint-usage' | 'response-time' | 'error-rate' | 'usage-dashboard' | 'interactive-dashboard'
  fallback?: React.ReactNode
  [key: string]: any
}

const ChartFallback = () => (
  <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
    <div className="flex flex-col items-center space-y-2">
      <InlineSpinner className="h-6 w-6" />
      <span className="text-sm text-gray-500">Loading chart...</span>
    </div>
  </div>
)

export function ChartLoader({ type, fallback = <ChartFallback />, ...props }: ChartLoaderProps) {
  const renderChart = () => {
    switch (type) {
      case 'line':
        return <BaseLineChart {...props} />
      case 'bar':
        return <BaseBarChart {...props} />
      case 'pie':
        return <BasePieChart {...props} />
      case 'area':
        return <BaseAreaChart {...props} />
      case 'api-usage':
        return <ApiUsageOverTimeChart {...props} />
      case 'endpoint-usage':
        return <EndpointUsageChart {...props} />
      case 'response-time':
        return <ResponseTimeChart {...props} />
      case 'error-rate':
        return <ErrorRateTrendChart {...props} />
      case 'usage-dashboard':
        return <UsageAnalyticsDashboard {...props} />
      case 'interactive-dashboard':
        return <InteractiveUsageAnalyticsDashboard {...props} />
      default:
        return <div>Unknown chart type: {type}</div>
    }
  }

  return (
    <Suspense fallback={fallback}>
      {renderChart()}
    </Suspense>
  )
}

// Export individual lazy components for direct use
export {
  BaseLineChart,
  BaseBarChart,
  BasePieChart,
  BaseAreaChart,
  ApiUsageOverTimeChart,
  EndpointUsageChart,
  ResponseTimeChart,
  ErrorRateTrendChart,
  UsageAnalyticsDashboard,
  InteractiveUsageAnalyticsDashboard,
}
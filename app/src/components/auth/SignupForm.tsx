import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { useAuth } from '../../hooks/useAuth'
import type { SignupData } from '../../types/auth'

interface SignupFormProps {
  onSuccess?: () => void
  redirectTo?: string
}

export function SignupForm({ onSuccess, redirectTo }: SignupFormProps) {
  const navigate = useNavigate()
  const { signup, isLoading, error, clearError } = useAuth()
  
  const [signupData, setSignupData] = useState<SignupData & { confirmPassword: string }>({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  })
  
  const [validationErrors, setValidationErrors] = useState<{
    name?: string
    email?: string
    password?: string
    confirmPassword?: string
  }>({})

  const validateForm = (): boolean => {
    const errors: typeof validationErrors = {}
    
    if (!signupData.name.trim()) {
      errors.name = 'Name is required'
    } else if (signupData.name.trim().length < 2) {
      errors.name = 'Name must be at least 2 characters'
    }
    
    if (!signupData.email) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(signupData.email)) {
      errors.email = 'Please enter a valid email address'
    }
    
    if (!signupData.password) {
      errors.password = 'Password is required'
    } else if (signupData.password.length < 8) {
      errors.password = 'Password must be at least 8 characters'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(signupData.password)) {
      errors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
    }
    
    if (!signupData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password'
    } else if (signupData.password !== signupData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }
    
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return
    
    clearError()
    
    try {
      const { confirmPassword, ...data } = signupData
      await signup(data)
      
      if (onSuccess) {
        onSuccess()
      } else {
        // Redirect to dashboard or provided redirect path
        const path = redirectTo || '/dashboard'
        navigate(path, { replace: true })
      }
    } catch (error) {
      // Error is handled by the auth store
      console.error('Signup failed:', error)
    }
  }

  const handleInputChange = (field: keyof typeof signupData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setSignupData(prev => ({
      ...prev,
      [field]: e.target.value
    }))
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: undefined
      }))
    }
    
    // Clear auth error when user starts typing
    if (error) {
      clearError()
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center">Create Account</CardTitle>
        <CardDescription className="text-center">
          Sign up to get started with the admin panel
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="name" className="text-sm font-medium">
              Full Name
            </label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              value={signupData.name}
              onChange={handleInputChange('name')}
              disabled={isLoading}
              className={validationErrors.name ? 'border-red-500' : ''}
              data-testid="name"
            />
            {validationErrors.name && (
              <p className="text-sm text-red-500">{validationErrors.name}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              Email
            </label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={signupData.email}
              onChange={handleInputChange('email')}
              disabled={isLoading}
              className={validationErrors.email ? 'border-red-500' : ''}
              data-testid="email"
            />
            {validationErrors.email && (
              <p className="text-sm text-red-500">{validationErrors.email}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <label htmlFor="password" className="text-sm font-medium">
              Password
            </label>
            <Input
              id="password"
              type="password"
              placeholder="Create a password"
              value={signupData.password}
              onChange={handleInputChange('password')}
              disabled={isLoading}
              className={validationErrors.password ? 'border-red-500' : ''}
              data-testid="password"
            />
            {validationErrors.password && (
              <p className="text-sm text-red-500">{validationErrors.password}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <label htmlFor="confirmPassword" className="text-sm font-medium">
              Confirm Password
            </label>
            <Input
              id="confirmPassword"
              type="password"
              placeholder="Confirm your password"
              value={signupData.confirmPassword}
              onChange={handleInputChange('confirmPassword')}
              disabled={isLoading}
              className={validationErrors.confirmPassword ? 'border-red-500' : ''}
              data-testid="confirm-password"
            />
            {validationErrors.confirmPassword && (
              <p className="text-sm text-red-500">{validationErrors.confirmPassword}</p>
            )}
          </div>
          
          {error && (
            <div className="p-3 text-sm text-red-700 bg-red-50 border border-red-200 rounded-md">
              {error.message}
            </div>
          )}
          
          <Button
            type="submit"
            className="w-full"
            disabled={isLoading}
            data-testid="signup-button"
          >
            {isLoading ? 'Creating Account...' : 'Create Account'}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
import { useState } from 'react'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { 
  UserCheck, 
  UserX, 
  Shield, 
  ShieldOff, 
  Trash2, 
  X 
} from 'lucide-react'
import type { UserActionRequest } from '../../types/user'

interface BulkActionsBarProps {
  selectedCount: number
  onBulkAction: (action: UserActionRequest['action'], reason?: string) => Promise<void>
  onClearSelection: () => void
}

export function BulkActionsBar({ 
  selectedCount, 
  onBulkAction, 
  onClearSelection 
}: BulkActionsBarProps) {
  const [isActionDialogOpen, setIsActionDialogOpen] = useState(false)
  const [currentAction, setCurrentAction] = useState<UserActionRequest['action'] | null>(null)
  const [reason, setReason] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleActionClick = (action: UserActionRequest['action']) => {
    setCurrentAction(action)
    setReason('')
    setIsActionDialogOpen(true)
  }

  const handleConfirmAction = async () => {
    if (!currentAction) return

    setIsLoading(true)
    try {
      await onBulkAction(currentAction, reason || undefined)
      setIsActionDialogOpen(false)
      setCurrentAction(null)
      setReason('')
    } catch (error) {
      console.error('Bulk action failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getActionConfig = (action: UserActionRequest['action']) => {
    const configs = {
      approve: {
        title: 'Approve Users',
        description: `Are you sure you want to approve ${selectedCount} selected users?`,
        icon: UserCheck,
        buttonText: 'Approve Users',
        buttonVariant: 'default' as const,
        color: 'text-green-600'
      },
      revoke: {
        title: 'Revoke Access',
        description: `Are you sure you want to revoke access for ${selectedCount} selected users?`,
        icon: UserX,
        buttonText: 'Revoke Access',
        buttonVariant: 'destructive' as const,
        color: 'text-orange-600',
        requiresReason: true
      },
      promote: {
        title: 'Promote to Admin',
        description: `Are you sure you want to promote ${selectedCount} selected users to admin?`,
        icon: Shield,
        buttonText: 'Promote Users',
        buttonVariant: 'default' as const,
        color: 'text-blue-600'
      },
      demote: {
        title: 'Demote to User',
        description: `Are you sure you want to demote ${selectedCount} selected admins to regular users?`,
        icon: ShieldOff,
        buttonText: 'Demote Users',
        buttonVariant: 'outline-solid' as const,
        color: 'text-blue-600'
      },
      delete: {
        title: 'Delete Users',
        description: `Are you sure you want to permanently delete ${selectedCount} selected users? This action cannot be undone.`,
        icon: Trash2,
        buttonText: 'Delete Users',
        buttonVariant: 'destructive' as const,
        color: 'text-red-600',
        requiresReason: true
      }
    }
    return configs[action]
  }

  const actionConfig = currentAction ? getActionConfig(currentAction) : null

  return (
    <>
      <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg border">
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="text-sm">
            {selectedCount} selected
          </Badge>
          <span className="text-sm text-muted-foreground">
            Bulk actions:
          </span>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleActionClick('approve')}
            className="text-green-600 hover:text-green-700"
          >
            <UserCheck className="h-4 w-4 mr-1" />
            Approve
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleActionClick('revoke')}
            className="text-orange-600 hover:text-orange-700"
          >
            <UserX className="h-4 w-4 mr-1" />
            Revoke
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleActionClick('promote')}
            className="text-blue-600 hover:text-blue-700"
          >
            <Shield className="h-4 w-4 mr-1" />
            Promote
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleActionClick('demote')}
            className="text-blue-600 hover:text-blue-700"
          >
            <ShieldOff className="h-4 w-4 mr-1" />
            Demote
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleActionClick('delete')}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4 mr-1" />
            Delete
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={onClearSelection}
            className="text-muted-foreground hover:text-foreground"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </Button>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={isActionDialogOpen} onOpenChange={setIsActionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {actionConfig && (
                <>
                  <actionConfig.icon className={`h-5 w-5 ${actionConfig.color}`} />
                  {actionConfig.title}
                </>
              )}
            </DialogTitle>
            <DialogDescription>
              {actionConfig?.description}
            </DialogDescription>
          </DialogHeader>

          {actionConfig?.requiresReason && (
            <div className="space-y-2">
              <Label htmlFor="reason">Reason (optional)</Label>
              <Input
                id="reason"
                placeholder="Enter reason for this action..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
              />
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsActionDialogOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              variant={actionConfig?.buttonVariant}
              onClick={handleConfirmAction}
              disabled={isLoading}
            >
              {isLoading ? 'Processing...' : actionConfig?.buttonText}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
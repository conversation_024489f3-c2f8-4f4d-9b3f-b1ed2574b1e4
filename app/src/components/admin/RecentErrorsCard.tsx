
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { 
  AlertTriangle, 
  Clock, 
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import { format, formatDistanceToNow } from 'date-fns'
import type { ApiUsageLog } from '../../types/analytics'

interface RecentErrorsCardProps {
  errors: ApiUsageLog[]
  isLoading: boolean
}

export function RecentErrorsCard({ errors, isLoading }: RecentErrorsCardProps) {
  const getStatusBadge = (statusCode: number) => {
    if (statusCode >= 400 && statusCode < 500) {
      return <Badge variant="warning">{statusCode}</Badge>
    }
    if (statusCode >= 500) {
      return <Badge variant="destructive">{statusCode}</Badge>
    }
    return <Badge variant="outline">{statusCode}</Badge>
  }

  const getMethodBadge = (method: string) => {
    const variants = {
      GET: 'info',
      POST: 'success',
      PUT: 'warning',
      DELETE: 'destructive',
      PATCH: 'outline-solid'
    } as const

    return (
      <Badge variant={variants[method as keyof typeof variants] || 'outline-solid'} className="text-xs">
        {method}
      </Badge>
    )
  }

  const getErrorSeverity = (statusCode: number) => {
    if (statusCode >= 500) return 'high'
    if (statusCode === 429) return 'medium'
    if (statusCode >= 400) return 'low'
    return 'low'
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600'
      case 'medium': return 'text-orange-600'
      case 'low': return 'text-yellow-600'
      default: return 'text-gray-600'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Recent Errors (Last 24h)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="space-y-2 p-3 border rounded">
                <div className="h-4 bg-muted animate-pulse rounded w-1/2" />
                <div className="h-3 bg-muted animate-pulse rounded w-3/4" />
                <div className="h-3 bg-muted animate-pulse rounded w-1/4" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (errors.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Recent Errors (Last 24h)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="text-green-600 mb-2">
              <AlertTriangle className="h-12 w-12 mx-auto opacity-50" />
            </div>
            <p className="text-sm text-muted-foreground">
              No errors in the last 24 hours. Great job! 🎉
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Group errors by status code for summary
  const errorSummary = errors.reduce((acc, error) => {
    const key = error.statusCode
    if (!acc[key]) {
      acc[key] = { count: 0, severity: getErrorSeverity(error.statusCode) }
    }
    acc[key].count++
    return acc
  }, {} as Record<number, { count: number; severity: string }>)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            Recent Errors (Last 24h)
          </div>
          <Badge variant="destructive" className="text-xs">
            {errors.length} errors
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Error Summary */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Error Summary</h4>
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(errorSummary).map(([statusCode, { count, severity }]) => (
              <div key={statusCode} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                <div className="flex items-center gap-2">
                  {getStatusBadge(parseInt(statusCode))}
                  <span className={`text-xs font-medium ${getSeverityColor(severity)}`}>
                    {severity.toUpperCase()}
                  </span>
                </div>
                <span className="text-sm font-medium">{count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Error Details */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Recent Error Details</h4>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {errors.slice(0, 10).map((error) => (
              <div key={error.id} className="p-3 border rounded-lg space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getStatusBadge(error.statusCode)}
                    {getMethodBadge(error.method)}
                    <span className="font-mono text-xs text-muted-foreground">
                      {error.endpoint}
                    </span>
                  </div>
                  <Button variant="ghost" size="icon" className="h-6 w-6">
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>

                {error.errorMessage && (
                  <div className="text-sm text-red-600 bg-red-50 p-2 rounded border-l-2 border-red-200">
                    {error.errorMessage}
                  </div>
                )}

                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-4">
                    <span>User: {error.userId}</span>
                    <span>IP: {error.ipAddress || 'N/A'}</span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {error.responseTime}ms
                    </span>
                  </div>
                  <div className="flex flex-col items-end">
                    <span>{format(new Date(error.timestamp), 'HH:mm:ss')}</span>
                    <span>{formatDistanceToNow(new Date(error.timestamp), { addSuffix: true })}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {errors.length > 10 && (
            <div className="text-center pt-2">
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Load More Errors
              </Button>
            </div>
          )}
        </div>

        {/* Error Rate Trend */}
        <div className="pt-4 border-t">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Error Rate Trend</span>
            <div className="flex items-center gap-2">
              {errors.length > 20 ? (
                <span className="text-red-600 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  High error rate
                </span>
              ) : errors.length > 10 ? (
                <span className="text-orange-600">Moderate error rate</span>
              ) : (
                <span className="text-green-600">Low error rate</span>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
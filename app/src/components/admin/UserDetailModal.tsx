import { useState } from 'react'
import { useAtomValue } from 'jotai'
import { userActivitiesAtom } from '../../stores/users'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { 
  User as UserIcon, 
  Mail, 
  Calendar, 
  Shield, 
  Key, 
  Activity,
  UserCheck,
  UserX,
  ShieldOff,
  Trash2,
  Clock
} from 'lucide-react'
import { formatDistanceToNow, format } from 'date-fns'
import type { User } from '../../types/auth'
import type { UserActionRequest } from '../../types/user'

interface UserDetailModalProps {
  user: User
  open: boolean
  onClose: () => void
  onUserAction: (userId: string, action: UserActionRequest['action'], reason?: string) => void
}

export function UserDetailModal({ 
  user, 
  open, 
  onClose, 
  onUserAction 
}: UserDetailModalProps) {
  const [isActionLoading, setIsActionLoading] = useState(false)
  const activities = useAtomValue(userActivitiesAtom)

  const handleAction = async (action: UserActionRequest['action']) => {
    setIsActionLoading(true)
    try {
      await onUserAction(user.id, action)
      onClose()
    } catch (error) {
      console.error(`Failed to ${action} user:`, error)
    } finally {
      setIsActionLoading(false)
    }
  }

  const getStatusBadge = (status: User['status']) => {
    const variants = {
      pending: 'warning',
      approved: 'success',
      revoked: 'destructive'
    } as const

    return (
      <Badge variant={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getRoleBadge = (role: User['role']) => {
    return (
      <Badge variant={role === 'admin' ? 'admin' : 'secondary'}>
        {role === 'admin' ? (
          <>
            <Shield className="h-3 w-3 mr-1" />
            Admin
          </>
        ) : (
          'User'
        )}
      </Badge>
    )
  }

  const getAccessLevelBadge = (level: User['apiAccessLevel']) => {
    const variants = {
      none: 'outline-solid',
      basic: 'info',
      premium: 'success'
    } as const

    return (
      <Badge variant={variants[level]}>
        {level.charAt(0).toUpperCase() + level.slice(1)}
      </Badge>
    )
  }

  const getActivityIcon = (action: string) => {
    switch (action) {
      case 'login':
        return <UserIcon className="h-4 w-4 text-green-600" />
      case 'logout':
        return <UserIcon className="h-4 w-4 text-gray-600" />
      case 'api_key_created':
        return <Key className="h-4 w-4 text-blue-600" />
      case 'api_key_deleted':
        return <Key className="h-4 w-4 text-red-600" />
      case 'profile_updated':
        return <UserIcon className="h-4 w-4 text-orange-600" />
      default:
        return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserIcon className="h-5 w-5" />
            User Details: {user.name}
          </DialogTitle>
          <DialogDescription>
            Comprehensive information and activity for {user.email}
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="activity">Activity</TabsTrigger>
            <TabsTrigger value="actions">Actions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-2">
                    <UserIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Name:</span>
                    <span>{user.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Email:</span>
                    <span>{user.email}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Role:</span>
                    {getRoleBadge(user.role)}
                  </div>
                  <div className="flex items-center gap-2">
                    <UserCheck className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Status:</span>
                    {getStatusBadge(user.status)}
                  </div>
                  <div className="flex items-center gap-2">
                    <Key className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">API Access:</span>
                    {getAccessLevelBadge(user.apiAccessLevel)}
                  </div>
                </CardContent>
              </Card>

              {/* Account Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Account Timeline</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Created:</span>
                    <span>{format(new Date(user.createdAt), 'PPP')}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {formatDistanceToNow(new Date(user.createdAt), { addSuffix: true })}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Updated:</span>
                    <span>{format(new Date(user.updatedAt), 'PPP')}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {formatDistanceToNow(new Date(user.updatedAt), { addSuffix: true })}
                  </div>

                  {user.lastLoginAt && (
                    <>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">Last Login:</span>
                        <span>{format(new Date(user.lastLoginAt), 'PPP')}</span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatDistanceToNow(new Date(user.lastLoginAt), { addSuffix: true })}
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="activity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                {activities.length > 0 ? (
                  <div className="space-y-4">
                    {activities.map((activity, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 rounded-lg border">
                        {getActivityIcon(activity.action)}
                        <div className="flex-1">
                          <div className="font-medium">
                            {activity.action.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {format(new Date(activity.timestamp), 'PPP p')}
                          </div>
                          {activity.metadata && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {JSON.stringify(activity.metadata, null, 2)}
                            </div>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No recent activity found for this user.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="actions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">User Actions</CardTitle>
                <div className="text-sm text-muted-foreground">
                  Perform administrative actions on this user account
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 md:grid-cols-2">
                  {user.status === 'pending' && (
                    <Button
                      onClick={() => handleAction('approve')}
                      disabled={isActionLoading}
                      className="justify-start"
                      variant="default"
                    >
                      <UserCheck className="h-4 w-4 mr-2" />
                      Approve User
                    </Button>
                  )}

                  {user.status === 'approved' && (
                    <Button
                      onClick={() => handleAction('revoke')}
                      disabled={isActionLoading}
                      className="justify-start"
                      variant="destructive"
                    >
                      <UserX className="h-4 w-4 mr-2" />
                      Revoke Access
                    </Button>
                  )}

                  {user.role === 'user' && (
                    <Button
                      onClick={() => handleAction('promote')}
                      disabled={isActionLoading}
                      className="justify-start"
                      variant="outline"
                    >
                      <Shield className="h-4 w-4 mr-2" />
                      Promote to Admin
                    </Button>
                  )}

                  {user.role === 'admin' && (
                    <Button
                      onClick={() => handleAction('demote')}
                      disabled={isActionLoading}
                      className="justify-start"
                      variant="outline"
                    >
                      <ShieldOff className="h-4 w-4 mr-2" />
                      Demote to User
                    </Button>
                  )}

                  <Button
                    onClick={() => handleAction('delete')}
                    disabled={isActionLoading}
                    className="justify-start"
                    variant="destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete User
                  </Button>
                </div>

                {isActionLoading && (
                  <div className="mt-4 text-sm text-muted-foreground">
                    Processing action...
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
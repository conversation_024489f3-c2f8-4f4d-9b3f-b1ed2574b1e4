
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card'
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import { format } from 'date-fns'
import type { TrendData } from '../../types/analytics'

interface ApiUsageChartsProps {
  requestTrends: TrendData[]
  responseTimeTrends: TrendData[]
  errorTrends: TrendData[]
  userTrends: TrendData[]
  isLoading: boolean
}

export function ApiUsageCharts({
  requestTrends,
  responseTimeTrends,
  errorTrends,
  userTrends,
  isLoading
}: ApiUsageChartsProps) {
  const formatXAxisTick = (tickItem: any) => {
    return format(new Date(tickItem), 'MMM dd')
  }

  const formatTooltipLabel = (label: any) => {
    return format(new Date(label), 'PPP')
  }



  if (isLoading) {
    return (
      <div className="grid gap-6 md:grid-cols-2">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <CardTitle>
                <div className="h-5 bg-muted animate-pulse rounded w-1/3" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Combine data for multi-metric charts
  const combinedData = requestTrends.map((item, index) => ({
    timestamp: item.timestamp,
    requests: item.value,
    responseTime: responseTimeTrends[index]?.value || 0,
    errors: errorTrends[index]?.value || 0,
    users: userTrends[index]?.value || 0
  }))

  // Sample endpoint distribution data (would come from API in real implementation)
  const endpointData = [
    { name: '/api/ollama/generate', value: 45, color: '#0088FE' },
    { name: '/api/ollama/chat', value: 30, color: '#00C49F' },
    { name: '/api/users', value: 15, color: '#FFBB28' },
    { name: '/api/keys', value: 7, color: '#FF8042' },
    { name: 'Others', value: 3, color: '#8884D8' }
  ]

  return (
    <div className="grid gap-6 md:grid-cols-2">
      {/* Request Volume Over Time */}
      <Card>
        <CardHeader>
          <CardTitle>Request Volume Over Time</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={combinedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatXAxisTick}
              />
              <YAxis />
              <Tooltip 
                labelFormatter={formatTooltipLabel}
                formatter={(value: number) => [value, 'Requests']}
              />
              <Area 
                type="monotone" 
                dataKey="requests" 
                stroke="#0088FE" 
                fill="#0088FE" 
                fillOpacity={0.3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Response Time Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Average Response Time</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={combinedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatXAxisTick}
              />
              <YAxis />
              <Tooltip 
                labelFormatter={formatTooltipLabel}
                formatter={(value: number) => [`${value}ms`, 'Response Time']}
              />
              <Line 
                type="monotone" 
                dataKey="responseTime" 
                stroke="#00C49F" 
                strokeWidth={2}
                dot={{ fill: '#00C49F', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Error Rate Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Error Rate Over Time</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={combinedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatXAxisTick}
              />
              <YAxis />
              <Tooltip 
                labelFormatter={formatTooltipLabel}
                formatter={(value: number) => [value, 'Errors']}
              />
              <Bar 
                dataKey="errors" 
                fill="#FF8042"
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Endpoint Usage Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Endpoint Usage Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={endpointData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {endpointData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value: number) => [value, 'Requests']}
              />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Active Users Over Time */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Active Users & System Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={combinedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tickFormatter={formatXAxisTick}
              />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip 
                labelFormatter={formatTooltipLabel}
                formatter={(value: number, name: string) => {
                  if (name === 'users') return [value, 'Active Users']
                  if (name === 'requests') return [value, 'Requests']
                  return [value, name]
                }}
              />
              <Line 
                yAxisId="left"
                type="monotone" 
                dataKey="users" 
                stroke="#8884D8" 
                strokeWidth={2}
                dot={{ fill: '#8884D8', strokeWidth: 2, r: 4 }}
              />
              <Line 
                yAxisId="right"
                type="monotone" 
                dataKey="requests" 
                stroke="#0088FE" 
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={{ fill: '#0088FE', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  )
}
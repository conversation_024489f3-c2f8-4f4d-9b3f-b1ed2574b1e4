
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/table'
import { Badge } from '../ui/badge'
import { Button } from '../ui/button'
import { 
  ExternalLink,
  Clock,
  Database,
  AlertCircle
} from 'lucide-react'
import { format, formatDistanceToNow } from 'date-fns'
import type { ApiUsageLog } from '../../types/analytics'

interface ApiUsageTableProps {
  logs: ApiUsageLog[]
  isLoading: boolean
}

export function ApiUsageTable({ logs, isLoading }: ApiUsageTableProps) {
  const getStatusBadge = (statusCode: number) => {
    if (statusCode >= 200 && statusCode < 300) {
      return <Badge variant="success">{statusCode}</Badge>
    }
    if (statusCode >= 300 && statusCode < 400) {
      return <Badge variant="info">{statusCode}</Badge>
    }
    if (statusCode >= 400 && statusCode < 500) {
      return <Badge variant="warning">{statusCode}</Badge>
    }
    if (statusCode >= 500) {
      return <Badge variant="destructive">{statusCode}</Badge>
    }
    return <Badge variant="outline">{statusCode}</Badge>
  }

  const getMethodBadge = (method: string) => {
    const variants = {
      GET: 'info',
      POST: 'success',
      PUT: 'warning',
      DELETE: 'destructive',
      PATCH: 'outline-solid'
    } as const

    return (
      <Badge variant={variants[method as keyof typeof variants] || 'outline-solid'}>
        {method}
      </Badge>
    )
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const getResponseTimeColor = (responseTime: number) => {
    if (responseTime < 200) return 'text-green-600'
    if (responseTime < 500) return 'text-yellow-600'
    if (responseTime < 1000) return 'text-orange-600'
    return 'text-red-600'
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (logs.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No API usage logs found for the selected filters.
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Timestamp</TableHead>
            <TableHead>Method</TableHead>
            <TableHead>Endpoint</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Response Time</TableHead>
            <TableHead>Data Size</TableHead>
            <TableHead>User</TableHead>
            <TableHead>IP Address</TableHead>
            <TableHead>Error</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {logs.map((log) => (
            <TableRow key={log.id}>
              <TableCell>
                <div className="flex flex-col">
                  <span className="text-sm font-medium">
                    {format(new Date(log.timestamp), 'HH:mm:ss')}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(log.timestamp), { addSuffix: true })}
                  </span>
                </div>
              </TableCell>
              
              <TableCell>
                {getMethodBadge(log.method)}
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2 max-w-xs">
                  <span className="truncate font-mono text-sm">
                    {log.endpoint}
                  </span>
                  <Button variant="ghost" size="icon" className="h-4 w-4">
                    <ExternalLink className="h-3 w-3" />
                  </Button>
                </div>
              </TableCell>
              
              <TableCell>
                {getStatusBadge(log.statusCode)}
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span className={`text-sm font-medium ${getResponseTimeColor(log.responseTime)}`}>
                    {log.responseTime}ms
                  </span>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-1">
                  <Database className="h-3 w-3 text-muted-foreground" />
                  <div className="text-sm">
                    <div>{formatBytes(log.requestSize + log.responseSize)}</div>
                    <div className="text-xs text-muted-foreground">
                      ↑{formatBytes(log.requestSize)} ↓{formatBytes(log.responseSize)}
                    </div>
                  </div>
                </div>
              </TableCell>
              
              <TableCell>
                <div className="text-sm">
                  <div className="font-medium">{log.userId}</div>
                  <div className="text-xs text-muted-foreground truncate max-w-24">
                    {log.apiKeyId}
                  </div>
                </div>
              </TableCell>
              
              <TableCell>
                <span className="text-sm font-mono">
                  {log.ipAddress || 'N/A'}
                </span>
              </TableCell>
              
              <TableCell>
                {log.errorMessage ? (
                  <div className="flex items-start gap-1 max-w-xs">
                    <AlertCircle className="h-3 w-3 text-red-500 mt-0.5 shrink-0" />
                    <span className="text-xs text-red-600 truncate">
                      {log.errorMessage}
                    </span>
                  </div>
                ) : (
                  <span className="text-xs text-muted-foreground">-</span>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {logs.length >= 100 && (
        <div className="text-center py-4 text-sm text-muted-foreground">
          Showing first 100 results. Use filters to narrow down the results.
        </div>
      )}
    </div>
  )
}
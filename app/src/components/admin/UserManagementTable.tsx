import React from 'react'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '../ui/table'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '../ui/dropdown-menu'
import { Checkbox } from '../ui/checkbox'
import { 
  MoreHorizontal, 
  Eye, 
  UserCheck, 
  UserX, 
  Shield, 
  ShieldOff,
  Trash2,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import type { User } from '../../types/auth'
import type { UserActionRequest } from '../../types/user'
import type { PaginationParams } from '../../types/api'

interface UserManagementTableProps {
  users: User[]
  selectedUsers: string[]
  pagination: PaginationParams
  isLoading: boolean
  onUserSelect: (userId: string, selected: boolean) => void
  onSelectAll: (selected: boolean) => void
  onUserAction: (userId: string, action: UserActionRequest['action'], reason?: string) => void
  onViewDetails: (user: User) => void
  onPaginationChange: (pagination: Partial<PaginationParams>) => void
}

export function UserManagementTable({
  users,
  selectedUsers,
  pagination,
  isLoading,
  onUserSelect,
  onSelectAll,
  onUserAction,
  onViewDetails,
  onPaginationChange
}: UserManagementTableProps) {
  const allSelected = users.length > 0 && selectedUsers.length === users.length

  const getStatusBadge = (status: User['status']) => {
    const variants = {
      pending: 'warning',
      approved: 'success', 
      revoked: 'destructive'
    } as const

    return (
      <Badge variant={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getRoleBadge = (role: User['role']) => {
    return (
      <Badge variant={role === 'admin' ? 'admin' : 'secondary'}>
        {role === 'admin' ? (
          <>
            <Shield className="h-3 w-3 mr-1" />
            Admin
          </>
        ) : (
          'User'
        )}
      </Badge>
    )
  }

  const getAccessLevelBadge = (level: User['apiAccessLevel']) => {
    const variants = {
      none: 'outline-solid',
      basic: 'info',
      premium: 'success'
    } as const

    return (
      <Badge variant={variants[level]}>
        {level.charAt(0).toUpperCase() + level.slice(1)}
      </Badge>
    )
  }

  const handleSort = (field: keyof User) => {
    const newSortOrder = pagination.sortBy === field && pagination.sortOrder === 'asc' ? 'desc' : 'asc'
    onPaginationChange({
      sortBy: field,
      sortOrder: newSortOrder
    })
  }

  const SortableHeader = ({ field, children }: { field: keyof User; children: React.ReactNode }) => (
    <TableHead 
      className="cursor-pointer hover:bg-muted/50 select-none"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center gap-2">
        {children}
        <ArrowUpDown className="h-4 w-4" />
      </div>
    </TableHead>
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={allSelected}

                onCheckedChange={onSelectAll}
                aria-label="Select all users"
              />
            </TableHead>
            <SortableHeader field="name">Name</SortableHeader>
            <SortableHeader field="email">Email</SortableHeader>
            <SortableHeader field="role">Role</SortableHeader>
            <SortableHeader field="status">Status</SortableHeader>
            <SortableHeader field="apiAccessLevel">Access Level</SortableHeader>
            <SortableHeader field="createdAt">Created</SortableHeader>
            <SortableHeader field="lastLoginAt">Last Login</SortableHeader>
            <TableHead className="w-12">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id}>
              <TableCell>
                <Checkbox
                  checked={selectedUsers.includes(user.id)}
                  onCheckedChange={(checked) => onUserSelect(user.id, !!checked)}
                  aria-label={`Select ${user.name}`}
                />
              </TableCell>
              <TableCell className="font-medium">{user.name}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>{getRoleBadge(user.role)}</TableCell>
              <TableCell>{getStatusBadge(user.status)}</TableCell>
              <TableCell>{getAccessLevelBadge(user.apiAccessLevel)}</TableCell>
              <TableCell>
                {formatDistanceToNow(new Date(user.createdAt), { addSuffix: true })}
              </TableCell>
              <TableCell>
                {user.lastLoginAt 
                  ? formatDistanceToNow(new Date(user.lastLoginAt), { addSuffix: true })
                  : 'Never'
                }
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Open menu</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onViewDetails(user)}>
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    
                    {user.status === 'pending' && (
                      <DropdownMenuItem 
                        onClick={() => onUserAction(user.id, 'approve')}
                        className="text-green-600"
                      >
                        <UserCheck className="h-4 w-4 mr-2" />
                        Approve User
                      </DropdownMenuItem>
                    )}
                    
                    {user.status === 'approved' && (
                      <DropdownMenuItem 
                        onClick={() => onUserAction(user.id, 'revoke')}
                        className="text-orange-600"
                      >
                        <UserX className="h-4 w-4 mr-2" />
                        Revoke Access
                      </DropdownMenuItem>
                    )}
                    
                    {user.role === 'user' && (
                      <DropdownMenuItem 
                        onClick={() => onUserAction(user.id, 'promote')}
                        className="text-blue-600"
                      >
                        <Shield className="h-4 w-4 mr-2" />
                        Promote to Admin
                      </DropdownMenuItem>
                    )}
                    
                    {user.role === 'admin' && (
                      <DropdownMenuItem 
                        onClick={() => onUserAction(user.id, 'demote')}
                        className="text-blue-600"
                      >
                        <ShieldOff className="h-4 w-4 mr-2" />
                        Demote to User
                      </DropdownMenuItem>
                    )}
                    
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => onUserAction(user.id, 'delete')}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete User
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
          {Math.min(pagination.page * pagination.limit, users.length)} of {users.length} users
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPaginationChange({ page: pagination.page - 1 })}
            disabled={pagination.page <= 1}
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <div className="text-sm">
            Page {pagination.page}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPaginationChange({ page: pagination.page + 1 })}
            disabled={users.length < pagination.limit}
          >
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
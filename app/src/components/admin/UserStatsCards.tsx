
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '../ui/card'
import { Badge } from '../ui/badge'
import {
  Users,
  UserCheck,
  Shield,
  Clock,
  TrendingUp
} from 'lucide-react'

interface UserStatsCardsProps {
  stats: {
    totalUsers: number
    pendingApprovals: number
    activeUsers: number
    adminUsers: number
    recentSignups: number
  } | null
}

export function UserStatsCards({ stats }: UserStatsCardsProps) {
  if (!stats) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 bg-muted animate-pulse rounded" />
              </CardTitle>
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted animate-pulse rounded mb-1" />
              <div className="h-3 bg-muted animate-pulse rounded w-2/3" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const cards = [
    {
      title: 'Total Users',
      value: stats.totalUsers,
      icon: Users,
      description: 'All registered users',
      color: 'text-blue-600'
    },
    {
      title: 'Pending Approvals',
      value: stats.pendingApprovals,
      icon: Clock,
      description: 'Awaiting approval',
      color: 'text-orange-600',
      badge: stats.pendingApprovals > 0 ? 'warning' : undefined
    },
    {
      title: 'Active Users',
      value: stats.activeUsers,
      icon: UserCheck,
      description: 'Approved users',
      color: 'text-green-600'
    },
    {
      title: 'Administrators',
      value: stats.adminUsers,
      icon: Shield,
      description: 'Admin users',
      color: 'text-purple-600'
    },
    {
      title: 'Recent Signups',
      value: stats.recentSignups,
      icon: TrendingUp,
      description: 'Last 7 days',
      color: 'text-indigo-600'
    },
    {
      title: 'Approval Rate',
      value: `${stats.totalUsers > 0 ? Math.round((stats.activeUsers / stats.totalUsers) * 100) : 0}%`,
      icon: UserCheck,
      description: 'Users approved',
      color: 'text-emerald-600'
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
      {cards.map((card, index) => {
        const Icon = card.icon
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className="flex items-center gap-2">
                {card.badge && (
                  <Badge variant={card.badge as any} className="text-xs">
                    {card.value}
                  </Badge>
                )}
                <Icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <p className="text-xs text-muted-foreground">
                {card.description}
              </p>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
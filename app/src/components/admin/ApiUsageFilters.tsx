
import { Input } from '../ui/input'
import { Button } from '../ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Badge } from '../ui/badge'

import { 
  X, 
  Search, 
  Filter, 
  Calendar as CalendarIcon,
  Clock
} from 'lucide-react'
import { format } from 'date-fns'
import type { AnalyticsFilters } from '../../types/analytics'

interface ApiUsageFiltersProps {
  filters: AnalyticsFilters
  onFiltersChange: (filters: Partial<AnalyticsFilters>) => void
  onTimeRangePreset: (preset: 'hour' | 'day' | 'week' | 'month' | 'quarter' | 'year') => void
}

export function ApiUsageFilters({ 
  filters, 
  onFiltersChange, 
  onTimeRangePreset 
}: ApiUsageFiltersProps) {


  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value })
  }

  const handleEndpointChange = (value: string) => {
    const updates: Partial<AnalyticsFilters> = {}
    if (value !== 'all') {
      updates.endpoint = value
    }
    onFiltersChange(updates)
  }

  const handleMethodChange = (value: string) => {
    const updates: Partial<AnalyticsFilters> = {}
    if (value !== 'all') {
      updates.method = value
    }
    onFiltersChange(updates)
  }

  const handleStatusCodeChange = (value: string) => {
    const updates: Partial<AnalyticsFilters> = {}
    if (value !== 'all') {
      updates.statusCode = parseInt(value)
    }
    onFiltersChange(updates)
  }

  const handleUserIdChange = (value: string) => {
    const updates: Partial<AnalyticsFilters> = {}
    if (value) {
      updates.userId = value
    }
    onFiltersChange(updates)
  }

  const handleApiKeyIdChange = (value: string) => {
    const updates: Partial<AnalyticsFilters> = {}
    if (value) {
      updates.apiKeyId = value
    }
    onFiltersChange(updates)
  }

  const clearFilters = () => {
    onFiltersChange({
      search: ''
    })
  }

  const hasActiveFilters = 
    filters.search ||
    filters.endpoint ||
    filters.method ||
    filters.statusCode ||
    filters.userId ||
    filters.apiKeyId

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.search) count++
    if (filters.endpoint) count++
    if (filters.method) count++
    if (filters.statusCode) count++
    if (filters.userId) count++
    if (filters.apiKeyId) count++
    return count
  }

  const timeRangePresets = [
    { label: 'Last Hour', value: 'hour' as const },
    { label: 'Last 24 Hours', value: 'day' as const },
    { label: 'Last Week', value: 'week' as const },
    { label: 'Last Month', value: 'month' as const },
    { label: 'Last Quarter', value: 'quarter' as const },
    { label: 'Last Year', value: 'year' as const }
  ]

  return (
    <div className="space-y-4">
      {/* Time Range Presets */}
      <div className="flex flex-wrap gap-2">
        <div className="flex items-center gap-2 text-sm font-medium">
          <Clock className="h-4 w-4" />
          Quick ranges:
        </div>
        {timeRangePresets.map((preset) => (
          <Button
            key={preset.value}
            variant="outline"
            size="sm"
            onClick={() => onTimeRangePreset(preset.value)}
          >
            {preset.label}
          </Button>
        ))}
      </div>

      {/* Current Time Range Display */}
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <CalendarIcon className="h-4 w-4" />
        <span>
          {format(filters.timeRange.start, 'PPP')} - {format(filters.timeRange.end, 'PPP')}
        </span>
      </div>

      {/* Search and Filters */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search endpoints, errors..."
            value={filters.search || ''}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Endpoint Filter */}
        <Select value={filters.endpoint || 'all'} onValueChange={handleEndpointChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by endpoint" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Endpoints</SelectItem>
            <SelectItem value="/api/ollama/generate">/api/ollama/generate</SelectItem>
            <SelectItem value="/api/ollama/chat">/api/ollama/chat</SelectItem>
            <SelectItem value="/api/ollama/embeddings">/api/ollama/embeddings</SelectItem>
            <SelectItem value="/api/users">/api/users</SelectItem>
            <SelectItem value="/api/keys">/api/keys</SelectItem>
          </SelectContent>
        </Select>

        {/* Method Filter */}
        <Select value={filters.method || 'all'} onValueChange={handleMethodChange}>
          <SelectTrigger>
            <SelectValue placeholder="Filter by method" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Methods</SelectItem>
            <SelectItem value="GET">GET</SelectItem>
            <SelectItem value="POST">POST</SelectItem>
            <SelectItem value="PUT">PUT</SelectItem>
            <SelectItem value="DELETE">DELETE</SelectItem>
            <SelectItem value="PATCH">PATCH</SelectItem>
          </SelectContent>
        </Select>

        {/* Status Code Filter */}
        <Select 
          value={filters.statusCode?.toString() || 'all'} 
          onValueChange={handleStatusCodeChange}
        >
          <SelectTrigger>
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status Codes</SelectItem>
            <SelectItem value="200">200 - OK</SelectItem>
            <SelectItem value="201">201 - Created</SelectItem>
            <SelectItem value="400">400 - Bad Request</SelectItem>
            <SelectItem value="401">401 - Unauthorized</SelectItem>
            <SelectItem value="403">403 - Forbidden</SelectItem>
            <SelectItem value="404">404 - Not Found</SelectItem>
            <SelectItem value="429">429 - Rate Limited</SelectItem>
            <SelectItem value="500">500 - Server Error</SelectItem>
          </SelectContent>
        </Select>

        {/* User ID Filter */}
        <Input
          placeholder="Filter by User ID"
          value={filters.userId || ''}
          onChange={(e) => handleUserIdChange(e.target.value)}
        />

        {/* API Key ID Filter */}
        <Input
          placeholder="Filter by API Key ID"
          value={filters.apiKeyId || ''}
          onChange={(e) => handleApiKeyIdChange(e.target.value)}
        />
      </div>

      {/* Active Filters and Clear */}
      {hasActiveFilters && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 flex-wrap">
            <div className="flex items-center gap-1">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Active filters:</span>
            </div>
            
            {filters.search && (
              <Badge variant="secondary" className="gap-1">
                Search: "{filters.search}"
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleSearchChange('')}
                />
              </Badge>
            )}
            
            {filters.endpoint && (
              <Badge variant="secondary" className="gap-1">
                Endpoint: {filters.endpoint}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleEndpointChange('all')}
                />
              </Badge>
            )}
            
            {filters.method && (
              <Badge variant="secondary" className="gap-1">
                Method: {filters.method}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleMethodChange('all')}
                />
              </Badge>
            )}
            
            {filters.statusCode && (
              <Badge variant="secondary" className="gap-1">
                Status: {filters.statusCode}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleStatusCodeChange('all')}
                />
              </Badge>
            )}
            
            {filters.userId && (
              <Badge variant="secondary" className="gap-1">
                User: {filters.userId}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleUserIdChange('')}
                />
              </Badge>
            )}
            
            {filters.apiKeyId && (
              <Badge variant="secondary" className="gap-1">
                API Key: {filters.apiKeyId}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => handleApiKeyIdChange('')}
                />
              </Badge>
            )}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="text-muted-foreground hover:text-foreground"
          >
            Clear all ({getActiveFilterCount()})
          </Button>
        </div>
      )}
    </div>
  )
}
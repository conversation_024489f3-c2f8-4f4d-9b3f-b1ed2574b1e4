import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Checkbox } from '../ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Label } from '../ui/label'
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  FileImage,
  Calendar
} from 'lucide-react'
import { format } from 'date-fns'
import type { AnalyticsFilters } from '../../types/analytics'

interface ExportReportsDialogProps {
  open: boolean
  onClose: () => void
  filters: AnalyticsFilters
  stats: {
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    averageResponseTime: number
    totalDataTransferred: number
    uniqueUsers: number
    uniqueEndpoints: number
    errorRate: number
    successRate: number
  }
}

export function ExportReportsDialog({ 
  open, 
  onClose, 
  filters, 
  stats 
}: ExportReportsDialogProps) {
  const [exportFormat, setExportFormat] = useState<'pdf' | 'csv' | 'json' | 'xlsx'>('pdf')
  const [selectedSections, setSelectedSections] = useState({
    summary: true,
    charts: true,
    logs: true,
    performance: true,
    errors: true
  })
  const [isExporting, setIsExporting] = useState(false)

  const handleSectionToggle = (section: keyof typeof selectedSections) => {
    setSelectedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const handleExport = async () => {
    setIsExporting(true)
    
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // In a real implementation, this would call an API endpoint
      // const exportData = await apiClient.exportReport({
      //   format: exportFormat,
      //   sections: selectedSections,
      //   filters,
      //   stats
      // })
      
      // Create a mock download
      const filename = `api-usage-report-${format(new Date(), 'yyyy-MM-dd')}.${exportFormat}`
      console.log(`Exporting report: ${filename}`)
      
      onClose()
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setIsExporting(false)
    }
  }

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <FileText className="h-4 w-4" />
      case 'csv': case 'xlsx': return <FileSpreadsheet className="h-4 w-4" />
      case 'json': return <FileText className="h-4 w-4" />
      default: return <FileImage className="h-4 w-4" />
    }
  }

  const getSelectedSectionCount = () => {
    return Object.values(selectedSections).filter(Boolean).length
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Usage Report
          </DialogTitle>
          <DialogDescription>
            Generate and download a comprehensive API usage report
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Report Period */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Report Period</Label>
            <div className="flex items-center gap-2 text-sm text-muted-foreground bg-muted/50 p-2 rounded">
              <Calendar className="h-4 w-4" />
              <span>
                {format(filters.timeRange.start, 'MMM dd, yyyy')} - {format(filters.timeRange.end, 'MMM dd, yyyy')}
              </span>
            </div>
          </div>

          {/* Export Format */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Export Format</Label>
            <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pdf">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    PDF Report
                  </div>
                </SelectItem>
                <SelectItem value="xlsx">
                  <div className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    Excel Spreadsheet
                  </div>
                </SelectItem>
                <SelectItem value="csv">
                  <div className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    CSV Data
                  </div>
                </SelectItem>
                <SelectItem value="json">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    JSON Data
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Report Sections */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              Include Sections ({getSelectedSectionCount()}/5)
            </Label>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="summary"
                  checked={selectedSections.summary}
                  onCheckedChange={() => handleSectionToggle('summary')}
                />
                <Label htmlFor="summary" className="text-sm">
                  Usage Summary & Statistics
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="charts"
                  checked={selectedSections.charts}
                  onCheckedChange={() => handleSectionToggle('charts')}
                />
                <Label htmlFor="charts" className="text-sm">
                  Charts & Visualizations
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="logs"
                  checked={selectedSections.logs}
                  onCheckedChange={() => handleSectionToggle('logs')}
                />
                <Label htmlFor="logs" className="text-sm">
                  Request Logs & Details
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="performance"
                  checked={selectedSections.performance}
                  onCheckedChange={() => handleSectionToggle('performance')}
                />
                <Label htmlFor="performance" className="text-sm">
                  Performance Metrics
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="errors"
                  checked={selectedSections.errors}
                  onCheckedChange={() => handleSectionToggle('errors')}
                />
                <Label htmlFor="errors" className="text-sm">
                  Error Analysis
                </Label>
              </div>
            </div>
          </div>

          {/* Report Preview */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Report Preview</Label>
            <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded space-y-1">
              <div>• {stats.totalRequests.toLocaleString()} total requests</div>
              <div>• {stats.uniqueUsers} unique users</div>
              <div>• {stats.uniqueEndpoints} endpoints accessed</div>
              <div>• {(stats.errorRate * 100).toFixed(1)}% error rate</div>
              <div>• {stats.averageResponseTime}ms avg response time</div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isExporting}>
            Cancel
          </Button>
          <Button 
            onClick={handleExport} 
            disabled={isExporting || getSelectedSectionCount() === 0}
          >
            {getFormatIcon(exportFormat)}
            {isExporting ? 'Generating...' : `Export ${exportFormat.toUpperCase()}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
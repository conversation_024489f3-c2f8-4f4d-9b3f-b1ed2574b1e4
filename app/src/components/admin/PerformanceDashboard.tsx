import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import {
  Activity,
  Zap,
  TrendingUp,
  AlertTriangle,
  RefreshCw,
  Download,
  Trash2
} from 'lucide-react'
import { getPerformanceSummary, performanceMonitor } from '../../lib/performance-monitor'
import ErrorBoundary from '../ErrorBoundary'

interface PerformanceMetric {
  name: string
  value: number
  status: 'good' | 'needs-improvement' | 'poor'
  threshold: { good: number; poor: number }
  unit: string
}

export function PerformanceDashboard() {
  const [performanceData, setPerformanceData] = useState<any>(null)
  const [errorReports, setErrorReports] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const refreshData = () => {
    setIsLoading(true)
    
    // Get performance summary
    const summary = getPerformanceSummary()
    setPerformanceData(summary)
    
    // Get error reports
    const reports = ErrorBoundary.getErrorReports()
    setErrorReports(reports)
    
    setIsLoading(false)
  }

  useEffect(() => {
    refreshData()
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(refreshData, 30000)
    return () => clearInterval(interval)
  }, [])

  const exportPerformanceData = () => {
    const data = performanceMonitor.exportMetrics()
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-data-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const clearErrorReports = () => {
    ErrorBoundary.clearErrorReports()
    setErrorReports([])
  }



  const getMetricStatus = (value: number, thresholds: { good: number; poor: number }): 'good' | 'needs-improvement' | 'poor' => {
    if (value <= thresholds.good) return 'good'
    if (value <= thresholds.poor) return 'needs-improvement'
    return 'poor'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800'
      case 'needs-improvement': return 'bg-yellow-100 text-yellow-800'
      case 'poor': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const webVitalsMetrics: PerformanceMetric[] = performanceData?.webVitals ? [
    {
      name: 'Largest Contentful Paint (LCP)',
      value: performanceData.webVitals.LCP?.value || 0,
      status: getMetricStatus(performanceData.webVitals.LCP?.value || 0, { good: 2500, poor: 4000 }),
      threshold: { good: 2500, poor: 4000 },
      unit: 'ms'
    },
    {
      name: 'First Input Delay (FID)',
      value: performanceData.webVitals.FID?.value || 0,
      status: getMetricStatus(performanceData.webVitals.FID?.value || 0, { good: 100, poor: 300 }),
      threshold: { good: 100, poor: 300 },
      unit: 'ms'
    },
    {
      name: 'Cumulative Layout Shift (CLS)',
      value: performanceData.webVitals.CLS?.value || 0,
      status: getMetricStatus(performanceData.webVitals.CLS?.value || 0, { good: 0.1, poor: 0.25 }),
      threshold: { good: 0.1, poor: 0.25 },
      unit: ''
    },
    {
      name: 'Time to First Byte (TTFB)',
      value: performanceData.webVitals.TTFB?.value || 0,
      status: getMetricStatus(performanceData.webVitals.TTFB?.value || 0, { good: 800, poor: 1800 }),
      threshold: { good: 800, poor: 1800 },
      unit: 'ms'
    }
  ] : []

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Loading performance data...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Performance Dashboard</h2>
          <p className="text-muted-foreground">
            Monitor application performance and user experience metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={exportPerformanceData}>
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="web-vitals">Web Vitals</TabsTrigger>
          <TabsTrigger value="charts">Chart Performance</TabsTrigger>
          <TabsTrigger value="user-activity">User Activity</TabsTrigger>
          <TabsTrigger value="errors">Error Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Metrics</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{performanceData?.totalMetrics || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Performance measurements
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Chart Renders</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{performanceData?.totalChartRenders || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Chart rendering events
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">User Interactions</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{performanceData?.totalInteractions || 0}</div>
                <p className="text-xs text-muted-foreground">
                  User engagement events
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Error Reports</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{errorReports.length}</div>
                <p className="text-xs text-muted-foreground">
                  Reported errors
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="web-vitals" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {webVitalsMetrics.map((metric) => (
              <Card key={metric.name}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
                    <Badge className={getStatusColor(metric.status)}>
                      {metric.status.replace('-', ' ')}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {metric.value.toFixed(metric.unit === 'ms' ? 0 : 3)}{metric.unit}
                  </div>
                  <div className="text-xs text-muted-foreground mt-2">
                    Good: ≤{metric.threshold.good}{metric.unit} | 
                    Poor: &gt;{metric.threshold.poor}{metric.unit}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="charts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Chart Performance Summary</CardTitle>
              <CardDescription>
                Rendering performance metrics for different chart types
              </CardDescription>
            </CardHeader>
            <CardContent>
              {performanceData?.chartPerformance && Object.keys(performanceData.chartPerformance).length > 0 ? (
                <div className="space-y-4">
                  {Object.entries(performanceData.chartPerformance).map(([chartType, metrics]: [string, any]) => (
                    <div key={chartType} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">{chartType}</div>
                        <div className="text-sm text-muted-foreground">
                          {metrics.totalRenders} renders
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">
                          {metrics.averageRenderTime.toFixed(0)}ms avg
                        </div>
                        {metrics.slowRenders > 0 && (
                          <div className="text-sm text-red-600">
                            {metrics.slowRenders} slow renders
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  No chart performance data available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="user-activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Activity Summary</CardTitle>
              <CardDescription>
                Recent user interactions and engagement patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              {performanceData?.userActivity && Object.keys(performanceData.userActivity).length > 0 ? (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {Object.entries(performanceData.userActivity).map(([activity, count]: [string, any]) => (
                    <div key={activity} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="font-medium capitalize">
                        {activity.replace('_', ' ')}
                      </div>
                      <div className="text-2xl font-bold">{count}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  No user activity data available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Error Reports</CardTitle>
                  <CardDescription>
                    Recent application errors and crashes
                  </CardDescription>
                </div>
                {errorReports.length > 0 && (
                  <Button variant="outline" size="sm" onClick={clearErrorReports}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear Reports
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {errorReports.length > 0 ? (
                <div className="space-y-4">
                  {errorReports.slice(0, 10).map((report) => (
                    <div key={report.errorId} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium text-red-600">
                          {report.message}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {new Date(report.timestamp).toLocaleString()}
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        ID: {report.errorId}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        URL: {report.url}
                      </div>
                      {report.stack && (
                        <details className="mt-2">
                          <summary className="cursor-pointer text-sm">Stack Trace</summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto max-h-32">
                            {report.stack}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  No error reports found
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
# shadcn/ui Components

This directory contains the shadcn/ui components customized for the Home Server Admin Panel. All components are built with accessibility in mind and follow the design system tokens defined in the Tailwind configuration.

## Available Components

### Core Components
- **Button** - Primary action component with multiple variants (default, admin, success, warning, info, destructive)
- **Input** - Form input component with consistent styling
- **Card** - Content container with header, content, and footer sections
- **Table** - Data table components with sorting and filtering support
- **Dialog** - Modal dialog component for forms and confirmations
- **Badge** - Status indicators and labels
- **Tabs** - Navigation between related content
- **Select** - Dropdown selection component

### Notification Components
- **Toast** - Notification system with multiple variants

## Usage Examples

### Button Component
```tsx
import { Button } from '@/components/ui/button'

// Primary button
<Button>Click me</Button>

// Admin variant
<Button variant="admin">Admin Action</Button>

// Success variant
<Button variant="success">Approve</Button>

// Destructive variant
<Button variant="destructive">Delete</Button>
```

### Card Component
```tsx
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'

<Card>
  <CardHeader>
    <CardTitle>User Statistics</CardTitle>
  </CardHeader>
  <CardContent>
    <p>Content goes here</p>
  </CardContent>
</Card>
```

### Table Component
```tsx
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from '@/components/ui/table'

<Table>
  <TableHeader>
    <TableRow>
      <TableHead>Name</TableHead>
      <TableHead>Status</TableHead>
      <TableHead>Actions</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    <TableRow>
      <TableCell>John Doe</TableCell>
      <TableCell>Active</TableCell>
      <TableCell>
        <Button size="sm">Edit</Button>
      </TableCell>
    </TableRow>
  </TableBody>
</Table>
```

### Badge Component
```tsx
import { Badge } from '@/components/ui/badge'

<Badge variant="success">Approved</Badge>
<Badge variant="warning">Pending</Badge>
<Badge variant="destructive">Revoked</Badge>
```

### Dialog Component
```tsx
import { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'

<Dialog>
  <DialogTrigger asChild>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Confirm Action</DialogTitle>
    </DialogHeader>
    <p>Are you sure you want to proceed?</p>
  </DialogContent>
</Dialog>
```

## Customization

All components use CSS variables defined in `src/index.css` and can be customized by modifying the design tokens. The components support both light and dark themes automatically.

## Admin Panel Specific Variants

Several components include admin-specific variants:
- `Button` has an `admin` variant with purple styling
- `Badge` includes `success`, `warning`, `info` variants for status indicators
- `Toast` supports all status variants for notifications

## Accessibility

All components are built with accessibility in mind:
- Proper ARIA attributes
- Keyboard navigation support
- Focus management
- Screen reader compatibility
- High contrast support
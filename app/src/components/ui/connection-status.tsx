// Connection status indicator component
import React from 'react'
import { Badge } from './badge'
import { Button } from './button'
import { Popover, PopoverContent, PopoverTrigger } from './popover'
import { useConnectionIndicator, useRealTimeUpdates } from '../../hooks/useRealTimeUpdates'
import { Wifi, WifiOff, AlertCircle, RefreshCw } from 'lucide-react'

interface ConnectionStatusProps {
  className?: string
  showDetails?: boolean
}

export function ConnectionStatus({ className, showDetails = false }: ConnectionStatusProps) {
  const { 
    overallStatus, 
    statusColor, 
    statusText, 
    details 
  } = useConnectionIndicator()
  
  const { reconnectAll } = useRealTimeUpdates()

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <Wifi className="h-4 w-4" />
      case 'connecting':
        return <RefreshCw className="h-4 w-4 animate-spin" />
      case 'error':
        return <AlertCircle className="h-4 w-4" />
      case 'disconnected':
        return <WifiOff className="h-4 w-4" />
      default:
        return <WifiOff className="h-4 w-4" />
    }
  }

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'connected':
        return 'default' as const
      case 'connecting':
        return 'secondary' as const
      case 'error':
        return 'destructive' as const
      case 'disconnected':
        return 'outline-solid' as const
      default:
        return 'outline-solid' as const
    }
  }

  if (!showDetails) {
    return (
      <Badge 
        variant={getStatusVariant(overallStatus)} 
        className={className}
      >
        {getStatusIcon(overallStatus)}
        <span className="ml-1">{statusText}</span>
      </Badge>
    )
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className={className}
        >
          {getStatusIcon(overallStatus)}
          <span className="ml-1">{statusText}</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">Connection Status</h4>
            <Button
              variant="outline"
              size="sm"
              onClick={reconnectAll}
              disabled={overallStatus === 'connecting'}
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Reconnect
            </Button>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Usage Updates</span>
              <Badge variant={getStatusVariant(details.usageUpdates.status)}>
                {getStatusIcon(details.usageUpdates.status)}
                <span className="ml-1">{details.usageUpdates.text}</span>
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">System Status</span>
              <Badge variant={getStatusVariant(details.systemStatus.status)}>
                {getStatusIcon(details.systemStatus.status)}
                <span className="ml-1">{details.systemStatus.text}</span>
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Notifications</span>
              <Badge variant={getStatusVariant(details.notifications.status)}>
                {getStatusIcon(details.notifications.status)}
                <span className="ml-1">{details.notifications.text}</span>
              </Badge>
            </div>
          </div>
          
          {overallStatus === 'error' && (
            <div className="p-3 bg-destructive/10 rounded-md">
              <p className="text-sm text-destructive">
                Some connections are experiencing issues. Real-time updates may be delayed.
              </p>
            </div>
          )}
          
          {overallStatus === 'disconnected' && (
            <div className="p-3 bg-muted rounded-md">
              <p className="text-sm text-muted-foreground">
                Real-time updates are disabled. Click reconnect to enable live data.
              </p>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Simple connection indicator for minimal UI
export function ConnectionIndicator({ className }: { className?: string }) {
  const { overallStatus } = useConnectionIndicator()

  const getIndicatorColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-green-500'
      case 'connecting':
        return 'bg-yellow-500 animate-pulse'
      case 'error':
        return 'bg-red-500'
      case 'disconnected':
        return 'bg-gray-400'
      default:
        return 'bg-gray-400'
    }
  }

  return (
    <div 
      className={`w-2 h-2 rounded-full ${getIndicatorColor(overallStatus)} ${className}`}
      title={`Connection: ${overallStatus}`}
    />
  )
}
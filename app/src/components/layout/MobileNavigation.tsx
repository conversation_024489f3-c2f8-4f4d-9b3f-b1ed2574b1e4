import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  Menu, 
  X, 
  LayoutDashboard, 
  Users, 
  Key, 
  BarChart3, 
  Settings, 
  User,
  Server
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
}

interface MobileNavigationProps {
  isAdmin?: boolean
}

const adminNavItems: NavItem[] = [
  { title: 'Dashboard', href: '/admin', icon: LayoutDashboard },
  { title: 'User Management', href: '/admin/users', icon: Users },
  { title: 'API Usage', href: '/admin/usage', icon: BarChart3 },
  { title: 'System Settings', href: '/admin/settings', icon: Settings },
]

const userNavItems: NavItem[] = [
  { title: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { title: 'API Keys', href: '/dashboard/api-keys', icon: Key },
  { title: 'Usage Analytics', href: '/dashboard/usage', icon: BarChart3 },
  { title: 'Profile', href: '/dashboard/profile', icon: User },
]

export function MobileNavigation({ isAdmin = false }: MobileNavigationProps) {
  const [isOpen, setIsOpen] = useState(false)
  const location = useLocation()
  const navItems = isAdmin ? adminNavItems : userNavItems

  return (
    <>
      {/* Mobile menu button - only visible on mobile */}
      <div className="md:hidden fixed top-4 left-4 z-50">
        <Button
          variant="outline"
          size="icon"
          onClick={() => setIsOpen(!isOpen)}
          className="bg-background"
        >
          {isOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Mobile navigation overlay */}
      {isOpen && (
        <div className="md:hidden fixed inset-0 z-40 bg-black/50" onClick={() => setIsOpen(false)} />
      )}

      {/* Mobile navigation sidebar */}
      <aside className={cn(
        "md:hidden fixed left-0 top-0 z-40 h-screen w-64 bg-sidebar border-r border-sidebar-border transform transition-transform duration-300",
        isOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex h-full flex-col">
          {/* Logo */}
          <div className="flex items-center gap-2 p-4 border-b border-sidebar-border">
            <Server className={cn(
              "h-6 w-6",
              isAdmin ? "text-admin" : "text-primary"
            )} />
            <span className="font-semibold text-sidebar-foreground">
              {isAdmin ? 'Admin Panel' : 'Home Server'}
            </span>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4">
            <ul className="space-y-2">
              {navItems.map((item) => {
                const isActive = location.pathname === item.href
                const Icon = item.icon

                return (
                  <li key={item.href}>
                    <Link
                      to={item.href}
                      onClick={() => setIsOpen(false)}
                      className={cn(
                        "nav-item",
                        isActive ? "nav-item-active" : "nav-item-inactive"
                      )}
                    >
                      <Icon className="h-4 w-4 shrink-0" />
                      <span>{item.title}</span>
                    </Link>
                  </li>
                )
              })}
            </ul>
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-sidebar-border">
            <div className="text-xs text-muted-foreground">
              <p>{isAdmin ? 'Home Server Admin' : 'Home Server Dashboard'}</p>
              <p>v1.0.0</p>
            </div>
          </div>
        </div>
      </aside>
    </>
  )
}
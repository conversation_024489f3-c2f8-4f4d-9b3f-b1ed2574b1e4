# Layout Components

This directory contains the layout components for the Home Server Admin Panel, providing both admin and user interfaces with responsive design.

## Components

### Layout Containers
- **AdminLayout** - Main layout for admin dashboard with sidebar and header
- **UserLayout** - Main layout for user dashboard with sidebar and header
- **ResponsiveLayout** - Wrapper that handles mobile/desktop layout switching

### Navigation Components
- **AdminSidebar** - Admin navigation sidebar with collapsible functionality
- **UserSidebar** - User navigation sidebar with collapsible functionality
- **AdminHeader** - Admin header with search, notifications, and user menu
- **UserHeader** - User header with search, notifications, and user menu
- **MobileNavigation** - Mobile-friendly navigation overlay
- **Breadcrumbs** - Breadcrumb navigation for deep page hierarchies

## Usage

### Admin Layout
```tsx
import { AdminLayout } from '@/components/layout'

// In your router setup
<Route path="/admin" element={<AdminLayout />}>
  <Route index element={<AdminDashboard />} />
  <Route path="users" element={<UserManagement />} />
  <Route path="usage" element={<ApiUsage />} />
  <Route path="settings" element={<SystemSettings />} />
</Route>
```

### User Layout
```tsx
import { UserLayout } from '@/components/layout'

// In your router setup
<Route path="/dashboard" element={<UserLayout />}>
  <Route index element={<UserDashboard />} />
  <Route path="api-keys" element={<ApiKeyManagement />} />
  <Route path="usage" element={<UsageAnalytics />} />
  <Route path="profile" element={<UserProfile />} />
</Route>
```

### Responsive Layout
```tsx
import { ResponsiveLayout } from '@/components/layout'

// For admin routes
<Route path="/admin" element={<ResponsiveLayout isAdmin={true} />}>
  {/* Admin routes */}
</Route>

// For user routes
<Route path="/dashboard" element={<ResponsiveLayout isAdmin={false} />}>
  {/* User routes */}
</Route>
```

## Features

### Responsive Design
- **Desktop**: Full sidebar and header layout
- **Mobile**: Collapsible overlay navigation with mobile-optimized header
- **Tablet**: Adaptive layout that works well on medium screens

### Navigation Features
- **Collapsible Sidebar**: Desktop sidebars can be collapsed to save space
- **Active State Tracking**: Navigation items highlight based on current route
- **Breadcrumb Navigation**: Automatic breadcrumb generation based on route
- **Search Integration**: Global search functionality in headers
- **User Menus**: Profile and settings access through header menus

### Admin vs User Differences
- **Admin Layout**: Purple accent colors, admin-specific navigation items
- **User Layout**: Blue accent colors, user-focused navigation items
- **Different Navigation Items**: Each layout shows relevant menu options
- **Role-Based Styling**: Visual differences to distinguish admin from user areas

## Customization

### Adding Navigation Items
Edit the `navItems` arrays in `AdminSidebar.tsx` or `UserSidebar.tsx`:

```tsx
const adminNavItems: NavItem[] = [
  {
    title: 'New Section',
    href: '/admin/new-section',
    icon: NewIcon,
    badge: 'New' // Optional badge
  },
  // ... other items
]
```

### Customizing Breadcrumbs
Update the `routeLabels` object in `Breadcrumbs.tsx`:

```tsx
const routeLabels: Record<string, string> = {
  '/admin/new-section': 'New Section',
  // ... other routes
}
```

### Styling
All layout components use the design tokens defined in `src/index.css` and can be customized by modifying the CSS variables.

## Accessibility

- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Focus Management**: Clear focus indicators and logical tab order
- **Mobile Touch Targets**: Appropriately sized touch targets for mobile devices
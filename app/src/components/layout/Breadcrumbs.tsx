import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  label: string
  href?: string
}

const routeLabels: Record<string, string> = {
  '/admin': 'Dashboard',
  '/admin/users': 'User Management',
  '/admin/usage': 'API Usage',
  '/admin/settings': 'System Settings',
  '/dashboard': 'Dashboard',
  '/dashboard/api-keys': 'API Keys',
  '/dashboard/usage': 'Usage Analytics',
  '/dashboard/profile': 'Profile',
}

export function Breadcrumbs() {
  const location = useLocation()
  const pathSegments = location.pathname.split('/').filter(Boolean)
  
  // Build breadcrumb items
  const breadcrumbs: BreadcrumbItem[] = []
  
  // Add home
  breadcrumbs.push({
    label: 'Home',
    href: pathSegments[0] === 'admin' ? '/admin' : '/dashboard'
  })
  
  // Add path segments
  let currentPath = ''
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`
    const label = routeLabels[currentPath] || segment.charAt(0).toUpperCase() + segment.slice(1)
    
    breadcrumbs.push({
      label,
      href: index === pathSegments.length - 1 ? undefined : currentPath
    })
  })

  return (
    <nav className="flex items-center space-x-1 text-sm text-muted-foreground">
      {breadcrumbs.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <ChevronRight className="h-4 w-4" />
          )}
          {item.href ? (
            <Link
              to={item.href}
              className="hover:text-foreground transition-colors"
            >
              {index === 0 ? (
                <Home className="h-4 w-4" />
              ) : (
                item.label
              )}
            </Link>
          ) : (
            <span className={cn(
              "font-medium",
              index === breadcrumbs.length - 1 && "text-foreground"
            )}>
              {index === 0 ? (
                <Home className="h-4 w-4" />
              ) : (
                item.label
              )}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  )
}
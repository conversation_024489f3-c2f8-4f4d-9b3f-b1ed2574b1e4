// Optimistic updates and data synchronization utilities
import { useQueryClient } from '@tanstack/react-query'
import { useCallback } from 'react'
import { queryKeys } from './useApiQuery'
import type { User, CreateUserRequest, UpdateUserRequest } from '../types/user'
import type { ApiKey, CreateApiKeyRequest, UpdateApiKeyRequest } from '../types/apiKey'
import type { PaginatedResponse } from '../types/api'

// User optimistic updates
export function useOptimisticUserUpdates() {
  const queryClient = useQueryClient()

  const optimisticCreateUser = useCallback(
    (userData: CreateUserRequest) => {
      const tempUser: User = {
        id: `temp-${Date.now()}`,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        apiAccessLevel: userData.apiAccessLevel
      }

      // Update all user list queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.users.lists() },
        (oldData: PaginatedResponse<User> | undefined) => {
          if (!oldData) return oldData
          return {
            ...oldData,
            data: [tempUser, ...oldData.data],
            total: oldData.total + 1
          }
        }
      )

      return tempUser
    },
    [queryClient]
  )

  const optimisticUpdateUser = useCallback(
    (userData: UpdateUserRequest) => {
      // Update specific user query
      queryClient.setQueryData(
        queryKeys.users.detail(userData.id),
        (oldData: User | undefined) => {
          if (!oldData) return oldData
          return {
            ...oldData,
            ...userData,
            updatedAt: new Date()
          }
        }
      )

      // Update user in all list queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.users.lists() },
        (oldData: PaginatedResponse<User> | undefined) => {
          if (!oldData) return oldData
          return {
            ...oldData,
            data: oldData.data.map(user =>
              user.id === userData.id
                ? { ...user, ...userData, updatedAt: new Date() }
                : user
            )
          }
        }
      )
    },
    [queryClient]
  )

  const optimisticDeleteUser = useCallback(
    (userId: string) => {
      // Remove from all user list queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.users.lists() },
        (oldData: PaginatedResponse<User> | undefined) => {
          if (!oldData) return oldData
          return {
            ...oldData,
            data: oldData.data.filter(user => user.id !== userId),
            total: oldData.total - 1
          }
        }
      )

      // Remove specific user query
      queryClient.removeQueries({
        queryKey: queryKeys.users.detail(userId)
      })
    },
    [queryClient]
  )

  const rollbackUserChanges = useCallback(
    (queryKey: any[], previousData: any) => {
      queryClient.setQueryData(queryKey, previousData)
    },
    [queryClient]
  )

  return {
    optimisticCreateUser,
    optimisticUpdateUser,
    optimisticDeleteUser,
    rollbackUserChanges
  }
}

// API Key optimistic updates
export function useOptimisticApiKeyUpdates() {
  const queryClient = useQueryClient()

  const optimisticCreateApiKey = useCallback(
    (keyData: CreateApiKeyRequest, userId: string) => {
      const tempKey: ApiKey = {
        id: `temp-${Date.now()}`,
        userId,
        name: keyData.name,
        description: keyData.description,
        keyHash: 'temp-hash',
        status: 'active',
        permissions: keyData.permissions,
        rateLimit: {
          requestsPerMinute: keyData.rateLimit?.requestsPerMinute || 60,
          requestsPerHour: keyData.rateLimit?.requestsPerHour || 1000,
          requestsPerDay: keyData.rateLimit?.requestsPerDay || 10000
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: keyData.expiresAt
      }

      // Update API key list queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.apiKeys.lists() },
        (oldData: ApiKey[] | undefined) => {
          if (!oldData) return [tempKey]
          return [tempKey, ...oldData]
        }
      )

      return tempKey
    },
    [queryClient]
  )

  const optimisticUpdateApiKey = useCallback(
    (keyData: UpdateApiKeyRequest) => {
      // Update specific API key query
      queryClient.setQueryData(
        queryKeys.apiKeys.detail(keyData.id),
        (oldData: ApiKey | undefined) => {
          if (!oldData) return oldData
          return {
            ...oldData,
            ...keyData,
            updatedAt: new Date()
          }
        }
      )

      // Update API key in all list queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.apiKeys.lists() },
        (oldData: ApiKey[] | undefined) => {
          if (!oldData) return oldData
          return oldData.map(key =>
            key.id === keyData.id
              ? { ...key, ...keyData, updatedAt: new Date() }
              : key
          )
        }
      )
    },
    [queryClient]
  )

  const optimisticRevokeApiKey = useCallback(
    (keyId: string) => {
      const updateKeyStatus = (key: ApiKey) =>
        key.id === keyId
          ? { ...key, status: 'revoked' as const, updatedAt: new Date() }
          : key

      // Update specific API key query
      queryClient.setQueryData(
        queryKeys.apiKeys.detail(keyId),
        (oldData: ApiKey | undefined) => {
          if (!oldData) return oldData
          return updateKeyStatus(oldData)
        }
      )

      // Update API key in all list queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.apiKeys.lists() },
        (oldData: ApiKey[] | undefined) => {
          if (!oldData) return oldData
          return oldData.map(updateKeyStatus)
        }
      )
    },
    [queryClient]
  )

  const optimisticDeleteApiKey = useCallback(
    (keyId: string) => {
      // Remove from all API key list queries
      queryClient.setQueriesData(
        { queryKey: queryKeys.apiKeys.lists() },
        (oldData: ApiKey[] | undefined) => {
          if (!oldData) return oldData
          return oldData.filter(key => key.id !== keyId)
        }
      )

      // Remove specific API key query
      queryClient.removeQueries({
        queryKey: queryKeys.apiKeys.detail(keyId)
      })
    },
    [queryClient]
  )

  const rollbackApiKeyChanges = useCallback(
    (queryKey: any[], previousData: any) => {
      queryClient.setQueryData(queryKey, previousData)
    },
    [queryClient]
  )

  return {
    optimisticCreateApiKey,
    optimisticUpdateApiKey,
    optimisticRevokeApiKey,
    optimisticDeleteApiKey,
    rollbackApiKeyChanges
  }
}

// Data synchronization hooks
export function useDataSync() {
  const queryClient = useQueryClient()

  const syncAllData = useCallback(async () => {
    try {
      await queryClient.invalidateQueries()
      console.log('All data synchronized')
    } catch (error) {
      console.error('Data sync failed:', error)
      throw error
    }
  }, [queryClient])

  const syncUsers = useCallback(async () => {
    try {
      await queryClient.invalidateQueries({ queryKey: queryKeys.users.all })
      console.log('User data synchronized')
    } catch (error) {
      console.error('User sync failed:', error)
      throw error
    }
  }, [queryClient])

  const syncApiKeys = useCallback(async () => {
    try {
      await queryClient.invalidateQueries({ queryKey: queryKeys.apiKeys.all })
      console.log('API key data synchronized')
    } catch (error) {
      console.error('API key sync failed:', error)
      throw error
    }
  }, [queryClient])

  const syncAnalytics = useCallback(async () => {
    try {
      await queryClient.invalidateQueries({ queryKey: queryKeys.analytics.all })
      console.log('Analytics data synchronized')
    } catch (error) {
      console.error('Analytics sync failed:', error)
      throw error
    }
  }, [queryClient])

  const syncUserData = useCallback(async (userId: string) => {
    try {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: queryKeys.users.detail(userId) }),
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.apiKeys.lists(),
          predicate: (query) => {
            const queryKey = query.queryKey as any[]
            return queryKey.some(key => 
              typeof key === 'object' && key?.userId === userId
            )
          }
        })
      ])
      console.log(`User ${userId} data synchronized`)
    } catch (error) {
      console.error(`User ${userId} sync failed:`, error)
      throw error
    }
  }, [queryClient])

  return {
    syncAllData,
    syncUsers,
    syncApiKeys,
    syncAnalytics,
    syncUserData
  }
}

// Background sync utilities
export function useBackgroundSync() {
  const { syncAllData } = useDataSync()

  const startBackgroundSync = useCallback((intervalMs = 5 * 60 * 1000) => {
    const interval = setInterval(async () => {
      try {
        // Only sync if the page is visible
        if (document.visibilityState === 'visible') {
          await syncAllData()
        }
      } catch (error) {
        console.warn('Background sync failed:', error)
      }
    }, intervalMs)

    return () => clearInterval(interval)
  }, [syncAllData])

  const syncOnFocus = useCallback(() => {
    const handleFocus = async () => {
      try {
        await syncAllData()
      } catch (error) {
        console.warn('Focus sync failed:', error)
      }
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [syncAllData])

  const syncOnOnline = useCallback(() => {
    const handleOnline = async () => {
      try {
        await syncAllData()
      } catch (error) {
        console.warn('Online sync failed:', error)
      }
    }

    window.addEventListener('online', handleOnline)
    return () => window.removeEventListener('online', handleOnline)
  }, [syncAllData])

  return {
    startBackgroundSync,
    syncOnFocus,
    syncOnOnline
  }
}
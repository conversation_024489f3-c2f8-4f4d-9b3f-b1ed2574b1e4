import { useEffect } from 'react'
import { useStore } from 'jotai'
import { sessionStorageMonitorAtom, sessionAtom, lastKnownSessionAtom } from '@/stores/auth'

/**
 * Hook to monitor session changes in localStorage and automatically validate sessions
 * This hook should be used at the app level (in App.tsx or main layout component)
 */
export const useSessionMonitoring = () => {
    const store = useStore()

    useEffect(() => {
        // Initialize the last known session
        const currentSession = store.get(sessionAtom)
        // store.set(lastKnownSessionAtom, currentSession)

        // Listen for storage events from other tabs/windows
        const handleStorageChange = (event: StorageEvent) => {
            if (event.key === 'auth-session') {
                console.log('Session changed in localStorage, validating...')
                store.set(sessionStorageMonitorAtom)
            }
        }

        // Listen for visibility change to check session when tab becomes active
        const handleVisibilityChange = () => {
            if (!document.hidden) {
                console.log('Tab became active, checking session...')
                store.set(sessionStorageMonitorAtom)
            }
        }

        // Listen for focus events to check session when window gets focus
        const handleFocus = () => {
            console.log('Window focused, checking session...')
            store.set(sessionStorageMonitorAtom)
        }

        window.addEventListener('storage', handleStorageChange)
        document.addEventListener('visibilitychange', handleVisibilityChange)
        window.addEventListener('focus', handleFocus)

        // Periodic check for manual localStorage modifications (every 30 seconds)
        const intervalId = setInterval(() => {
            store.set(sessionStorageMonitorAtom)
        }, 30000)

        // Initial check
        store.set(sessionStorageMonitorAtom)

        return () => {
            window.removeEventListener('storage', handleStorageChange)
            document.removeEventListener('visibilitychange', handleVisibilityChange)
            window.removeEventListener('focus', handleFocus)
            clearInterval(intervalId)
        }
    }, [store])
}

/**
 * Hook to manually trigger session validation
 * Useful for components that want to check session validity on demand
 */
export const useSessionValidator = () => {
    const store = useStore()

    const validateSession = () => {
        store.set(sessionStorageMonitorAtom)
    }

    return { validateSession }
}

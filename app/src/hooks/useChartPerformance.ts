import { useEffect, useRef, useCallback } from 'react'
import { recordChartRender, recordInteraction } from '../lib/performance-monitor'

interface ChartPerformanceOptions {
  chartType: string
  dataPoints?: number
  enableInteractionTracking?: boolean
}

interface ChartPerformanceMetrics {
  renderTime: number
  dataPoints: number
  interactionCount: number
}

export function useChartPerformance(options: ChartPerformanceOptions) {
  const renderStartTime = useRef<number>(0)
  const interactionCount = useRef<number>(0)
  const chartRef = useRef<HTMLDivElement>(null)

  // Start measuring render time
  const startRender = useCallback(() => {
    renderStartTime.current = performance.now()
  }, [])

  // End measuring render time
  const endRender = useCallback(() => {
    if (renderStartTime.current > 0) {
      const renderTime = performance.now() - renderStartTime.current
      const dataPoints = options.dataPoints || 0
      
      recordChartRender(options.chartType, renderTime, dataPoints)
      
      renderStartTime.current = 0
      return renderTime
    }
    return 0
  }, [options.chartType, options.dataPoints])

  // Track chart interactions
  const trackInteraction = useCallback((interactionType: string, metadata?: Record<string, any>) => {
    interactionCount.current += 1
    recordInteraction('chart_interaction', `${options.chartType}_${interactionType}`, {
      chartType: options.chartType,
      dataPoints: options.dataPoints,
      ...metadata
    })
  }, [options.chartType, options.dataPoints])

  // Set up interaction tracking
  useEffect(() => {
    if (!options.enableInteractionTracking || !chartRef.current) return

    const chartElement = chartRef.current

    const handleClick = (event: MouseEvent) => {
      trackInteraction('click', {
        x: event.clientX,
        y: event.clientY,
        target: (event.target as Element)?.tagName
      })
    }

    const handleMouseEnter = () => {
      trackInteraction('hover_start')
    }

    const handleMouseLeave = () => {
      trackInteraction('hover_end')
    }

    chartElement.addEventListener('click', handleClick)
    chartElement.addEventListener('mouseenter', handleMouseEnter)
    chartElement.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      chartElement.removeEventListener('click', handleClick)
      chartElement.removeEventListener('mouseenter', handleMouseEnter)
      chartElement.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [options.enableInteractionTracking, trackInteraction])

  // Get current metrics
  const getMetrics = useCallback((): ChartPerformanceMetrics => {
    return {
      renderTime: renderStartTime.current > 0 ? performance.now() - renderStartTime.current : 0,
      dataPoints: options.dataPoints || 0,
      interactionCount: interactionCount.current
    }
  }, [options.dataPoints])

  // Reset metrics
  const resetMetrics = useCallback(() => {
    renderStartTime.current = 0
    interactionCount.current = 0
  }, [])

  return {
    chartRef,
    startRender,
    endRender,
    trackInteraction,
    getMetrics,
    resetMetrics
  }
}

// Hook for measuring data loading performance
export function useDataLoadingPerformance(dataSource: string) {
  const loadStartTime = useRef<number>(0)

  const startLoading = useCallback(() => {
    loadStartTime.current = performance.now()
  }, [])

  const endLoading = useCallback((recordCount?: number) => {
    if (loadStartTime.current > 0) {
      const loadTime = performance.now() - loadStartTime.current
      
      recordInteraction('data_loading', dataSource, {
        loadTime,
        recordCount,
        dataSource
      })
      
      loadStartTime.current = 0
      return loadTime
    }
    return 0
  }, [dataSource])

  return {
    startLoading,
    endLoading
  }
}

// Hook for tracking user dashboard usage patterns
export function useDashboardAnalytics() {
  const sessionStartTime = useRef<number>(Date.now())
  const pageViews = useRef<Map<string, number>>(new Map())
  const featureUsage = useRef<Map<string, number>>(new Map())

  const trackPageView = useCallback((pageName: string) => {
    const currentCount = pageViews.current.get(pageName) || 0
    pageViews.current.set(pageName, currentCount + 1)
    
    recordInteraction('navigation', pageName, {
      sessionDuration: Date.now() - sessionStartTime.current,
      totalPageViews: Array.from(pageViews.current.values()).reduce((a, b) => a + b, 0)
    })
  }, [])

  const trackFeatureUsage = useCallback((featureName: string, metadata?: Record<string, any>) => {
    const currentCount = featureUsage.current.get(featureName) || 0
    featureUsage.current.set(featureName, currentCount + 1)
    
    recordInteraction('feature_usage', featureName, {
      usageCount: currentCount + 1,
      sessionDuration: Date.now() - sessionStartTime.current,
      ...metadata
    })
  }, [])

  const getSessionSummary = useCallback(() => {
    return {
      sessionDuration: Date.now() - sessionStartTime.current,
      pageViews: Object.fromEntries(pageViews.current),
      featureUsage: Object.fromEntries(featureUsage.current),
      totalInteractions: Array.from(featureUsage.current.values()).reduce((a, b) => a + b, 0)
    }
  }, [])

  return {
    trackPageView,
    trackFeatureUsage,
    getSessionSummary
  }
}

// Hook for monitoring component render performance
export function useRenderPerformance(componentName: string) {
  const renderCount = useRef<number>(0)
  const totalRenderTime = useRef<number>(0)

  useEffect(() => {
    const startTime = performance.now()
    renderCount.current += 1

    return () => {
      const renderTime = performance.now() - startTime
      totalRenderTime.current += renderTime
      
      // Record slow renders
      if (renderTime > 16) { // > 16ms (60fps threshold)
        recordInteraction('slow_render', componentName, {
          renderTime,
          renderCount: renderCount.current,
          averageRenderTime: totalRenderTime.current / renderCount.current
        })
      }
    }
  })

  const getPerformanceStats = useCallback(() => {
    return {
      renderCount: renderCount.current,
      totalRenderTime: totalRenderTime.current,
      averageRenderTime: renderCount.current > 0 ? totalRenderTime.current / renderCount.current : 0
    }
  }, [])

  return {
    getPerformanceStats
  }
}
// Polling mechanisms for periodic data refresh
import { useEffect, useRef, useCallback, useState } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { queryKeys } from './useApiQuery'
import { apiClient } from '../lib/api'
import type { TimeRange } from '../types/api'

export interface PollingConfig {
  interval: number
  enabled?: boolean
  immediate?: boolean
  onError?: (error: Error) => void
  onSuccess?: (data: any) => void
}

// Generic polling hook
export function usePolling<T>(
  queryFn: () => Promise<T>,
  config: PollingConfig
): {
  data: T | null
  isPolling: boolean
  error: Error | null
  start: () => void
  stop: () => void
  refresh: () => Promise<void>
} {
  const [data, setData] = useState<T | null>(null)
  const [error, setError] = useState<Error | null>(null)
  const [isPolling, setIsPolling] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef(true)

  const start = useCallback(() => {
    if (intervalRef.current) return // Already polling

    setIsPolling(true)
    setError(null)

    const poll = async () => {
      try {
        const result = await queryFn()
        if (mountedRef.current) {
          setData(result)
          setError(null)
          config.onSuccess?.(result)
        }
      } catch (err) {
        const error = err as Error
        if (mountedRef.current) {
          setError(error)
          config.onError?.(error)
        }
      }
    }

    // Execute immediately if requested
    if (config.immediate) {
      poll()
    }

    // Set up interval
    intervalRef.current = setInterval(poll, config.interval)
  }, [queryFn, config])

  const stop = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    setIsPolling(false)
  }, [])

  const refresh = useCallback(async () => {
    try {
      const result = await queryFn()
      if (mountedRef.current) {
        setData(result)
        setError(null)
        config.onSuccess?.(result)
      }
    } catch (err) {
      const error = err as Error
      if (mountedRef.current) {
        setError(error)
        config.onError?.(error)
      }
      throw error
    }
  }, [queryFn, config])

  useEffect(() => {
    if (config.enabled) {
      start()
    } else {
      stop()
    }

    return () => {
      stop()
    }
  }, [config.enabled, start, stop])

  useEffect(() => {
    return () => {
      mountedRef.current = false
      stop()
    }
  }, [stop])

  return {
    data,
    isPolling,
    error,
    start,
    stop,
    refresh
  }
}

// Polling hook with query client integration
export function useQueryPolling<T>(
  queryKey: any[],
  queryFn: () => Promise<T>,
  config: PollingConfig
) {
  const queryClient = useQueryClient()

  const enhancedConfig = {
    ...config,
    onSuccess: (data: T) => {
      // Update query cache
      queryClient.setQueryData(queryKey, data)
      config.onSuccess?.(data)
    }
  }

  return usePolling(queryFn, enhancedConfig)
}

// System metrics polling
export function useSystemMetricsPolling(
  timeRange: TimeRange,
  interval = 30000, // 30 seconds
  enabled = true
) {
  return useQueryPolling(
    queryKeys.analytics.systemMetrics(timeRange),
    () => apiClient.getSystemMetrics(timeRange),
    {
      interval,
      enabled,
      immediate: true,
      onError: (error) => {
        console.error('System metrics polling failed:', error)
      }
    }
  )
}

// Usage metrics polling
export function useUsageMetricsPolling(
  query: any,
  interval = 60000, // 1 minute
  enabled = true
) {
  return useQueryPolling(
    queryKeys.analytics.usageMetrics(query),
    () => apiClient.getUsageMetrics(query),
    {
      interval,
      enabled,
      immediate: true,
      onError: (error) => {
        console.error('Usage metrics polling failed:', error)
      }
    }
  )
}

// User stats polling
export function useUserStatsPolling(
  interval = 120000, // 2 minutes
  enabled = true
) {
  return useQueryPolling(
    queryKeys.users.stats(),
    () => apiClient.getUserStats(),
    {
      interval,
      enabled,
      immediate: true,
      onError: (error) => {
        console.error('User stats polling failed:', error)
      }
    }
  )
}

// API key stats polling
export function useApiKeyStatsPolling(
  userId?: string,
  interval = 120000, // 2 minutes
  enabled = true
) {
  return useQueryPolling(
    queryKeys.apiKeys.userStats(userId),
    () => apiClient.getApiKeyStats(userId),
    {
      interval,
      enabled,
      immediate: true,
      onError: (error) => {
        console.error('API key stats polling failed:', error)
      }
    }
  )
}

// Adaptive polling hook that adjusts interval based on activity
export function useAdaptivePolling<T>(
  queryFn: () => Promise<T>,
  baseConfig: PollingConfig & {
    minInterval?: number
    maxInterval?: number
    activityThreshold?: number
  }
) {
  const [currentInterval, setCurrentInterval] = useState(baseConfig.interval)
  const [activityLevel, setActivityLevel] = useState(0)
  const lastActivityRef = useRef(Date.now())

  const updateActivity = useCallback(() => {
    const now = Date.now()
    const timeSinceLastActivity = now - lastActivityRef.current
    lastActivityRef.current = now

    // Calculate activity level (0-1)
    const threshold = baseConfig.activityThreshold || 60000 // 1 minute
    const activity = Math.max(0, 1 - (timeSinceLastActivity / threshold))
    setActivityLevel(activity)

    // Adjust polling interval based on activity
    const minInterval = baseConfig.minInterval || baseConfig.interval / 4
    const maxInterval = baseConfig.maxInterval || baseConfig.interval * 4
    const newInterval = minInterval + (maxInterval - minInterval) * (1 - activity)
    setCurrentInterval(Math.round(newInterval))
  }, [baseConfig])

  // Track user activity
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
    
    const handleActivity = () => {
      updateActivity()
    }

    events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true })
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity)
      })
    }
  }, [updateActivity])

  return usePolling(queryFn, {
    ...baseConfig,
    interval: currentInterval
  })
}

// Smart polling manager for multiple data sources
export class PollingManager {
  private pollers = new Map<string, {
    stop: () => void
    refresh: () => Promise<void>
  }>()

  // Add a poller
  addPoller(
    key: string,
    queryFn: () => Promise<any>,
    config: PollingConfig
  ): void {
    // Stop existing poller if any
    this.removePoller(key)

    let intervalId: NodeJS.Timeout | null = null
    let isActive = false

    const start = () => {
      if (isActive) return
      isActive = true

      const poll = async () => {
        try {
          const result = await queryFn()
          config.onSuccess?.(result)
        } catch (error) {
          config.onError?.(error as Error)
        }
      }

      if (config.immediate) {
        poll()
      }

      intervalId = setInterval(poll, config.interval)
    }

    const stop = () => {
      if (intervalId) {
        clearInterval(intervalId)
        intervalId = null
      }
      isActive = false
    }

    const refresh = async () => {
      try {
        const result = await queryFn()
        config.onSuccess?.(result)
      } catch (error) {
        config.onError?.(error as Error)
        throw error
      }
    }

    this.pollers.set(key, { stop, refresh })

    if (config.enabled !== false) {
      start()
    }
  }

  // Remove a poller
  removePoller(key: string): void {
    const poller = this.pollers.get(key)
    if (poller) {
      poller.stop()
      this.pollers.delete(key)
    }
  }

  // Refresh specific poller
  async refreshPoller(key: string): Promise<void> {
    const poller = this.pollers.get(key)
    if (poller) {
      await poller.refresh()
    }
  }

  // Refresh all pollers
  async refreshAll(): Promise<void> {
    await Promise.allSettled(
      Array.from(this.pollers.values()).map(poller => poller.refresh())
    )
  }

  // Stop all pollers
  stopAll(): void {
    this.pollers.forEach(poller => poller.stop())
    this.pollers.clear()
  }

  // Get active poller count
  getActiveCount(): number {
    return this.pollers.size
  }
}

// Export singleton instance
export const pollingManager = new PollingManager()

// Hook to use the polling manager
export function usePollingManager() {
  useEffect(() => {
    return () => {
      // Cleanup on unmount
      pollingManager.stopAll()
    }
  }, [])

  return pollingManager
}
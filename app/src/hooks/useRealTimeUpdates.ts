// Real-time updates and notification system
import { useEffect, useCallback, useState } from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { useAtom, atom } from 'jotai'
import { 
  wsManager, 
  WebSocketClient, 
  type UsageUpdateMessage, 
  type SystemStatusMessage, 
  type NotificationMessage 
} from '../lib/websocket'
import { queryKeys } from './useApiQuery'
import { useToast } from './useToast'
import type { ApiUsageLog, SystemMetrics } from '../types/analytics'

// Connection status atom
const connectionStatusAtom = atom<{
  usageUpdates: 'disconnected' | 'connecting' | 'connected' | 'error'
  systemStatus: 'disconnected' | 'connecting' | 'connected' | 'error'
  notifications: 'disconnected' | 'connecting' | 'connected' | 'error'
}>({
  usageUpdates: 'disconnected',
  systemStatus: 'disconnected',
  notifications: 'disconnected'
})

// Real-time data atoms
const realtimeUsageLogsAtom = atom<ApiUsageLog[]>([])
const realtimeSystemMetricsAtom = atom<SystemMetrics | null>(null)
const realtimeNotificationsAtom = atom<NotificationMessage['data'][]>([])

// Connection status hook
export function useConnectionStatus() {
  return useAtom(connectionStatusAtom)
}

// Real-time usage updates hook
export function useRealTimeUsageUpdates(enabled = true) {
  const queryClient = useQueryClient()
  const [connectionStatus, setConnectionStatus] = useAtom(connectionStatusAtom)
  const [usageLogs, setUsageLogs] = useAtom(realtimeUsageLogsAtom)
  const [client, setClient] = useState<WebSocketClient | null>(null)

  const handleUsageUpdate = useCallback((data: UsageUpdateMessage['data']) => {
    // Convert to ApiUsageLog format
    const usageLog: ApiUsageLog = {
      id: `realtime-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      userId: data.userId,
      apiKeyId: data.apiKeyId,
      endpoint: data.endpoint,
      method: data.method as any,
      statusCode: data.statusCode,
      responseTime: data.responseTime,
      requestSize: 0, // Not provided in real-time update
      responseSize: 0, // Not provided in real-time update
      timestamp: new Date(data.timestamp),
      ipAddress: undefined,
      userAgent: undefined,
      errorMessage: data.statusCode >= 400 ? `Error ${data.statusCode}` : undefined
    }

    // Add to real-time logs (keep last 100)
    setUsageLogs(prev => [usageLog, ...prev.slice(0, 99)])

    // Invalidate usage-related queries
    queryClient.invalidateQueries({ queryKey: queryKeys.analytics.all })
    queryClient.invalidateQueries({ queryKey: queryKeys.apiKeys.all })
    queryClient.invalidateQueries({ queryKey: queryKeys.users.all })
  }, [queryClient, setUsageLogs])

  const handleConnect = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, usageUpdates: 'connected' }))
  }, [setConnectionStatus])

  const handleDisconnect = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, usageUpdates: 'disconnected' }))
  }, [setConnectionStatus])

  const handleError = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, usageUpdates: 'error' }))
  }, [setConnectionStatus])

  const handleReconnect = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, usageUpdates: 'connecting' }))
  }, [setConnectionStatus])

  useEffect(() => {
    if (!enabled) {
      client?.disconnect()
      setClient(null)
      return
    }

    const wsClient = wsManager.connectUsageUpdates({
      onUsageUpdate: handleUsageUpdate,
      onConnect: handleConnect,
      onDisconnect: handleDisconnect,
      onError: handleError,
      onReconnect: handleReconnect
    })

    setClient(wsClient)
    setConnectionStatus(prev => ({ ...prev, usageUpdates: 'connecting' }))

    return () => {
      wsClient.disconnect()
    }
  }, [enabled, handleUsageUpdate, handleConnect, handleDisconnect, handleError, handleReconnect, setConnectionStatus])

  return {
    usageLogs,
    connectionStatus: connectionStatus.usageUpdates,
    client
  }
}

// Real-time system status hook
export function useRealTimeSystemStatus(enabled = true) {
  const queryClient = useQueryClient()
  const [connectionStatus, setConnectionStatus] = useAtom(connectionStatusAtom)
  const [systemMetrics, setSystemMetrics] = useAtom(realtimeSystemMetricsAtom)
  const [client, setClient] = useState<WebSocketClient | null>(null)

  const handleSystemStatus = useCallback((data: SystemStatusMessage['data']) => {
    const metrics: SystemMetrics = {
      timestamp: new Date(),
      activeUsers: data.activeUsers,
      totalApiCalls: data.totalApiCalls,
      averageResponseTime: data.averageResponseTime,
      errorRate: data.errorRate,
      systemLoad: data.systemLoad,
      ollamaMetrics: [] // Would be populated from separate endpoint
    }

    setSystemMetrics(metrics)

    // Update system metrics queries
    queryClient.setQueriesData(
      { queryKey: queryKeys.analytics.systemMetrics },
      (oldData: SystemMetrics[] | undefined) => {
        if (!oldData) return [metrics]
        return [metrics, ...oldData.slice(0, 99)] // Keep last 100 entries
      }
    )
  }, [queryClient, setSystemMetrics])

  const handleConnect = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, systemStatus: 'connected' }))
  }, [setConnectionStatus])

  const handleDisconnect = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, systemStatus: 'disconnected' }))
  }, [setConnectionStatus])

  const handleError = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, systemStatus: 'error' }))
  }, [setConnectionStatus])

  const handleReconnect = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, systemStatus: 'connecting' }))
  }, [setConnectionStatus])

  useEffect(() => {
    if (!enabled) {
      client?.disconnect()
      setClient(null)
      return
    }

    const wsClient = wsManager.connectSystemStatus({
      onSystemStatus: handleSystemStatus,
      onConnect: handleConnect,
      onDisconnect: handleDisconnect,
      onError: handleError,
      onReconnect: handleReconnect
    })

    setClient(wsClient)
    setConnectionStatus(prev => ({ ...prev, systemStatus: 'connecting' }))

    return () => {
      wsClient.disconnect()
    }
  }, [enabled, handleSystemStatus, handleConnect, handleDisconnect, handleError, handleReconnect, setConnectionStatus])

  return {
    systemMetrics,
    connectionStatus: connectionStatus.systemStatus,
    client
  }
}

// Real-time notifications hook
export function useRealTimeNotifications(enabled = true) {
  const { toast } = useToast()
  const [connectionStatus, setConnectionStatus] = useAtom(connectionStatusAtom)
  const [notifications, setNotifications] = useAtom(realtimeNotificationsAtom)
  const [client, setClient] = useState<WebSocketClient | null>(null)

  const handleNotification = useCallback((data: NotificationMessage['data']) => {
    // Add to notifications list
    setNotifications(prev => [data, ...prev.slice(0, 49)]) // Keep last 50

    // Show toast notification
    toast({
      title: data.title,
      description: data.message,
      variant: data.level === 'error' ? 'destructive' : 'default'
    })

    // Handle notification actions if any
    if (data.actions) {
      // This could trigger specific UI actions or navigation
      console.log('Notification actions:', data.actions)
    }
  }, [toast, setNotifications])

  const handleConnect = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, notifications: 'connected' }))
  }, [setConnectionStatus])

  const handleDisconnect = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, notifications: 'disconnected' }))
  }, [setConnectionStatus])

  const handleError = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, notifications: 'error' }))
  }, [setConnectionStatus])

  const handleReconnect = useCallback(() => {
    setConnectionStatus(prev => ({ ...prev, notifications: 'connecting' }))
  }, [setConnectionStatus])

  useEffect(() => {
    if (!enabled) {
      client?.disconnect()
      setClient(null)
      return
    }

    const wsClient = wsManager.connectNotifications({
      onNotification: handleNotification,
      onConnect: handleConnect,
      onDisconnect: handleDisconnect,
      onError: handleError,
      onReconnect: handleReconnect
    })

    setClient(wsClient)
    setConnectionStatus(prev => ({ ...prev, notifications: 'connecting' }))

    return () => {
      wsClient.disconnect()
    }
  }, [enabled, handleNotification, handleConnect, handleDisconnect, handleError, handleReconnect, setConnectionStatus])

  const clearNotifications = useCallback(() => {
    setNotifications([])
  }, [setNotifications])

  const removeNotification = useCallback((index: number) => {
    setNotifications(prev => prev.filter((_, i) => i !== index))
  }, [setNotifications])

  return {
    notifications,
    connectionStatus: connectionStatus.notifications,
    clearNotifications,
    removeNotification,
    client
  }
}

// Combined real-time updates hook
export function useRealTimeUpdates(config: {
  usageUpdates?: boolean
  systemStatus?: boolean
  notifications?: boolean
} = {}) {
  const {
    usageUpdates = true,
    systemStatus = true,
    notifications = true
  } = config

  const usageData = useRealTimeUsageUpdates(usageUpdates)
  const systemData = useRealTimeSystemStatus(systemStatus)
  const notificationData = useRealTimeNotifications(notifications)

  const [connectionStatus] = useConnectionStatus()

  const isConnected = usageUpdates ? connectionStatus.usageUpdates === 'connected' :
                     systemStatus ? connectionStatus.systemStatus === 'connected' :
                     notifications ? connectionStatus.notifications === 'connected' : false

  const hasErrors = Object.values(connectionStatus).some(status => status === 'error')

  const reconnectAll = useCallback(() => {
    usageData.client?.connect()
    systemData.client?.connect()
    notificationData.client?.connect()
  }, [usageData.client, systemData.client, notificationData.client])

  const disconnectAll = useCallback(() => {
    usageData.client?.disconnect()
    systemData.client?.disconnect()
    notificationData.client?.disconnect()
  }, [usageData.client, systemData.client, notificationData.client])

  // Set auth token for all connections
  const setAuthToken = useCallback((token: string | null) => {
    wsManager.setAuthToken(token)
  }, [])

  return {
    usageUpdates: usageData,
    systemStatus: systemData,
    notifications: notificationData,
    connectionStatus,
    isConnected,
    hasErrors,
    reconnectAll,
    disconnectAll,
    setAuthToken
  }
}

// Connection status indicator component data
export function useConnectionIndicator() {
  const [connectionStatus] = useConnectionStatus()

  const getOverallStatus = () => {
    const statuses = Object.values(connectionStatus)
    
    if (statuses.every(status => status === 'connected')) {
      return 'connected'
    } else if (statuses.some(status => status === 'error')) {
      return 'error'
    } else if (statuses.some(status => status === 'connecting')) {
      return 'connecting'
    } else {
      return 'disconnected'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'green'
      case 'connecting': return 'yellow'
      case 'error': return 'red'
      case 'disconnected': return 'gray'
      default: return 'gray'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'connected': return 'Connected'
      case 'connecting': return 'Connecting...'
      case 'error': return 'Connection Error'
      case 'disconnected': return 'Disconnected'
      default: return 'Unknown'
    }
  }

  const overallStatus = getOverallStatus()

  return {
    connectionStatus,
    overallStatus,
    statusColor: getStatusColor(overallStatus),
    statusText: getStatusText(overallStatus),
    details: {
      usageUpdates: {
        status: connectionStatus.usageUpdates,
        color: getStatusColor(connectionStatus.usageUpdates),
        text: getStatusText(connectionStatus.usageUpdates)
      },
      systemStatus: {
        status: connectionStatus.systemStatus,
        color: getStatusColor(connectionStatus.systemStatus),
        text: getStatusText(connectionStatus.systemStatus)
      },
      notifications: {
        status: connectionStatus.notifications,
        color: getStatusColor(connectionStatus.notifications),
        text: getStatusText(connectionStatus.notifications)
      }
    }
  }
}
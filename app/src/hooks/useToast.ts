import { useState, useCallback } from 'react'

export interface ToastOptions {
  title: string
  description?: string
  variant?: 'default' | 'destructive' | 'success' | 'warning' | 'info'
  duration?: number
}

export interface Toast extends ToastOptions {
  id: string
  open: boolean
}

let toastCount = 0

export function useToast() {
  const [toasts, setToasts] = useState<Toast[]>([])

  const toast = useCallback((options: ToastOptions) => {
    const id = (++toastCount).toString()
    const newToast: Toast = {
      ...options,
      id,
      open: true,
      duration: options.duration ?? 5000,
    }

    setToasts((prev) => [...prev, newToast])

    // Auto dismiss after duration
    if (newToast.duration > 0) {
      setTimeout(() => {
        setToasts((prev) => prev.filter((t) => t.id !== id))
      }, newToast.duration)
    }

    return id
  }, [])

  const dismiss = useCallback((toastId: string) => {
    setToasts((prev) => prev.filter((t) => t.id !== toastId))
  }, [])

  const dismissAll = useCallback(() => {
    setToasts([])
  }, [])

  return {
    toast,
    dismiss,
    dismissAll,
    toasts,
  }
}

// Global toast function for convenience
let globalToast: ((options: ToastOptions) => string) | null = null

export function setGlobalToast(toastFn: (options: ToastOptions) => string) {
  globalToast = toastFn
}

export function toast(options: ToastOptions) {
  if (globalToast) {
    return globalToast(options)
  }
  console.warn('Toast not initialized. Make sure to use ToastProvider.')
  return ''
}
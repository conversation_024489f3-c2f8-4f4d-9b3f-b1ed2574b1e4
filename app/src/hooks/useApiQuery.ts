// Custom hooks for data fetching with <PERSON><PERSON> async atoms and caching
import { atom, useAtom, useAtomValue, useSet<PERSON>tom } from 'jotai'
import { atomWithQuery, atomWithMutation } from 'jotai-tanstack-query'
import type { QueryKey, QueryFunction, MutationFunction } from '@tanstack/react-query'
import { apiClient } from '../lib/api'
import type { 
  User, 
  CreateUserRequest, 
  UpdateUserRequest, 
  UserActionRequest, 
  UserStats, 
  UserActivity, 
  UserFilters 
} from '../types/user'
import type { 
  ApiKey, 
  CreateApiKeyRequest, 
  UpdateApiKeyRequest, 
  ApiKeyStats, 
  ApiKeyUsage, 
  ApiKeyFilters 
} from '../types/apiKey'
import type { 
  ApiUsageLog, 
  UsageMetrics, 
  OllamaPerformanceMetrics, 
  SystemMetrics, 
  AnalyticsQuery, 
  AnalyticsFilters 
} from '../types/analytics'
import type { PaginatedResponse, TimeRange } from '../types/api'

// Cache configuration
export const CACHE_CONFIG = {
  STALE_TIME: {
    SHORT: 30 * 1000, // 30 seconds
    MEDIUM: 5 * 60 * 1000, // 5 minutes
    LONG: 30 * 60 * 1000, // 30 minutes
    VERY_LONG: 60 * 60 * 1000 // 1 hour
  },
  GC_TIME: {
    SHORT: 5 * 60 * 1000, // 5 minutes
    MEDIUM: 30 * 60 * 1000, // 30 minutes
    LONG: 60 * 60 * 1000 // 1 hour
  }
} as const

// Query key factories for consistent cache keys
export const queryKeys = {
  // User queries
  users: {
    all: ['users'] as const,
    lists: () => [...queryKeys.users.all, 'list'] as const,
    list: (filters?: UserFilters, page?: number, limit?: number) => 
      [...queryKeys.users.lists(), { filters, page, limit }] as const,
    details: () => [...queryKeys.users.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.users.details(), id] as const,
    stats: () => [...queryKeys.users.all, 'stats'] as const,
    activities: () => [...queryKeys.users.all, 'activities'] as const,
    activity: (userId?: string, limit?: number) => 
      [...queryKeys.users.activities(), { userId, limit }] as const
  },
  
  // API Key queries
  apiKeys: {
    all: ['apiKeys'] as const,
    lists: () => [...queryKeys.apiKeys.all, 'list'] as const,
    list: (filters?: ApiKeyFilters, userId?: string) => 
      [...queryKeys.apiKeys.lists(), { filters, userId }] as const,
    details: () => [...queryKeys.apiKeys.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.apiKeys.details(), id] as const,
    stats: () => [...queryKeys.apiKeys.all, 'stats'] as const,
    userStats: (userId?: string) => [...queryKeys.apiKeys.stats(), { userId }] as const,
    usage: () => [...queryKeys.apiKeys.all, 'usage'] as const,
    keyUsage: (keyId: string, timeRange: TimeRange) => 
      [...queryKeys.apiKeys.usage(), { keyId, timeRange }] as const
  },
  
  // Analytics queries
  analytics: {
    all: ['analytics'] as const,
    usageLogs: (filters: AnalyticsFilters, limit?: number) => 
      [...queryKeys.analytics.all, 'usageLogs', { filters, limit }] as const,
    usageMetrics: (query: AnalyticsQuery) => 
      [...queryKeys.analytics.all, 'usageMetrics', query] as const,
    ollamaMetrics: (timeRange: TimeRange) => 
      [...queryKeys.analytics.all, 'ollamaMetrics', timeRange] as const,
    systemMetrics: (timeRange: TimeRange) => 
      [...queryKeys.analytics.all, 'systemMetrics', timeRange] as const,
    trendData: (metric: string, timeRange: TimeRange, granularity?: string) => 
      [...queryKeys.analytics.all, 'trendData', { metric, timeRange, granularity }] as const
  }
} as const

// User Management Hooks
export function useUsers(filters?: UserFilters, page = 1, limit = 10) {
  const usersAtom = atomWithQuery(() => ({
    queryKey: queryKeys.users.list(filters, page, limit),
    queryFn: () => apiClient.getUsers(filters, page, limit),
    staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM,
    gcTime: CACHE_CONFIG.GC_TIME.MEDIUM
  }))
  
  return useAtomValue(usersAtom)
}

export function useUser(id: string) {
  const userAtom = atomWithQuery(() => ({
    queryKey: queryKeys.users.detail(id),
    queryFn: () => apiClient.getUserById(id),
    staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM,
    gcTime: CACHE_CONFIG.GC_TIME.MEDIUM,
    enabled: !!id
  }))
  
  return useAtomValue(userAtom)
}

export function useUserStats() {
  const userStatsAtom = atomWithQuery(() => ({
    queryKey: queryKeys.users.stats(),
    queryFn: () => apiClient.getUserStats(),
    staleTime: CACHE_CONFIG.STALE_TIME.SHORT,
    gcTime: CACHE_CONFIG.GC_TIME.SHORT
  }))
  
  return useAtomValue(userStatsAtom)
}

export function useUserActivities(userId?: string, limit = 50) {
  const userActivitiesAtom = atomWithQuery(() => ({
    queryKey: queryKeys.users.activity(userId, limit),
    queryFn: () => apiClient.getUserActivities(userId, limit),
    staleTime: CACHE_CONFIG.STALE_TIME.SHORT,
    gcTime: CACHE_CONFIG.GC_TIME.SHORT
  }))
  
  return useAtomValue(userActivitiesAtom)
}

// User Mutation Hooks
export function useCreateUser() {
  const createUserAtom = atomWithMutation(() => ({
    mutationFn: (userData: CreateUserRequest) => apiClient.createUser(userData),
    onSuccess: () => {
      // Invalidate users list cache
      // This would be handled by the query client in a real implementation
    }
  }))
  
  return useAtom(createUserAtom)
}

export function useUpdateUser() {
  const updateUserAtom = atomWithMutation(() => ({
    mutationFn: (userData: UpdateUserRequest) => apiClient.updateUser(userData),
    onSuccess: (data) => {
      // Invalidate specific user and users list cache
      // This would be handled by the query client in a real implementation
    }
  }))
  
  return useAtom(updateUserAtom)
}

export function useUserAction() {
  const userActionAtom = atomWithMutation(() => ({
    mutationFn: (action: UserActionRequest) => apiClient.performUserAction(action),
    onSuccess: (data) => {
      // Invalidate users list and specific user cache
      // This would be handled by the query client in a real implementation
    }
  }))
  
  return useAtom(userActionAtom)
}

export function useDeleteUser() {
  const deleteUserAtom = atomWithMutation(() => ({
    mutationFn: (id: string) => apiClient.deleteUser(id),
    onSuccess: () => {
      // Invalidate users list cache
      // This would be handled by the query client in a real implementation
    }
  }))
  
  return useAtom(deleteUserAtom)
}

// API Key Management Hooks
export function useApiKeys(filters?: ApiKeyFilters, userId?: string) {
  const apiKeysAtom = atomWithQuery(() => ({
    queryKey: queryKeys.apiKeys.list(filters, userId),
    queryFn: () => apiClient.getApiKeys(filters, userId),
    staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM,
    gcTime: CACHE_CONFIG.GC_TIME.MEDIUM
  }))
  
  return useAtomValue(apiKeysAtom)
}

export function useApiKey(id: string) {
  const apiKeyAtom = atomWithQuery(() => ({
    queryKey: queryKeys.apiKeys.detail(id),
    queryFn: () => apiClient.getApiKeyById(id),
    staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM,
    gcTime: CACHE_CONFIG.GC_TIME.MEDIUM,
    enabled: !!id
  }))
  
  return useAtomValue(apiKeyAtom)
}

export function useApiKeyStats(userId?: string) {
  const apiKeyStatsAtom = atomWithQuery(() => ({
    queryKey: queryKeys.apiKeys.userStats(userId),
    queryFn: () => apiClient.getApiKeyStats(userId),
    staleTime: CACHE_CONFIG.STALE_TIME.SHORT,
    gcTime: CACHE_CONFIG.GC_TIME.SHORT
  }))
  
  return useAtomValue(apiKeyStatsAtom)
}

export function useApiKeyUsage(keyId: string, timeRange: TimeRange) {
  const apiKeyUsageAtom = atomWithQuery(() => ({
    queryKey: queryKeys.apiKeys.keyUsage(keyId, timeRange),
    queryFn: () => apiClient.getApiKeyUsage(keyId, timeRange),
    staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM,
    gcTime: CACHE_CONFIG.GC_TIME.MEDIUM,
    enabled: !!keyId
  }))
  
  return useAtomValue(apiKeyUsageAtom)
}

// API Key Mutation Hooks
export function useCreateApiKey() {
  const createApiKeyAtom = atomWithMutation(() => ({
    mutationFn: ({ keyData, userId }: { keyData: CreateApiKeyRequest; userId: string }) => 
      apiClient.createApiKey(keyData, userId),
    onSuccess: () => {
      // Invalidate API keys list cache
      // This would be handled by the query client in a real implementation
    }
  }))
  
  return useAtom(createApiKeyAtom)
}

export function useUpdateApiKey() {
  const updateApiKeyAtom = atomWithMutation(() => ({
    mutationFn: (keyData: UpdateApiKeyRequest) => apiClient.updateApiKey(keyData),
    onSuccess: (data) => {
      // Invalidate specific API key and API keys list cache
      // This would be handled by the query client in a real implementation
    }
  }))
  
  return useAtom(updateApiKeyAtom)
}

export function useRegenerateApiKey() {
  const regenerateApiKeyAtom = atomWithMutation(() => ({
    mutationFn: (keyId: string) => apiClient.regenerateApiKey(keyId),
    onSuccess: (data) => {
      // Invalidate specific API key cache
      // This would be handled by the query client in a real implementation
    }
  }))
  
  return useAtom(regenerateApiKeyAtom)
}

export function useRevokeApiKey() {
  const revokeApiKeyAtom = atomWithMutation(() => ({
    mutationFn: (keyId: string) => apiClient.revokeApiKey(keyId),
    onSuccess: (data) => {
      // Invalidate API keys list and specific key cache
      // This would be handled by the query client in a real implementation
    }
  }))
  
  return useAtom(revokeApiKeyAtom)
}

export function useDeleteApiKey() {
  const deleteApiKeyAtom = atomWithMutation(() => ({
    mutationFn: (keyId: string) => apiClient.deleteApiKey(keyId),
    onSuccess: () => {
      // Invalidate API keys list cache
      // This would be handled by the query client in a real implementation
    }
  }))
  
  return useAtom(deleteApiKeyAtom)
}

// Analytics Hooks
export function useUsageLogs(filters: AnalyticsFilters, limit = 100) {
  const usageLogsAtom = atomWithQuery(() => ({
    queryKey: queryKeys.analytics.usageLogs(filters, limit),
    queryFn: () => apiClient.getUsageLogs(filters, limit),
    staleTime: CACHE_CONFIG.STALE_TIME.SHORT,
    gcTime: CACHE_CONFIG.GC_TIME.SHORT
  }))
  
  return useAtomValue(usageLogsAtom)
}

export function useUsageMetrics(query: AnalyticsQuery) {
  const usageMetricsAtom = atomWithQuery(() => ({
    queryKey: queryKeys.analytics.usageMetrics(query),
    queryFn: () => apiClient.getUsageMetrics(query),
    staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM,
    gcTime: CACHE_CONFIG.GC_TIME.MEDIUM
  }))
  
  return useAtomValue(usageMetricsAtom)
}

export function useOllamaMetrics(timeRange: TimeRange) {
  const ollamaMetricsAtom = atomWithQuery(() => ({
    queryKey: queryKeys.analytics.ollamaMetrics(timeRange),
    queryFn: () => apiClient.getOllamaMetrics(timeRange),
    staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM,
    gcTime: CACHE_CONFIG.GC_TIME.MEDIUM
  }))
  
  return useAtomValue(ollamaMetricsAtom)
}

export function useSystemMetrics(timeRange: TimeRange) {
  const systemMetricsAtom = atomWithQuery(() => ({
    queryKey: queryKeys.analytics.systemMetrics(timeRange),
    queryFn: () => apiClient.getSystemMetrics(timeRange),
    staleTime: CACHE_CONFIG.STALE_TIME.SHORT,
    gcTime: CACHE_CONFIG.GC_TIME.SHORT
  }))
  
  return useAtomValue(systemMetricsAtom)
}

export function useTrendData(
  metric: string, 
  timeRange: TimeRange, 
  granularity: 'hour' | 'day' | 'week' = 'day'
) {
  const trendDataAtom = atomWithQuery(() => ({
    queryKey: queryKeys.analytics.trendData(metric, timeRange, granularity),
    queryFn: () => apiClient.getTrendData(metric, timeRange, granularity),
    staleTime: CACHE_CONFIG.STALE_TIME.MEDIUM,
    gcTime: CACHE_CONFIG.GC_TIME.MEDIUM
  }))
  
  return useAtomValue(trendDataAtom)
}

// Optimistic update utilities
export interface OptimisticUpdateConfig<TData, TVariables> {
  queryKey: QueryKey
  updater: (oldData: TData | undefined, variables: TVariables) => TData
  rollback?: (oldData: TData | undefined, error: Error) => TData
}

export function createOptimisticUpdate<TData, TVariables>(
  config: OptimisticUpdateConfig<TData, TVariables>
) {
  return {
    onMutate: async (variables: TVariables) => {
      // Cancel any outgoing refetches
      // This would be handled by the query client in a real implementation
      
      // Snapshot the previous value
      const previousData = undefined // Get from query client cache
      
      // Optimistically update to the new value
      // This would be handled by the query client in a real implementation
      
      return { previousData }
    },
    onError: (error: Error, variables: TVariables, context: any) => {
      // Rollback to previous data on error
      if (context?.previousData && config.rollback) {
        // This would be handled by the query client in a real implementation
      }
    },
    onSettled: () => {
      // Always refetch after error or success
      // This would be handled by the query client in a real implementation
    }
  }
}

// Data synchronization utilities
export function useSyncData() {
  const syncAtom = atom(
    null,
    async (get, set) => {
      try {
        // Sync critical data
        // This would invalidate and refetch important queries
        console.log('Syncing data...')
      } catch (error) {
        console.error('Data sync failed:', error)
      }
    }
  )
  
  return useSetAtom(syncAtom)
}
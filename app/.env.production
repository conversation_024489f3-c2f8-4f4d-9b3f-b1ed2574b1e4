# Production Environment Configuration
NODE_ENV=production
VITE_APP_TITLE=Home Server Admin Panel
VITE_BUILD_VERSION=1.0.0

# API Configuration (Update with production URLs)
VITE_API_BASE_URL=https://your-api-domain.com
VITE_WS_URL=wss://your-api-domain.com

# Authentication
VITE_AUTH_PROVIDER=better-auth
VITE_SESSION_TIMEOUT=3600000

# Feature Flags
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_DEBUG_MODE=false

# Performance Monitoring
VITE_PERFORMANCE_SAMPLE_RATE=0.1
VITE_ERROR_REPORTING_ENDPOINT=https://your-error-reporting-service.com/api/errors

# Production Settings
VITE_MOCK_API=false
VITE_ENABLE_DEVTOOLS=false
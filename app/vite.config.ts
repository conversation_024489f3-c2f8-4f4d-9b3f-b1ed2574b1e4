import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), '')

  const isProduction = mode === 'production'
  const isStaging = mode === 'staging'
  const isDevelopment = mode === 'development'

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    server: {
      port: 5173,
      host: true,
      open: true,
      allowedHosts: ['home-server.kvmanas.com'],
    },
    build: {
      target: 'es2022',
      outDir: 'dist',
      sourcemap: isDevelopment || isStaging,
      minify: isProduction ? 'esbuild' : false,
      cssMinify: isProduction,
      reportCompressedSize: isProduction,
      chunkSizeWarningLimit: isProduction ? 500 : 1000,
      rollupOptions: {
        output: {
          manualChunks: {
            // Core React
            'react-vendor': ['react', 'react-dom'],
            // Router
            'router': ['react-router-dom'],
            // UI libraries
            'ui-vendor': [
              '@radix-ui/react-dialog',
              '@radix-ui/react-dropdown-menu',
              '@radix-ui/react-select',
              'lucide-react'
            ],
            // State management
            'state-vendor': ['jotai', '@tanstack/react-query'],
            // Charts (will be lazy loaded but still chunked)
            'charts-vendor': ['recharts'],
            // Utilities
            'utils-vendor': ['axios', 'date-fns', 'clsx', 'tailwind-merge'],
            // Authentication
            'auth-vendor': ['better-auth'],
          },
          // Optimize chunk names for production
          chunkFileNames: isProduction
            ? 'assets/js/[name]-[hash].js'
            : 'chunks/[name]-[hash].js',
          entryFileNames: isProduction
            ? 'assets/js/[name]-[hash].js'
            : 'assets/[name]-[hash].js',
          assetFileNames: isProduction
            ? 'assets/[ext]/[name]-[hash].[ext]'
            : 'assets/[name]-[hash].[ext]',
        },
      },
    },
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'jotai',
        '@tanstack/react-query',
        'axios',
        'clsx',
        'date-fns',
      ],
      exclude: [
        'recharts', // Lazy loaded
        'html2canvas', // Lazy loaded
        'jspdf', // Lazy loaded
      ],
    },
    define: {
      // Inject build-time environment variables
      __BUILD_VERSION__: JSON.stringify(env['VITE_BUILD_VERSION'] || '1.0.0'),
      __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
      __COMMIT_HASH__: JSON.stringify(process.env['COMMIT_HASH'] || 'unknown'),
    },
    // Environment-specific configurations
    ...(isProduction && {
      esbuild: {
        drop: ['console', 'debugger'], // Remove console.log and debugger in production
      },
    }),
  }
})

# Home Server Admin Panel

A modern React 19 application built with TypeScript 5, providing comprehensive administrative capabilities and user self-service functionality for managing API access and monitoring usage.

## 🚀 Tech Stack

- **React 19** - Latest React with concurrent features
- **TypeScript 5** - Advanced type safety with strict configuration
- **Vite 5+** - Fast development and optimized builds
- **Bun** - Fast package manager and runtime
- **<PERSON><PERSON>** - Atomic state management
- **better-auth** - Framework-agnostic authentication
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Accessible component library
- **Radix UI** - Primitive components
- **Recharts** - Data visualization
- **Vitest** - Unit testing framework
- **Playwright** - End-to-end testing
- **ESLint + Prettier** - Code quality and formatting

## 📁 Project Structure

```
app3/
├── src/
│   ├── components/           # Reusable UI components
│   ├── lib/                 # Utility libraries
│   ├── test/                # Test setup and utilities
│   └── ...
├── tests/
│   └── e2e/                 # Playwright e2e tests
├── dist/                    # Production build output
└── config files             # Vite, TypeScript, ESLint, etc.
```

## 🛠️ Development

### Prerequisites

- [Bun](https://bun.sh/) - Fast JavaScript runtime and package manager

### Getting Started

1. Install dependencies:
   ```bash
   bun install
   ```

2. Start development server:
   ```bash
   bun run dev
   ```

3. Open [http://localhost:3000](http://localhost:3000) in your browser

### Available Scripts

- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run preview` - Preview production build
- `bun run lint` - Run ESLint
- `bun run lint:fix` - Fix ESLint issues
- `bun run format` - Format code with Prettier
- `bun run format:check` - Check code formatting
- `bun run test` - Run unit tests
- `bun run test:watch` - Run tests in watch mode
- `bun run test:ui` - Run tests with UI
- `bun run test:coverage` - Run tests with coverage
- `bun run e2e` - Run e2e tests
- `bun run e2e:ui` - Run e2e tests with UI
- `bun run e2e:install` - Install Playwright browsers

## 🧪 Testing

### Unit Tests (Vitest)

Unit tests are located in `src/**/*.{test,spec}.{ts,tsx}` files.

```bash
bun run test        # Run once
bun run test:watch  # Watch mode
bun run test:ui     # Interactive UI
```

### E2E Tests (Playwright)

E2E tests are located in `tests/e2e/` directory.

```bash
bun run e2e         # Run e2e tests
bun run e2e:ui      # Interactive mode
```

## 📝 Code Quality

The project uses ESLint and Prettier for code quality and formatting:

- **ESLint**: Configured with TypeScript, React, and strict rules
- **Prettier**: Consistent code formatting with Tailwind CSS plugin
- **TypeScript**: Strict mode with advanced type checking

## 🎨 Styling

- **Tailwind CSS**: Utility-first CSS framework
- **CSS Custom Properties**: Design tokens for theming
- **shadcn/ui**: Pre-built accessible components
- **Responsive Design**: Mobile-first approach

## 🔧 Configuration

### TypeScript

- Strict mode enabled
- Advanced type checking options
- Path aliases configured (`@/` → `src/`)

### Vite

- React plugin configured
- Path aliases
- Optimized build settings
- Development server on port 3000

### Tailwind CSS

- Custom design tokens
- shadcn/ui integration
- Form and typography plugins

## 📦 Dependencies

### Core Dependencies

- React 19 + React DOM
- TypeScript 5
- Jotai (state management)
- better-auth (authentication)
- React Router DOM
- Tailwind CSS + PostCSS
- shadcn/ui components
- Radix UI primitives
- Recharts (data visualization)

### Development Dependencies

- Vite 5+
- Vitest + Testing Library
- Playwright
- ESLint + Prettier
- TypeScript ESLint

## 🚀 Deployment

Build the project for production:

```bash
bun run build
```

The built files will be in the `dist/` directory, ready for deployment to any static hosting service.

## 📋 Next Steps

This project setup provides the foundation for building the Home Server Admin Panel. The next tasks involve:

1. UI Foundation and Design System Setup
2. Authentication System Implementation
3. State Management Architecture with Jotai
4. Admin Dashboard Implementation
5. User Dashboard Implementation
6. Data Visualization and Charts
7. API Integration and Data Management
8. Testing Implementation
9. Performance Optimization

Refer to the implementation plan in `.kiro/specs/home-server-admin-panel/tasks.md` for detailed task breakdown.
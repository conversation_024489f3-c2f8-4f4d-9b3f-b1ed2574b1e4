name: Build and Deploy

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run type check
        run: bun run typecheck

      - name: Run linting
        run: bun run lint

      - name: Run unit tests
        run: bun run test

      - name: Install Playwright browsers
        run: bun run e2e:install

      - name: Run E2E tests
        run: bun run e2e

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: test-results
          path: |
            test-results/
            playwright-report/

  build-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build for staging
        run: bun run build:staging
        env:
          VITE_API_BASE_URL: ${{ secrets.STAGING_API_URL }}
          VITE_WS_URL: ${{ secrets.STAGING_WS_URL }}
          VITE_BUILD_VERSION: ${{ github.sha }}
          COMMIT_HASH: ${{ github.sha }}

      - name: Analyze bundle
        run: bun run build:analyze:staging

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: staging-build
          path: dist/

      - name: Deploy to staging
        if: success()
        run: |
          # Add your staging deployment commands here
          echo "Deploying to staging environment"
          # Example: rsync, AWS S3, Netlify, etc.

  build-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build for production
        run: bun run build:production
        env:
          VITE_API_BASE_URL: ${{ secrets.PRODUCTION_API_URL }}
          VITE_WS_URL: ${{ secrets.PRODUCTION_WS_URL }}
          VITE_BUILD_VERSION: ${{ github.sha }}
          COMMIT_HASH: ${{ github.sha }}

      - name: Analyze bundle
        run: bun run build:analyze:production

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: production-build
          path: dist/

      - name: Build Docker image
        run: |
          docker build -t home-server-admin:${{ github.sha }} .
          docker tag home-server-admin:${{ github.sha }} home-server-admin:latest

      - name: Deploy to production
        if: success()
        run: |
          # Add your production deployment commands here
          echo "Deploying to production environment"
          # Example: Docker registry push, Kubernetes deployment, etc.

  security-scan:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run security audit
        run: |
          bun audit
          # Add additional security scanning tools here

  performance-test:
    needs: build-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: staging-build
          path: dist/

      - name: Setup Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: Run Lighthouse CI
        run: |
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  notify:
    needs: [build-staging, build-production]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Notify deployment status
        run: |
          # Add notification logic here (Slack, Discord, email, etc.)
          echo "Deployment completed with status: ${{ needs.build-production.result || needs.build-staging.result }}"
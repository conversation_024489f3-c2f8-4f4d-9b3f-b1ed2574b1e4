# Design Document

## Overview

The Home Server Admin Panel is a modern React 19 application that provides comprehensive administrative capabilities and user self-service functionality for managing API access and monitoring usage. The system features a dual-interface architecture with distinct admin and user dashboards, built using cutting-edge technologies including TypeScript 5, Vite 5+, <PERSON><PERSON> for atomic state management, and shadcn/ui components for a consistent design system.

The application integrates with better-auth for secure authentication flows and provides real-time monitoring of Ollama API usage with detailed analytics and performance metrics. The architecture emphasizes modularity, type safety, and optimal developer experience while maintaining high performance and accessibility standards.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend (React 19 + TypeScript 5)"
        A[Admin Dashboard] --> C[Shared Components]
        B[User Dashboard] --> C
        C --> D[Authentication Layer]
        C --> E[State Management - Jotai]
        C --> F[API Client Layer]
    end
    
    subgraph "Authentication"
        D --> G[better-auth Client]
        G --> H[better-auth Server]
    end
    
    subgraph "Backend Services"
        F --> I[User Management API]
        F --> J[API Key Management API]
        F --> K[Usage Analytics API]
        F --> L[Ollama API Proxy]
    end
    
    subgraph "Data Layer"
        H --> M[User Database]
        I --> M
        J --> N[API Keys Database]
        K --> O[Usage Logs Database]
        L --> P[Ollama Service]
    end
```

### Application Structure

```
app3/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── ui/              # shadcn/ui components
│   │   ├── forms/           # Form components
│   │   ├── charts/          # Data visualization components
│   │   └── layout/          # Layout components
│   ├── pages/               # Page components
│   │   ├── admin/           # Admin dashboard pages
│   │   ├── user/            # User dashboard pages
│   │   └── auth/            # Authentication pages
│   ├── lib/                 # Utility libraries
│   │   ├── auth.ts          # better-auth configuration
│   │   ├── api.ts           # API client setup
│   │   └── utils.ts         # General utilities
│   ├── stores/              # Jotai atoms and state management
│   │   ├── auth.ts          # Authentication state
│   │   ├── users.ts         # User management state
│   │   ├── apiKeys.ts       # API key management state
│   │   └── analytics.ts     # Analytics and usage data state
│   ├── hooks/               # Custom React hooks
│   ├── types/               # TypeScript type definitions
│   └── styles/              # Global styles and Tailwind config
├── public/                  # Static assets
├── tests/                   # Test files
│   ├── unit/                # Vitest unit tests
│   └── e2e/                 # Playwright e2e tests
└── config files             # Vite, TypeScript, ESLint, etc.
```

### Technology Stack Integration

- **React 19**: Leverages latest features including concurrent rendering and improved hydration
- **TypeScript 5**: Advanced type system with decorators, const assertions, and improved inference
- **Vite 5+**: Fast development server with HMR and optimized production builds
- **Jotai**: Atomic state management for fine-grained reactivity and optimal re-renders
- **shadcn/ui**: Accessible, customizable components built on Radix UI primitives
- **Tailwind CSS**: Utility-first CSS framework for rapid UI development
- **better-auth**: Framework-agnostic authentication with TypeScript support

## Components and Interfaces

### Core Component Architecture

#### Authentication Components
```typescript
// Auth state atoms
const sessionAtom = atom<Session | null>(null)
const isAuthenticatedAtom = atom((get) => get(sessionAtom) !== null)
const userRoleAtom = atom((get) => get(sessionAtom)?.user?.role || 'user')

// Auth components
interface AuthGuardProps {
  children: React.ReactNode
  requiredRole?: 'admin' | 'user'
  fallback?: React.ReactNode
}

interface LoginFormProps {
  onSuccess?: (session: Session) => void
  redirectTo?: string
}
```

#### Admin Dashboard Components
```typescript
// User management atoms
const usersAtom = atom<User[]>([])
const selectedUserAtom = atom<User | null>(null)
const userFiltersAtom = atom({
  status: 'all' as 'all' | 'approved' | 'pending' | 'revoked',
  search: '',
  sortBy: 'createdAt' as keyof User,
  sortOrder: 'desc' as 'asc' | 'desc'
})

// Admin components
interface UserManagementTableProps {
  users: User[]
  onApprove: (userId: string) => void
  onRevoke: (userId: string) => void
  onViewDetails: (user: User) => void
}

interface ApiUsageDashboardProps {
  timeRange: TimeRange
  filters: UsageFilters
}
```

#### User Dashboard Components
```typescript
// API key management atoms
const apiKeysAtom = atom<ApiKey[]>([])
const createApiKeyAtom = atom(
  null,
  async (get, set, keyData: CreateApiKeyRequest) => {
    const newKey = await apiClient.createApiKey(keyData)
    set(apiKeysAtom, (prev) => [...prev, newKey])
    return newKey
  }
)

// User components
interface ApiKeyManagerProps {
  keys: ApiKey[]
  onCreateKey: (data: CreateApiKeyRequest) => Promise<ApiKey>
  onRegenerateKey: (keyId: string) => Promise<ApiKey>
  onDeleteKey: (keyId: string) => Promise<void>
}

interface UsageMetricsProps {
  userId: string
  timeRange: TimeRange
}
```

### Shared UI Components (shadcn/ui)

#### Core UI Components
- **Button**: Primary, secondary, destructive, ghost variants
- **Input**: Text, email, password with validation states
- **Select**: Dropdown selections with search capability
- **Table**: Sortable, filterable data tables
- **Card**: Content containers with headers and actions
- **Dialog**: Modal dialogs for forms and confirmations
- **Toast**: Notification system for user feedback
- **Badge**: Status indicators and labels
- **Tabs**: Navigation between related content
- **Form**: Form handling with validation

#### Data Visualization Components
```typescript
interface ChartProps {
  data: ChartData[]
  type: 'line' | 'bar' | 'pie' | 'area'
  config: ChartConfig
  className?: string
}

interface MetricCardProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: 'increase' | 'decrease'
    period: string
  }
  icon?: React.ReactNode
}
```

## Data Models

### User Management Models
```typescript
interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'user'
  status: 'pending' | 'approved' | 'revoked'
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date
  apiAccessLevel: 'none' | 'basic' | 'premium'
}

interface UserSession {
  id: string
  userId: string
  token: string
  expiresAt: Date
  createdAt: Date
  ipAddress?: string
  userAgent?: string
}
```

### API Key Management Models
```typescript
interface ApiKey {
  id: string
  userId: string
  name: string
  description?: string
  key: string // Only shown once during creation
  keyHash: string // Stored hash for verification
  status: 'active' | 'revoked'
  permissions: ApiPermission[]
  rateLimit: {
    requestsPerMinute: number
    requestsPerHour: number
    requestsPerDay: number
  }
  createdAt: Date
  updatedAt: Date
  lastUsedAt?: Date
  expiresAt?: Date
}

interface ApiPermission {
  resource: string
  actions: string[]
}

interface CreateApiKeyRequest {
  name: string
  description?: string
  permissions: ApiPermission[]
  rateLimit?: Partial<ApiKey['rateLimit']>
  expiresAt?: Date
}
```

### Usage Analytics Models
```typescript
interface ApiUsageLog {
  id: string
  userId: string
  apiKeyId: string
  endpoint: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  statusCode: number
  responseTime: number
  requestSize: number
  responseSize: number
  timestamp: Date
  ipAddress?: string
  userAgent?: string
  errorMessage?: string
}

interface UsageMetrics {
  userId: string
  apiKeyId?: string
  period: {
    start: Date
    end: Date
  }
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  totalDataTransferred: number
  topEndpoints: Array<{
    endpoint: string
    count: number
    averageResponseTime: number
  }>
  errorBreakdown: Array<{
    statusCode: number
    count: number
    percentage: number
  }>
}

interface OllamaPerformanceMetrics {
  modelName: string
  totalRequests: number
  averageResponseTime: number
  tokensPerSecond: number
  successRate: number
  errorRate: number
  peakUsageTime: Date
  resourceUtilization: {
    cpu: number
    memory: number
    gpu?: number
  }
}
```

## Error Handling

### Error Management Strategy
```typescript
// Error types
interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
  timestamp: Date
}

interface ValidationError extends ApiError {
  field: string
  value: any
}

// Error atoms
const errorAtom = atom<ApiError | null>(null)
const validationErrorsAtom = atom<ValidationError[]>([])

// Error handling utilities
const withErrorHandling = <T extends any[], R>(
  fn: (...args: T) => Promise<R>
) => {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args)
    } catch (error) {
      // Handle and transform errors
      throw transformError(error)
    }
  }
}
```

### Global Error Boundary
```typescript
interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

class GlobalErrorBoundary extends Component<
  PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  // Error boundary implementation with logging and recovery
}
```

## Testing Strategy

### Unit Testing with Vitest
```typescript
// Atom testing utilities
const createTestStore = () => createStore()

describe('User Management Atoms', () => {
  it('should update users list when new user is added', async () => {
    const store = createTestStore()
    const initialUsers = store.get(usersAtom)
    
    await store.set(addUserAtom, mockUser)
    
    const updatedUsers = store.get(usersAtom)
    expect(updatedUsers).toHaveLength(initialUsers.length + 1)
  })
})

// Component testing
describe('UserManagementTable', () => {
  it('should render users and handle approval', async () => {
    const mockOnApprove = vi.fn()
    render(
      <UserManagementTable 
        users={mockUsers} 
        onApprove={mockOnApprove}
        onRevoke={vi.fn()}
        onViewDetails={vi.fn()}
      />
    )
    
    await user.click(screen.getByRole('button', { name: /approve/i }))
    expect(mockOnApprove).toHaveBeenCalledWith(mockUsers[0].id)
  })
})
```

### E2E Testing with Playwright
```typescript
// Authentication flow tests
test('admin can login and access user management', async ({ page }) => {
  await page.goto('/login')
  await page.fill('[data-testid="email"]', '<EMAIL>')
  await page.fill('[data-testid="password"]', 'password')
  await page.click('[data-testid="login-button"]')
  
  await expect(page).toHaveURL('/admin/dashboard')
  await expect(page.locator('[data-testid="user-management"]')).toBeVisible()
})

// API key management tests
test('user can create and manage API keys', async ({ page }) => {
  await loginAsUser(page)
  await page.goto('/dashboard/api-keys')
  
  await page.click('[data-testid="create-key-button"]')
  await page.fill('[data-testid="key-name"]', 'Test Key')
  await page.fill('[data-testid="key-description"]', 'Test Description')
  await page.click('[data-testid="create-button"]')
  
  await expect(page.locator('[data-testid="api-key-list"]')).toContainText('Test Key')
})
```

## Performance Optimizations

### State Management Optimizations
```typescript
// Selective subscriptions with Jotai
const userListAtom = atom<User[]>([])
const filteredUsersAtom = atom((get) => {
  const users = get(userListAtom)
  const filters = get(userFiltersAtom)
  return filterUsers(users, filters)
})

// Async data fetching with caching
const userQueryAtom = atomWithQuery((get) => ({
  queryKey: ['users', get(userFiltersAtom)],
  queryFn: () => fetchUsers(get(userFiltersAtom)),
  staleTime: 5 * 60 * 1000, // 5 minutes
}))
```

### Component Optimizations
```typescript
// Memoized components for expensive renders
const UserTable = memo(({ users, onAction }: UserTableProps) => {
  const memoizedUsers = useMemo(
    () => users.map(user => ({ ...user, actions: createActions(user) })),
    [users]
  )
  
  return <Table data={memoizedUsers} />
})

// Virtual scrolling for large datasets
const VirtualizedUserList = ({ users }: { users: User[] }) => {
  return (
    <FixedSizeList
      height={600}
      itemCount={users.length}
      itemSize={60}
      itemData={users}
    >
      {UserRow}
    </FixedSizeList>
  )
}
```

### Bundle Optimization
```typescript
// Code splitting by route
const AdminDashboard = lazy(() => import('./pages/admin/Dashboard'))
const UserDashboard = lazy(() => import('./pages/user/Dashboard'))

// Dynamic imports for heavy dependencies
const loadChartLibrary = () => import('recharts')
```

## Security Considerations

### Authentication Security
- JWT tokens with short expiration times
- Refresh token rotation
- Session invalidation on suspicious activity
- Rate limiting on authentication endpoints

### API Security
- API key hashing and secure storage
- Request signing and validation
- CORS configuration for allowed origins
- Input validation and sanitization

### Data Protection
- Sensitive data encryption at rest
- Secure transmission with HTTPS
- PII data masking in logs
- Regular security audits and updates

This design provides a comprehensive foundation for building a modern, scalable, and maintainable admin panel that meets all the specified requirements while following current best practices in React development.
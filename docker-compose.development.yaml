version: '3.9'

services:
  nginx:
    image: nginx:alpine
    volumes:
      - ./nginx/nginx.dev.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - frontend
      - backend
    networks:
      - cloudflare-tunnel
      - app-network
  frontend:
    build:
      context: .
      dockerfile: app/Dockerfile
      target: development
    volumes:
      - ./app/src:/apps/app/src
      - ./app/public:/apps/app/public
      - ./app/index.html:/apps/app/index.html
      - ./app/vite.config.js:/apps/app/vite.config.js
      - frontend_node_modules:/apps/app/node_modules
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=https://home-server.kvmanas.com/api
      - VITE_WS_URL=wss://home-server.kvmanas.com/api
    networks:
      - app-network

  backend:
    build:
      context: .
      dockerfile: server/Dockerfile
      target: development
    ports:
      - "4000:4000"
    volumes:
      - ./server/src:/apps/server/src
      - backend_node_modules:/apps/server/node_modules
    environment:
      - NODE_ENV=development
      - APP_PORT=4000
      - MONGO_URI=${MONGO_URI}
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
      - ADMIN_NAME=${ADMIN_NAME}
    networks:
      - app-network
      - mongo

volumes:
  frontend_node_modules:
  backend_node_modules:

networks:
  app-network:
    driver: bridge
  cloudflare-tunnel:
    external: true
  mongo:
    external: true
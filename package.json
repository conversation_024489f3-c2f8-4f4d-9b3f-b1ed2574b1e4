{"name": "home-server", "private": true, "workspaces": ["app", "server"], "scripts": {"dev": "bun run --parallel dev:frontend dev:backend", "dev:frontend": "bun --cwd app run dev", "dev:backend": "bun --cwd server run dev", "build": "bun run build:frontend && bun run build:backend", "build:frontend": "bun --cwd app run build", "build:backend": "bun --cwd server run build", "docker:up": "docker-compose up --build", "docker:down": "docker-compose down"}, "devDependencies": {"@types/node": "^20.0.0"}}